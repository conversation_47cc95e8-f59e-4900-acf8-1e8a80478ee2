'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_INBOUND_STATUS', {
      uuid: {
        type: Sequelize.STRING(100),
        primaryKey: true,
        allowNull: false
      },
      submissionUid: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      longId: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      internalId: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      typeName: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      typeVersionName: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      issuerTin: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      issuerName: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      receiverId: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      receiverName: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      dateTimeReceived: {
        type: Sequelize.DATE,
        allowNull: true
      },
      dateTimeValidated: {
        type: Sequelize.DATE,
        allowNull: true
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      documentStatusReason: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      cancelDateTime: {
        type: Sequelize.DATE,
        allowNull: true
      },
      rejectRequestDateTime: {
        type: Sequelize.DATE,
        allowNull: true
      },
      createdByUserId: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      dateTimeIssued: {
        type: Sequelize.DATE,
        allowNull: true
      },
      totalExcludingTax: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: true
      },
      totalDiscount: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: true
      },
      totalNetAmount: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: true
      },
      totalPayableAmount: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: true
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_INBOUND_STATUS');
  }
}; 