'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_SFTP_CONFIG', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      host: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      port: {
        type: Sequelize.STRING(10),
        defaultValue: '22'
      },
      username: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      password: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      root_path: {
        type: Sequelize.STRING(255),
        defaultValue: '/eInvoiceFTP'
      },
      incoming_manual_template: {
        type: Sequelize.STRING(255)
      },
      incoming_schedule_template: {
        type: Sequelize.STRING(255)
      },
      outgoing_manual_template: {
        type: Sequelize.STRING(255)
      },
      outgoing_schedule_template: {
        type: Sequelize.STRING(255)
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.STRING(50),
        allowNull: false
      }
    });

    // Add unique index for host and username combination
    await queryInterface.addIndex('WP_SFTP_CONFIG', ['host', 'username'], {
      unique: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_SFTP_CONFIG');
  }
}; 