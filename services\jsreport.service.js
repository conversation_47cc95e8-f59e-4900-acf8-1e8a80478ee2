const jsreport = require('jsreport-core')();
const fs = require('fs');
const path = require('path');
const os = require('os');

const initJsReport = async () => {
  try {
    // Create jsreport temp directory if it doesn't exist
    const tempDir = path.join(os.tmpdir(), 'jsreport');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Set the temp directory explicitly
    jsreport.options.tempDirectory = tempDir;

    jsreport.use(require('jsreport-jsrender')());
    jsreport.use(require('jsreport-chrome-pdf')({
      launchOptions: {
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu'
        ],
        headless: 'new'
      },
      strategy: 'dedicated-process',
      timeout: 60000
    }));

    await jsreport.init();
    console.log('jsreport initialized successfully');
    
    // Add a close method to the jsreport instance
    jsreport.close = async () => {
      try {
        // Use the original close method from jsreport-core
        await jsreport.originalClose();
        console.log('jsreport closed successfully');
      } catch (error) {
        console.error('Error closing jsreport:', error);
        throw error;
      }
    };
    
    // Store the original close method
    jsreport.originalClose = jsreport.close;
    
    return jsreport;
  } catch (error) {
    console.error('Error initializing jsreport:', error);
    throw error;
  }
};

module.exports = {
  jsreport,
  initJsReport
}; 