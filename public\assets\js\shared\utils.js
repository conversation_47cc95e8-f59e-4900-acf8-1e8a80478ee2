// Utility functions for formatting and data handling
const Utils = {
    formatCurrency: (amount, currency = 'MYR') => {
        return new Intl.NumberFormat('en-MY', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },

    formatDate: (date) => {
        return moment(date).format('DD-MM-YYYY');
    },

    formatDateTime: (date) => {
        return moment(date).format('DD-MM-YYYY HH:mm:ss');
    },

    formatRelativeTime: (date) => {
        return moment(date).fromNow();
    },

    // Clean postcode for LHDN compliance (frontend version)
    cleanPostcode: (postcode) => {
        if (!postcode) return '';

        const original = postcode;
        // Remove dashes, spaces, and any non-alphanumeric characters
        // Limit to 5 characters as per LHDN requirement
        const cleaned = postcode.replace(/[-\s\W]/g, '').substring(0, 5);

        // Log if postcode was modified for debugging
        if (original !== cleaned && original.length > 0) {
            console.log(`Frontend - Postcode cleaned: "${original}" -> "${cleaned}"`);
        }

        return cleaned;
    },

    // Validate postcode format
    isValidPostcode: (postcode) => {
        if (!postcode) return false;
        const cleaned = Utils.cleanPostcode(postcode);
        // Malaysian postcodes should be 5 digits
        return /^\d{5}$/.test(cleaned);
    }
};

// Export the utils
window.Utils = Utils;
