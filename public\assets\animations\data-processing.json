{"v": "5.5.7", "fr": 30, "ip": 0, "op": 90, "w": 200, "h": 200, "nm": "Data Processing Animation", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "h": 0}, {"t": 90, "s": [360], "h": 0}]}, "p": {"a": 0, "k": [100, 100, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [80, 80]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "st", "c": {"a": 0, "k": [0.4, 0.3, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 8}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "v": {"a": 0, "k": 20}}, {"n": "g", "v": {"a": 0, "k": 20}}]}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}], "nm": "Circle"}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Dot 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"t": 0, "s": [100, 70, 0], "h": 0}, {"t": 15, "s": [120, 80, 0], "h": 0}, {"t": 30, "s": [100, 90, 0], "h": 0}, {"t": 45, "s": [80, 80, 0], "h": 0}, {"t": 60, "s": [100, 70, 0], "h": 0}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [12, 12]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.3, 1, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}], "nm": "Dot"}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Dot 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"t": 0, "s": [120, 100, 0], "h": 0}, {"t": 15, "s": [100, 120, 0], "h": 0}, {"t": 30, "s": [80, 100, 0], "h": 0}, {"t": 45, "s": [100, 80, 0], "h": 0}, {"t": 60, "s": [120, 100, 0], "h": 0}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [12, 12]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.3, 1, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}], "nm": "Dot"}], "ip": 0, "op": 90, "st": 0, "bm": 0}]}