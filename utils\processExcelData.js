const fs = require('fs');
const path = require('path');
const { logRawTo<PERSON>son, logLhdnMapping } = require('./excelLogger');
const { mapToLHDNFormat } = require('./lhdnMapper');

// Constants for document types
const DOCUMENT_TYPES = {
  '01': 'Invoice',
  '02': 'Credit Note',
  '03': 'Debit Note',
  '04': 'Refund Note',
  '11': 'Self-billed Invoice',
  '12': 'Self-billed Credit Note',
  '13': 'Self-billed Debit Note',
  '14': 'Self-billed Refund Note'
};

// Function to validate file name format
function isValidFileName(fileName) {
  // Remove file extension if present
  const baseFileName = fileName.replace(/\.(xls|xlsx)$/, '');
  
  // Regular expression to match the correct file name format
  // Examples of valid formats:
  // 01_ARINV118942_eInvoice_20241223095718
  // 01_ARINV118943SCHEDULE_eInvoice_20241223095727
  const regex = /^(0[1-4]|1[1-4])_(?:AR)?(?:INV|CN|DN|RN)\d+(?:SCHEDULE)?_eInvoice_\d{14}$/;
  
  if (!regex.test(baseFileName)) {
    console.log(`Invalid file name format: ${fileName} (base: ${baseFileName})`);
    return false;
  }

  // Extract document type code
  const docType = baseFileName.substring(0, 2);
  const isValidType = docType in DOCUMENT_TYPES;
  
  if (!isValidType) {
    console.log(`Invalid document type: ${docType} in file: ${fileName}`);
  }
  
  return isValidType;
}

// Function to get document type from file name
function getDocumentType(fileName) {
  const docType = fileName.substring(0, 2);
  return DOCUMENT_TYPES[docType] || null;
}

// Helper function to get identifications from rows
function getIdentifications(rows) {
  return rows
    .filter(row => row && row.PartyIdentification_ID)
    .map(row => ({
      id: row.PartyIdentification_ID,
      schemeId: row.schemeId
    }));
}

const processExcelData = (rawData) => {
  const logger = {
    info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
    error: (msg, data) => console.error(`[ERROR] ${msg}`, data || '')
  };

  // Create a single log buffer instead of writing files
  let processLogs = {
    steps: [],
    identifications: [],
    documents: [],
    mappingResults: []
  };

  // Replace writeProcessLog with an in-memory function
  const addToProcessLog = (data, type) => {
    processLogs[type] = processLogs[type] || [];
    processLogs[type].push({
      timestamp: new Date().toISOString(),
      data
    });
  };

  try {
    // Log each processing step
    const logStep = (step, data) => {
      console.log(`\n=== ${step} ===`);
      console.log(JSON.stringify(data, null, 2));
      
      processLogs.steps.push({
        timestamp: new Date().toISOString(),
        step,
        data
      });
    };

    const descriptions = rawData[0];
    const fieldMappings = rawData[1];
    const dataRows = rawData.slice(2);

    // Log initial data
    console.log('\n=== Raw Data Structure ===');
    console.log('First Row (Descriptions):', JSON.stringify(descriptions, null, 2));
    console.log('Second Row (Field Mappings):', JSON.stringify(fieldMappings, null, 2));
    console.log('Sample Data Row:', JSON.stringify(dataRows[0], null, 2));

    logStep('Initial Data', {
      totalRows: dataRows.length,
      firstRow: dataRows[0]
    });

    // Write to file and console
    const rawDataStructure = {
      descriptions,
      fieldMappings,
      sampleDataRow: dataRows[0],
      emptyFields: Object.keys(dataRows[0])
        .filter(key => key.startsWith('__EMPTY'))
        .reduce((acc, key) => ({
          ...acc,
          [key]: dataRows[0][key]
        }), {}),
      rowSamples: {
        header: dataRows.find(row => row.__EMPTY === 'H'),
        line: dataRows.find(row => row.__EMPTY === 'L'),
        footer: dataRows.find(row => row.__EMPTY === 'F')
      }
    };

    addToProcessLog(rawDataStructure, 'raw_data_structure');
    
    // Log empty fields to console
    console.log('\n=== __EMPTY Fields Mapping ===');
    Object.keys(rawDataStructure.emptyFields).forEach(field => {
      console.log(`${field}: ${rawDataStructure.emptyFields[field]}`);
    });

    logger.info('Processing Excel file with rows:', dataRows.length);

    // Log row samples to console
    console.log('\n=== Row Type Samples ===');
    console.log('Header Row Structure:', JSON.stringify(rawDataStructure.rowSamples.header, null, 2));

    const documents = [];
    let currentDocument = null;
    let currentLineItems = [];

    // Function to create a new document from header row
    const createNewDocument = (headerRow, dataRows, currentIndex) => {
      // Find the footer row for tax scheme data
      const footerRow = dataRows.slice(currentIndex).find(row => row.__EMPTY === 'F');

      // Get the scheme ID rows for supplier from actual Excel data
      const supplierIdRows = [
        { 
          PartyIdentification_ID: headerRow.__EMPTY_16,
          //PartyIdentification_ID: 'C11428596090',
          schemeId: 'TIN'
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 1]?.__EMPTY_16,
          //PartyIdentification_ID: '200201023752',
          schemeId: 'BRN'
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 2]?.__EMPTY_16,
          schemeId: 'SST'
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 3]?.__EMPTY_16,
          schemeId: 'TTX'
        }
      ];

      // Get the scheme ID rows for buyer
      const buyerIdRows = [
        { 
          PartyIdentification_ID: headerRow.Buyer,
          schemeId: headerRow.__EMPTY_28
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 1]?.Buyer,
          schemeId: 'BRN'
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 2]?.Buyer,
          schemeId: 'SST'
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 3]?.Buyer,
          schemeId: 'TTX'
        }
      ];

      // Get the scheme ID rows for delivery
      const deliveryIdRows = [
        { 
          PartyIdentification_ID: headerRow.Delivery,
          schemeId: headerRow.__EMPTY_39
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 1]?.Delivery,
          schemeId: 'BRN'
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 2]?.Delivery,
          schemeId: 'SST'
        },
        { 
          PartyIdentification_ID: dataRows[currentIndex + 3]?.Delivery,
          schemeId: 'TTX'
        }
      ];

      console.log('Processing rows:', {
        supplier: supplierIdRows,
        buyer: buyerIdRows,
        delivery: deliveryIdRows
      });

      // Add detailed logging for document creation
      const documentLog = {
        timestamp: new Date().toISOString(),
        documentId: headerRow.Invoice,
        supplierIdRows,
        buyerIdRows,
        deliveryIdRows
      };
      processLogs.documents.push(documentLog);

      const currentDate = new Date();
      const formattedDate = currentDate.toISOString().split('T')[0];
      const formattedTime = currentDate.toISOString().split('T')[1].split('.')[0] + 'Z';

      const doc = {
        header: {
          invoiceNo: headerRow.Invoice,
          invoiceType: headerRow.__EMPTY_5,
          documentReference: {
            uuid: headerRow.__EMPTY_1,
            internalId: headerRow.__EMPTY_2,
            billingReference: headerRow.AdditionalDocumentReference,
            billingReferenceType: headerRow.__EMPTY_11
          },
          issueDate: [{ _: formattedDate }],
          issueTime: [{ _: formattedTime }],
          currency: headerRow.__EMPTY_6 || 'MYR',
          invoicePeriod: {
            startDate: headerRow.InvoicePeriod || null,
            endDate: headerRow.__EMPTY_9 || null,
            description: headerRow.__EMPTY_10 || 'NA'
          }
        },
        supplier: {
          id: headerRow.__EMPTY_16,
          additionalAccountID: headerRow.Supplier || 'NA',
          schemeAgencyName: headerRow.__EMPTY_13 || 'CertEx',
          industryClassificationCode: headerRow.__EMPTY_14,
          identifications: getIdentifications(supplierIdRows),
          name: headerRow.__EMPTY_25,
          address: {
            line: headerRow.__EMPTY_21,
            city: headerRow.__EMPTY_18,
            postcode: headerRow.__EMPTY_19,
            state: headerRow.__EMPTY_20,
            country: headerRow.__EMPTY_22,
            countryListID: headerRow.__EMPTY_23,
            countryListAgencyID: headerRow.__EMPTY_24
          },
          contact: {
            phone: headerRow.__EMPTY_26,
            email: headerRow.__EMPTY_27
          }
        },
        buyer: {
          id: headerRow.Buyer,
          identifications: getIdentifications(buyerIdRows),
          name: headerRow.__EMPTY_36,
          address: {
            line: headerRow.__EMPTY_32,
            city: headerRow.__EMPTY_29,
            postcode: headerRow.__EMPTY_30,
            state: headerRow.__EMPTY_31,
            country: headerRow.__EMPTY_33,
            countryListID: headerRow.__EMPTY_34 || 'ISO3166-1',
            countryListAgencyID: headerRow.__EMPTY_35 || '6'
          },
          contact: {
            phone: headerRow.__EMPTY_37,
            email: headerRow.__EMPTY_38
          }
        },
        delivery: {
          id: headerRow.Delivery,
          identifications: getIdentifications(deliveryIdRows),
          name: headerRow.__EMPTY_47,
          address: {
            line: headerRow.__EMPTY_43,
            city: headerRow.__EMPTY_40,
            postcode: headerRow.__EMPTY_41,
            state: headerRow.__EMPTY_42,
            country: headerRow.__EMPTY_44,
            countryListID: headerRow.__EMPTY_45 || 'ISO3166-1',
            countryListAgencyID: String(headerRow.__EMPTY_46 || '6')
          },
          shipment: {
            id: headerRow.__EMPTY_48 || 'NA',
            freightAllowanceCharge: {
              indicator: headerRow.__EMPTY_49 === true || 
                        headerRow.__EMPTY_49 === 'true' || 
                        headerRow.__EMPTY_49 === 1,
              reason: headerRow.__EMPTY_50 || 'NA',
              amount: headerRow.__EMPTY_51 || 0
            }
          }
        },
        payment: {
          paymentMeansCode: headerRow.PaymentMeans || 'NA',
          payeeFinancialAccount: headerRow.__EMPTY_52 || 'NA',
          paymentTerms: headerRow.PaymentTerms || 'NA',
          prepaidPayment: {
            id: headerRow.PrepaidPayment || 'NA',
            amount: parseFloat(headerRow.__EMPTY_53) || 0,
            date: headerRow.__EMPTY_54 || null,
            time: headerRow.__EMPTY_55 || null
          }
        },
        items: [],
        summary: {
          amounts: {
            lineExtensionAmount: 0,
            taxExclusiveAmount: 0,
            taxInclusiveAmount: 0,
            allowanceTotalAmount: 0,
            chargeTotalAmount: 0,
            payableRoundingAmount: 0,
            payableAmount: 0
          },
          tax: {
            totalAmount: 0,
            taxableAmount: 0,
            taxAmount: 0,
            taxTypeCode: footerRow?.__EMPTY_60,
            taxScheme: {
              id: footerRow?.__EMPTY_61,
              schemeID: footerRow?.__EMPTY_62,
              schemeAgencyID: footerRow?.__EMPTY_63
            }
          }
        },
        allowanceCharge: {
          indicator: headerRow.InvoiceAllowanceCharge === true || 
                    headerRow.InvoiceAllowanceCharge === 'true' || 
                    headerRow.InvoiceAllowanceCharge === 1,
          reason: headerRow.__EMPTY_56 || 'NA',
          amount: parseFloat(headerRow.__EMPTY_57) || 0
        }
      };

      // Add validation logging
      console.log('\n=== Document Field Validation ===');
      console.log(JSON.stringify({
        supplier: {
          additionalFields: {
            additionalAccountID: doc.supplier.additionalAccountID,
            schemeAgencyName: doc.supplier.schemeAgencyName,
            industryClassificationCode: doc.supplier.industryClassificationCode,
            countryListID: doc.supplier.address.countryListID,
            countryListAgencyID: doc.supplier.address.countryListAgencyID
          }
        },
        buyer: {
          countryFields: {
            countryListID: doc.buyer.address.countryListID,
            countryListAgencyID: doc.buyer.address.countryListAgencyID
          }
        },
        delivery: {
          shipment: doc.delivery.shipment,
          countryFields: {
            countryListID: doc.delivery.address.countryListID,
            countryListAgencyID: doc.delivery.address.countryListAgencyID
          }
        },
        payment: doc.payment
      }, null, 2));

      // Log the raw to JSON mapping with more detail
      console.log('\n=== Raw to JSON Mapping ===');
      console.log(JSON.stringify({
        rawData: {
          taxScheme: {
            raw_id: footerRow?.__EMPTY_61,
            raw_schemeID: footerRow?.__EMPTY_62,
            raw_schemeAgencyID: footerRow?.__EMPTY_63
          }
        },
        mappedData: doc.summary.tax.taxScheme
      }, null, 2));

      logRawToJson(doc, `${doc.header.invoiceNo}_eInvoice`);

      return doc;
    };

    // Function to process line items
    const processLineItem = (lineRow) => {
      return {
        lineId: lineRow.InvoiceLine,
        quantity: lineRow.__EMPTY_70,
        unitCode: lineRow.__EMPTY_71,
        lineExtensionAmount: parseFloat(lineRow.__EMPTY_72),
        allowanceCharges: [{
          chargeIndicator: lineRow.__EMPTY_73 === true || 
                          lineRow.__EMPTY_73 === 'true' || 
                          lineRow.__EMPTY_73 === 1,
          reason: lineRow.__EMPTY_74,
          multiplierFactorNumeric: parseFloat(lineRow.__EMPTY_75),
          amount: parseFloat(lineRow.__EMPTY_76)
        }],
        taxTotal: {
          amount: parseFloat(lineRow.InvoiceLine_TaxTotal),
          taxableAmount: parseFloat(lineRow.__EMPTY_77),
          taxAmount: parseFloat(lineRow.__EMPTY_78),
          percent: parseFloat(lineRow.__EMPTY_79),
          category: {
            id: lineRow.__EMPTY_80,
            exemptionReason: lineRow.__EMPTY_81,
            scheme: {
              id: lineRow.__EMPTY_82 || 'OTH',
              schemeId: lineRow.__EMPTY_83 || 'UN/ECE 5153',
              schemeAgencyId: String(lineRow.__EMPTY_84 || '6')
            }
          }
        },
        item: {
          classification: {
            code: lineRow.InvoiceItem,
            type: lineRow.__EMPTY_85
          },
          description: lineRow.__EMPTY_86,
          originCountry: lineRow.__EMPTY_87
        },
        price: {
          amount: parseFloat(lineRow.__EMPTY_88),
          extension: parseFloat(lineRow.__EMPTY_89)
        }
      };
    };

    // Function to process footer row
    const processFooter = (footerRow) => {
      return {
        amounts: {
          lineExtensionAmount: parseFloat(footerRow.LegalMonetaryTotal) || 0,      // 39.5
          taxExclusiveAmount: parseFloat(footerRow.__EMPTY_64) || 0,               // 39.5
          taxInclusiveAmount: parseFloat(footerRow.__EMPTY_65) || 0,               // 41.87
          allowanceTotalAmount: parseFloat(footerRow.__EMPTY_66) || 0,             // 0.5
          chargeTotalAmount: parseFloat(footerRow.__EMPTY_67) || 0,                // 10
          payableRoundingAmount: parseFloat(footerRow.__EMPTY_68) || 0,            // 0
          payableAmount: parseFloat(footerRow.__EMPTY_69) || 0                     // 41.87
        },
        legalMonetaryTotal: {
          lineExtensionAmount: parseFloat(footerRow.LegalMonetaryTotal) || 0,      // 39.5
          taxExclusiveAmount: parseFloat(footerRow.__EMPTY_64) || 0,               // 39.5
          taxInclusiveAmount: parseFloat(footerRow.__EMPTY_65) || 0,               // 41.87
          allowanceTotalAmount: parseFloat(footerRow.__EMPTY_66) || 0,             // 0.5
          chargeTotalAmount: parseFloat(footerRow.__EMPTY_67) || 0,                // 10
          payableRoundingAmount: parseFloat(footerRow.__EMPTY_68) || 0,            // 0
          payableAmount: parseFloat(footerRow.__EMPTY_69) || 0                     // 41.87
        },
        tax: {
          totalAmount: parseFloat(footerRow.Invoice_TaxTotal) || 0,                // 2.37
          taxableAmount: parseFloat(footerRow.__EMPTY_58) || 0,                    // 39.5
          taxAmount: parseFloat(footerRow.__EMPTY_59) || 0,                        // 2.37
          taxTypeCode: footerRow.__EMPTY_60 || 'NA',                              // "02"
          taxScheme: {
            id: footerRow.__EMPTY_61 || 'OTH',                                    // "OTH"
            schemeID: footerRow.__EMPTY_62 || 'UN/ECE 5153',                      // "UN/ECE 5155-02"
            schemeAgencyID: footerRow.__EMPTY_63 || '6'                           // 2
          }
        }
      };
    };

    // Process each row
    dataRows.forEach((row, index) => {
      const rowType = row.__EMPTY;

      switch(rowType) {
        case 'H':
          if (row.Invoice) {
            if (currentDocument) {
              documents.push(currentDocument);
            }
            currentDocument = createNewDocument(row, dataRows, index);
          }
          break;

        case 'L':
          if (currentDocument && row.__EMPTY === 'L') {
            const lineItem = processLineItem(row);
            
            // Group items by lineId
            if (lineItem.lineId) {
              const existingLineIndex = currentDocument.items.findIndex(item => item.lineId === lineItem.lineId);
              
              if (existingLineIndex >= 0) {
                // Add allowance/charge to existing line
                currentDocument.items[existingLineIndex].allowanceCharges.push({
                  chargeIndicator: lineItem.allowanceCharges[0].chargeIndicator,
                  reason: lineItem.allowanceCharges[0].reason,
                  multiplierFactorNumeric: lineItem.allowanceCharges[0].multiplierFactorNumeric,
                  amount: lineItem.allowanceCharges[0].amount
                });
              } else {
                // Add new line item
                currentDocument.items.push(lineItem);
              }
            }
          }
          break;

        case 'F':
          if (currentDocument) {
            // Process footer data
            const footerData = processFooter(row);
            
            // Update document summary with footer data
            currentDocument.summary = footerData;

            // Now that we have all data, map to LHDN format
            const lhdnStructure = mapToLHDNFormat([currentDocument]);
            logLhdnMapping(lhdnStructure, `${currentDocument.header.invoiceNo}_eInvoice`);

            // Add to documents array
            documents.push(currentDocument);
            currentDocument = null;
          }
          break;
      }
    });

    // Add the last document
    if (currentDocument) {
      documents.push(currentDocument);
    }

    logger.info('Processed documents:', documents.length);

    // Only write to file if explicitly requested
    if (process.env.EXCEL_DEBUG === 'true') {
      const logsDir = path.join(process.cwd(), 'logs', 'excel');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
      const logPath = path.join(logsDir, 'excel-processing.log');
      fs.appendFileSync(logPath, JSON.stringify(processLogs, null, 2) + '\n---\n');
    }

    return documents;

  } catch (error) {
    logger.error('Error processing Excel data:', error);
    addToProcessLog({
      error: error.message,
      stack: error.stack
    }, 'errors');
    throw error;
  }
};

module.exports = { 
  processExcelData,
  isValidFileName,
  getDocumentType,
  DOCUMENT_TYPES
};