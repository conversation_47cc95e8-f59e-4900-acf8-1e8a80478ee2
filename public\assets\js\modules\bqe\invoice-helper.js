class InvoiceHelper {
    static calculateCancellationTime(submissionDate, status) {
        // Ensure status is a string before calling toLowerCase()
        const statusStr = typeof status === 'string' ? status : String(status || '');
        if (!submissionDate || statusStr.toLowerCase() !== 'submitted') {
            return {
                isNA: true,
                text: 'Not Applicable'
            };
        }

        try {
            // Convert ISO string to local date object
            const submitTime = new Date(submissionDate.replace('T', ' ').replace('Z', ''));
            const currentTime = new Date();
            
            // Calculate total milliseconds difference
            const diffMs = submitTime - currentTime;
            const diffHours = diffMs / (1000 * 60 * 60);
            
            // If more than 72 hours have passed
            if (diffHours <= -72) {
                return {
                    expired: true,
                    text: 'Expired'
                };
            }
            
            // Calculate remaining time
            const remainingHours = Math.floor(72 + diffHours);
            const remainingMinutes = Math.floor((diffHours % 1) * 60);
            const adjustedMinutes = remainingMinutes < 0 ? 60 + remainingMinutes : remainingMinutes;
            
            // Check for urgent status (less than 24 hours remaining)
            const isUrgent = remainingHours < 24;
            
            return {
                expired: false,
                isUrgent,
                hours: remainingHours,
                minutes: adjustedMinutes,
                text: `${remainingHours}h ${adjustedMinutes}m remaining`
            };
    
        } catch (error) {
            console.error('Error calculating time:', error);
            return {
                error: true,
                text: 'Error calculating time'
            };
        }
    }
    static mapInvoicesWithStagingData(invoices, stagingMap) {
        return invoices.map(invoice => {
            const stagingInfo = stagingMap[invoice.invoice_number];
            
            // For existing invoices in staging
            if (stagingInfo) {
                return {
                    ...invoice,
                    status: stagingInfo.status,
                    // Use exact timestamps from staging
                    date_submitted: stagingInfo.date_submitted || null,
                    date_sync: stagingInfo.date_sync || null,
                };
            }

            // For new invoices not in staging
            return {
                ...invoice,
                status: 'Pending',
                date_submitted: null,
                date_sync: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        });
    }
}

// Export the helper class
window.InvoiceHelper = InvoiceHelper; 