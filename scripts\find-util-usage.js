const fs = require('fs');
const path = require('path');

function searchFiles(dir, searchText) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules')) {
      searchFiles(filePath, searchText);
    } else if (file.endsWith('.js')) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes(searchText)) {
        console.log(`Found in ${filePath}`);
      }
    }
  });
}

searchFiles(process.cwd(), 'util._extend');
searchFiles(process.cwd(), 'require("util")');
searchFiles(process.cwd(), "require('util')");