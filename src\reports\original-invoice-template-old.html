<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invoice</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #ffffff;
    }

    .container {
      width: 900px;
      margin: 20px auto;
      padding: 20px;
      background-color: #ffffff;
    }

    .header {
      text-align: center;
      margin-bottom: 10px;
    }

    .header h1 {
      font-size: 18px;
      margin: 5px 0;
    }

    .header p {
      margin: 2px 0;
      font-size: 14px;
    }

    .info-section {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .info-section-left {
      width: 65%;
      font-size: 14px;
      line-height: 1.5;
    }

    .info-section-left p {
      margin: 2px 0;
    }

    .info-section-right {
      width: 30%;
      font-size: 14px;
      line-height: 1.5;
      text-align: right;
    }

    .info-section-right p {
      margin: 2px 0;
    }

    .red-illustration {
      color: red;
      font-size: 14px;
      font-weight: bold;
      text-align: right;
    }

    /* Styles for the item details table */
    .details-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      font-size: 12px;
      table-layout: fixed;
    }

    .details-table th, 
    .details-table td {
      padding: 4px 6px;
      border: 1px solid #000;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .details-table th {
      background-color: #000;
      color: #fff;
      text-align: center;
      font-weight: normal;
    }

    .details-table td {
      text-align: left;
    }

    .details-table td.text-right,
    .details-table th.text-right {
      text-align: right;
    }

    .details-table td.text-center,
    .details-table th.text-center {
      text-align: center;
    }

    .details-table td:nth-child(3) {
      white-space: normal;
    }

    /* Total section */
    .total-section {
      display: flex;
      justify-content: flex-end;
    }

    .total-table {
      width: 100%; /* Full width */
      border-collapse: collapse;
      text-align: center;
    }

    .total-table th {
      padding: 8px;
      border: 1px solid #000;
      background-color: #000;
      color: #fff;
      text-align: center;
    }

    .total-table td {
      padding: 8px;
      border: 1px solid #000;
      background-color: #f0f0f0; /* Light gray background */
      text-align: center;
    }

    /* QR Code */
    .qr-code {
      text-align: right;
      margin-top: 10px;
    }

    .qr-code img {
      width: 100px;
      height: 100px;
    }

    .footer {
      text-align: center;
      margin-top: 20px;
      font-size: 12px;
    }

    /* Add these styles to your existing CSS */
    .details-table td.text-right {
      text-align: right;
    }
    
    .details-table th.text-right {
      text-align: right;
    }

    /* Make the table columns more proportional */
    .details-table th,
    .details-table td {
      padding: 8px;
      border: 1px solid #000;
    }

    /* Set specific widths for columns */
    .details-table th:nth-child(1) { width: 3%; }   /* No. */
    .details-table th:nth-child(2) { width: 8%; }   /* Classification */
    .details-table th:nth-child(3) { width: 25%; }  /* Description */
    .details-table th:nth-child(4) { width: 8%; }   /* Quantity */
    .details-table th:nth-child(5) { width: 12%; }  /* Unit Price */
    .details-table th:nth-child(6) { width: 12%; }  /* Amount */
    .details-table th:nth-child(7) { width: 8%; }   /* Disc */
    .details-table th:nth-child(8) { width: 12%; }  /* Tax Amount */
    .details-table th:nth-child(9) { width: 12%; }  /* Total */

  </style>
</head>
<body>
  <div class="container">
    <!-- Company Header -->
    <div class="header">
      <img src="https://erp-image.sgliteasset.com/_next/image?url=https%3A%2F%2Fs3.ap-southeast-1.amazonaws.com%2Fcdn1.sgliteasset.com%2Felianwar%2Fimages%2Fwebstore%2Flogo%2F320_X160_01_1724751955.jpg&w=640&q=75" alt="Company Logo">
      <h1>{{:companyName}}</h1>
      <p>{{:companyAddress}}</p>
      <p>{{:companyPhone}} | {{:companyEmail}}</p>
    </div>

    <!-- Supplier and Buyer Information on the Left, e-Invoice Information on the Right -->
    <div class="info-section">
      <div class="info-section-left">
        <p><strong>Supplier TIN:</strong> {{:SupplierTIN}}</p>
        <p><strong>Supplier Registration Number:</strong> {{:SupplierRegistrationNumber}}</p>
        <p><strong>Supplier SST ID:</strong> {{:SupplierSSTID}}</p>
        <p><strong>Supplier MSIC code:</strong> {{:SupplierMSICCode}}</p>
        <p><strong>Supplier business activity description:</strong> {{:SupplierBusinessActivity}}</p>

        <p><strong>Buyer TIN:</strong> {{:BuyerTIN}}</p>
        <p><strong>Buyer Name:</strong> {{:BuyerName}}</p>
        <p><strong>Buyer Contact Number:</strong> {{:BuyerPhone}}</p>
        <p><strong>Buyer Registration Number:</strong> {{:BuyerRegistrationNumber}}</p>
        <p><strong>Buyer Address:</strong> {{:BuyerAddress}}</p>
      </div>

      <div class="info-section-right">
        <p><strong>E-INVOICE</strong></p>
        <p><strong>e-Invoice Type:</strong> {{:InvoiceType}}</p>
        <p><strong>e-Invoice version:</strong> {{:InvoiceVersion}}</p>
        <p><strong>e-Invoice code:</strong> {{:InvoiceCode}}</p>
        <p><strong>Unique Identifier No:</strong> {{:UniqueIdentifier}}</p>
        <p><strong>Original Invoice Ref No.:</strong> {{:OriginalInvoiceRef}}</p>
        <p><strong>Invoice Date and Time:</strong> {{:dateTimeReceived}}</p>
 
      </div>
    </div>

    <!-- Item Details Table -->
    <table class="details-table">
      <tr>
        <th class="text-center">No.</th>
        <th class="text-center">Classification</th>
        <th>Description</th>
        <th class="text-right">Quantity</th>
        <th class="text-right">Unit Price</th>
        <th class="text-right">Amount</th>
        <th class="text-right">Disc</th>
        <th class="text-right">Tax Amount</th>
        <th class="text-right">Total</th>
      </tr>
      {{for items}}
      <tr>
        <td class="text-center">{{:No}}</td>
        <td class="text-center">{{:Cls}}</td>
        <td>{{:Description}}</td>
        <td class="text-right">{{:Quantity}}</td>
        <td class="text-right">MYR {{:UnitPrice}}</td>
        <td class="text-right">MYR {{:Amount}}</td>
        <td class="text-right">MYR {{:Disc}}</td>
        <td class="text-right">MYR {{:TaxAmount}}</td>
        <td class="text-right">MYR {{:TotalExclTax}}</td>
      </tr>
      {{/for}}
      <tr>
        <th colspan="8" class="text-right">Subtotal</th>
        <td class="text-right">MYR {{:Subtotal}}</td>
      </tr>
      <tr>
        <th colspan="8" class="text-right">Total excluding tax</th>
        <td class="text-right">MYR {{:TotalExcludingTax}}</td>
      </tr>
      <tr>
        <th colspan="8" class="text-right">Tax amount</th>
        <td class="text-right">MYR {{:TaxAmount}}</td>
      </tr>
      <tr>
        <th colspan="8" class="text-right">Total including tax</th>
        <td class="text-right">MYR {{:TotalIncludingTax}}</td>
      </tr>
      <tr>
        <th colspan="8" class="text-right">Total payable amount</th>
        <td class="text-right">MYR {{:TotalPayableAmount}}</td>
      </tr>
    </table>

    <!-- Totals Section (at the bottom) -->
    <!-- <div class="total-section">
      <table class="total-table">
        <tr>
          <th>Total Product / Service Price</th>
          <th>Tax Type</th>
          <th>Tax Rate</th>
          <th>Tax Amount</th>
        </tr>
        <tr>
          <td>MYR {{:TotalProductPrice}}</td>
          <td>{{:TaxType}}</td>
          <td>{{:TaxRate}}%</td>
          <td>MYR {{:TaxAmount}}</td>
        </tr>
      </table>
    </div> -->

    <!-- QR Code -->
    <div class="qr-code">
      <img src="{{:qrCode}}" alt="QR Code">
    </div>

    <!-- Footer Section -->
    <div class="footer">
      <p>Digital Signature: {{:DigitalSignature}}</p>
      <p>Date and Time of Validation: {{:validationDateTime}}</p>
      <p>This document is a visual presentation of the e-Invoice.</p>
    </div>
    
  </div>
</body>
</html>





<script>
  // JavaScript function to format numbers with commas
  function formatNumber(number) {
    return parseFloat(number).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }
</script>
