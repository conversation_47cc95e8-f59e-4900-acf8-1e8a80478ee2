const { validateNetworkPath } = require('../config/paths');
const { fileConfig } = require('../config/paths');

async function testNetworkAccess() {
  console.log('Testing network access...');
  console.log('Path:', fileConfig.formattedBaseDir);
  
  const isAccessible = await validateNetworkPath(fileConfig.formattedBaseDir);
  
  console.log('Network path accessible:', isAccessible);
}

testNetworkAccess().catch(console.error);