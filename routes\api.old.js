// routes/api.js
const express = require('express');
const router = express.Router();
const { extractAndMapData } = require('../services/mappingService');
const signAndSendLHDNData = require('../services/signAndSendLHDNData');
const sendDataToLHDN = require('../services/sendDataToLHDN');
const { WP_USER_REGISTRATION, WP_COMPANY_SETTINGS, WP_OUTBOUND_BQE_INVOICES } = require('../models');
const bcrypt = require('bcrypt');
const saltRounds = 10;
const multer = require('multer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../public/images');
    // Ensure the directory exists
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});
const upload = multer({ storage });


router.post('/save-generated-xml', async (req, res) => {
    try {
        const { xmlContent, invoiceNumber } = req.body;

        if (!xmlContent || !invoiceNumber) {
            return res.status(400).json({
                success: false,
                error: 'Missing required parameters'
            });
        }

        const result = saveGeneratedXML(xmlContent, invoiceNumber);

        if (result.success) {
            res.json({
                success: true,
                message: 'XML file saved successfully',
                filePath: result.filePath,
                filename: result.filename
            });
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('Error in save-generated-xml endpoint:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Generate XML endpoint
router.post('/generate-xml', async (req, res) => {
    try {
        const { invoiceData } = req.body;

        if (!invoiceData) {
            return res.status(400).json({
                success: false,
                error: 'Missing invoice data'
            });
        }

        // Transform the invoice data to match the XML template format
        const xmlData = {
            invoiceNumber: invoiceData.basic.invoiceNumber,
            issueDate: new Date(invoiceData.basic.date).toISOString().split('T')[0],
            issueTime: new Date(invoiceData.basic.date).toISOString().split('T')[1],
            supplierName: invoiceData.supplier.name,
            supplierTIN: invoiceData.supplier.tin,
            supplierBRN: invoiceData.supplier.registrationNo,
            supplierSST: invoiceData.supplier.sstNo,
            supplierPhone: invoiceData.supplier.phone,
            supplierEmail: invoiceData.supplier.email,
            customerName: invoiceData.buyer.name,
            customerTIN: invoiceData.buyer.tin,
            customerBRN: invoiceData.buyer.registrationNo,
            customerSST: invoiceData.buyer.sstNo,
            customerPhone: invoiceData.buyer.phone,
            customerEmail: invoiceData.buyer.email,
            // Add invoice lines
            invoiceLines: invoiceData.items.map((item, index) => ({
                lineId: (index + 1).toString(),
                quantity: item.quantity,
                description: item.description,
                unitPrice: item.unitPrice,
                lineAmount: item.totalAmount,
                taxRate: item.taxRate,
                taxAmount: item.taxAmount
            }))
        };

        // Generate XML content
        const xmlContent = generateXML(xmlData);

        // Save the generated XML
        const result = saveGeneratedXML(xmlContent, invoiceData.basic.invoiceNumber);

        if (result.success) {
            res.json({
                success: true,
                message: 'XML generated successfully',
                filename: result.filename,
                filePath: result.filePath
            });
        } else {
            throw new Error(result.error);
        }

    } catch (error) {
        console.error('Error generating XML:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// View XML endpoint
router.get('/view-xml/:filename', (req, res) => {
    try {
        const filename = req.params.filename;
        const filePath = path.join(__dirname, '../generated-xmls', filename);

        if (!fs.existsSync(filePath)) {
            return res.status(404).send('XML file not found');
        }

        const xmlContent = fs.readFileSync(filePath, 'utf8');
        res.header('Content-Type', 'text/xml');
        res.send(xmlContent);

    } catch (error) {
        console.error('Error viewing XML:', error);
        res.status(500).send('Error viewing XML file');
    }
});

router.get('/getSessionUserDetails', (req, res) => {
  if (!req.session.user) {
    return res.status(401).json({ success: false, message: 'User not logged in' });
  }

  res.json({
    success: true,
    user: req.session.user
  });
});

router.get('/fetchUsersAndCompanies', async (req, res) => {
  try {
      const companies = await WP_COMPANY_SETTINGS.findAll();
      const users = await WP_USER_REGISTRATION.findAll();

      res.json({ companies, users });
  } catch (error) {
      console.error('Error fetching users and companies:', error);
      res.status(500).json({ success: false, message: 'Failed to fetch users and companies' });
  }
});


// router.post('/updateValidationStatus', async (req, res) => {
//   const { TIN, IDValue, validStatus } = req.body;

//   try {
//     const company = await WP_COMPANY_SETTINGS.findOne({
//       where: {
//         TIN: TIN,
//         BRN: IDValue
//       }
//     });

//     if (!company) {
//       return res.status(404).json({ success: false, message: 'Company not found' });
//     }

//     await company.update({ ValidStatus: validStatus });

//     res.json({ success: true, message: 'Validation status updated successfully' });
//   } catch (error) {
//     console.error('Error updating validation status:', error);
//     res.status(500).json({ success: false, message: 'Failed to update validation status' });
//   }
// });
// Fetch company settings by ID
router.get('/company-settings/:id', async (req, res) => {
  const { id } = req.params;

  try {
      const company = await WP_COMPANY_SETTINGS.findOne({ where: { ID: id } });

      if (!company) {
          return res.status(404).json({ success: false, message: 'Company not found' });
      }

      res.json({ success: true, company });
  } catch (error) {
      console.error('Error:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Update company settings by ID
router.put('/updateCompanySettings/:id', upload.single('companyImage'), async (req, res) => {
  const { id } = req.params;
  const { companyName, industry, country, about, address, phone, email, tin, brn, userID } = req.body;

  try {
      // Find the company by its ID
      const company = await WP_COMPANY_SETTINGS.findOne({ where: { ID: id } });

      if (!company) {
          return res.status(404).json({ success: false, message: 'Company not found' });
      }

      // Update company fields
      if (req.file) {
          company.CompanyImage = '/images/' + req.file.filename; // Save the file path if an image is uploaded
      }
      company.CompanyName = companyName;
      company.Industry = industry;
      company.Country = country;
      company.About = about;
      company.Address = address;
      company.Phone = phone;
      company.Email = email;
      company.TIN = tin;
      company.BRN = brn;
      company.UserID = userID;

      // Save the updated company settings
      await company.save();

      res.json({ success: true, message: 'Company settings updated successfully' });
  } catch (error) {
      console.error('Error:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});


// // Update company settings by ID
// router.put('/updateCompanySettings/:id', upload.single('companyImage'), async (req, res) => {
//   const { id } = req.params;
//   const { companyName, industry, country, about, address, phone, email, tin, brn, userID } = req.body;

//   try {
//       const company = await WP_COMPANY_SETTINGS.findOne({ where: { ID: id } });

//       if (!company) {
//           return res.status(404).json({ success: false, message: 'Company not found' });
//       }

//       // Update company settings data
//       if (req.file) {
//           company.CompanyImage = '/images/' + req.file.filename; // Save the file path
//       }
//       company.CompanyName = companyName;
//       company.Industry = industry;
//       company.Country = country;
//       company.About = about;
//       company.Address = address;
//       company.Phone = phone;
//       company.Email = email;
//       company.TIN = tin;
//       company.BRN = brn;
//       company.UserID = userID;

//       await company.save();

//       res.json({ success: true, message: 'Company settings updated successfully' });
//   } catch (error) {
//       console.error('Error:', error);
//       res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });

// Delete company settings by ID
router.delete('/company-settings/:id', async (req, res) => {
  const { id } = req.params;

  try {
      const result = await WP_COMPANY_SETTINGS.destroy({ where: { ID: id } });
      if (result === 0) {
          return res.status(404).json({ success: false, message: 'Company setting not found' });
      }
      res.json({ success: true });
  } catch (error) {
      console.error('Error:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Validation endpoint
router.get('/validate-taxpayer', async (req, res) => {
  const { taxpayerId, idType, idValue, tin } = req.query;

  if (!taxpayerId || !idType || !idValue || !tin) {
    return res.status(400).json({ success: false, message: 'Missing required parameters' });
  }

  const apiUrl = `${process.env.API_BASE_URL}/api/v1.0/taxpayer/validate/${taxpayerId}?idType=${idType}&idValue=${idValue}&tin=${tin}`;
  const config = {
    method: 'get',
    url: apiUrl,
    headers: {
      'Authorization': `Bearer ${req.session.accessToken}`,
      'Content-Type': 'application/json'
    },
    timeout: 10000 // Increase timeout to 10 seconds
  };

  try {
    console.log(`Attempting to validate taxpayer with URL: ${apiUrl}`);
    console.log('Config:', config);

    const response = await axios(config);

    console.log('API Response:', response);

    if (response.status === 200) {
      // Update the validation status in the database
      await updateValidationStatus(taxpayerId, idValue, 'Validated');
      return res.json({ success: true, message: 'Validation successful' });
    } else {
      // Log unexpected 2xx status codes for further inspection
      console.log('Unexpected status code:', response.status);
      return res.status(500).json({ success: false, message: 'Unexpected status code from external API' });
    }
  } catch (error) {
    console.error('Error validating taxpayer:', error);

    if (error.response) {
      console.error('Error response data:', error.response.data);
      return res.status(error.response.status).json({
        success: false,
        message: error.response.data.title || 'Failed to validate taxpayer',
        error: error.response.data,
      });
    } else if (error.request) {
      console.error('No response received:', error.request);
      return res.status(500).json({
        success: false,
        message: 'No response received from external API',
        error: error.message,
      });
    } else {
      console.error('Error in request setup:', error.message);
      return res.status(500).json({
        success: false,
        message: 'Error in request setup',
        error: error.message,
      });
    }
  }
});

// Function to update the validation status in the database
async function updateValidationStatus(TIN, IDValue, validStatus) {
  try {
    const company = await WP_COMPANY_SETTINGS.findOne({
      where: {
        TIN: TIN,
        BRN: IDValue
      }
    });

    if (!company) {
      console.error('Company not found');
      throw new Error('Company not found');
    }

    await company.update({ ValidStatus: validStatus });
    console.log('Validation status updated successfully');
  } catch (error) {
    console.error('Error updating validation status:', error);
    throw error;
  }
}

// Define the updateValidationStatus POST endpoint
router.post('/updateValidationStatus', async (req, res) => {
  const { TIN, IDValue, validStatus } = req.body;

  console.log('Request body:', req.body);

  try {
      const company = await WP_COMPANY_SETTINGS.findOne({
          where: {
              TIN: TIN,
              BRN: IDValue
          }
      });

      if (!company) {
          console.log('Company not found');
          return res.status(404).json({ success: false, message: 'Company not found' });
      }

      console.log('Company found:', company);

      await company.update({ ValidStatus: validStatus });

      console.log('Validation status updated successfully');

      res.json({ success: true, message: 'Validation status updated successfully' });
  } catch (error) {
      console.error('Error updating validation status:', error);
      res.status(500).json({ success: false, message: 'Failed to update validation status' });
  }
});





// router.delete('/company-settings/:id', async (req, res) => {
//   const { id } = req.params;

//   try {
//       const result = await WP_COMPANY_SETTINGS.destroy({ where: { ID: id } });
//       if (result === 0) {
//           return res.status(404).json({ success: false, message: 'Company setting not found' });
//       }
//       res.json({ success: true });
//   } catch (error) {
//       console.error('Error:', error);
//       res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });

// // Fetch company settings by ID
// router.get('/company-settings/:id', async (req, res) => {
//   const { id } = req.params;

//   try {
//       const company = await WP_COMPANY_SETTINGS.findOne({ where: { ID: id } });

//       if (!company) {
//           return res.status(404).json({ success: false, message: 'Company not found' });
//       }

//       res.json({ success: true, company });
//   } catch (error) {
//       console.error('Error:', error);
//       res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });

// Endpoint to count companies
router.get('/company-count', async (req, res) => {
  try {
    const count = await WP_COMPANY_SETTINGS.count();
    res.json({ count });
  } catch (error) {
    console.error('Error counting companies:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// New user-related endpoints
router.get('/users-list', async (req, res) => {
  try {
    const users = await WP_USER_REGISTRATION.findAll();
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});


router.get('/user-details', async (req, res) => {
  try {
    const username = req.username;
    const adminStatus = req.admin;
    const user = await WP_USER_REGISTRATION.findOne({ where: { Username: username } });
    if (!user) {
      req.session.destroy();
      return res.status(404).json({ error: 'User not found', logout: true });
    }
    console.log('Admin Status:', adminStatus); // Log the Admin status for debugging
    res.json({ ...user.toJSON(), Admin: adminStatus });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch user details' });
  }
});

router.get('/getUserInfo', async (req, res) => {
  try {
      const username = req.query.username;

      const user = await WP_USER_REGISTRATION.findOne({ where: { Username: username } });

      if (!user) {
          return res.status(404).json({ success: false, message: 'User not found' });
      }

      res.json({
          TIN: user.TIN,
          IDType: user.IDType,
          IDValue: user.IDValue
      });
  } catch (error) {
      console.error('Error fetching user details:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// New api for fetching username and company logo
// router.get('/getUserAndCompanyDetails', async (req, res) => {
//   try {
//     const currentUsername = req.session.user.username;
//     // console.log('Current session user:', currentUsername);

//     const user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });
//     // console.log('Fetched user:', user);

//     if (!user) {
//       return res.status(404).json({ success: false, message: 'User not found' });
//     }

//     const tin = user.TIN;
//     // console.log('User TIN:', tin);

//     if (!tin) {
//       return res.status(400).json({ success: false, message: 'TIN not found for user' });
//     }

//     const companySettings = await WP_COMPANY_SETTINGS.findOne({ where: { TIN: tin } });
//     if (!companySettings) {
//       return res.status(404).json({ success: false, message: 'Company settings not found' });
//     }

//     res.json({ 
//       success: true, 
//       username: user.Username, 
//       companyLogo: companySettings.CompanyImage,
//       companyName: companySettings.CompanyName,
//       companyEmail: companySettings.Email
//     });
//   } catch (error) {
//     console.error('Error fetching user and company details:', error);
//     res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });
// router.get('/getUserAndCompanyDetails', async (req, res) => {
//   try {
//     if (!req.session.user) {
//       return res.status(401).json({ success: false, message: 'User not logged in' });
//     }

//     const currentUsername = req.session.user.username;
//     console.log('Current session user:', currentUsername);

//     const user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });
//     if (!user) {
//       return res.status(404).json({ success: false, message: 'User not found' });
//     }

//     res.json({ 
//       success: true, 
//       username: user.Username 
//     });
//   } catch (error) {
//     console.error('Error fetching user details:', error);
//     res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });

// router.get('/getUserAndCompanyDetails', async (req, res) => {
//   try {
//     if (!req.session.user) {
//       return res.status(401).json({ success: false, message: 'User not logged in' });
//     }

//     const currentUsername = req.session.user.username;
//     console.log('Current session user:', currentUsername);

//     const user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });
//     if (!user) {
//       return res.status(404).json({ success: false, message: 'User not found' });
//     }

//     const tin = user.TIN;
//     if (!tin) {
//       return res.status(400).json({ success: false, message: 'TIN not found for user' });
//     }

//     const companySettings = await WP_COMPANY_SETTINGS.findOne({ where: { TIN: tin } });
//     if (!companySettings) {
//       return res.status(404).json({ success: false, message: 'Company settings not found' });
//     }

//     const companyLogo = companySettings.CompanyImage || 'https://www.svgrepo.com/show/340721/no-image.svg';

//     res.json({
//       success: true,
//       username: user.Username,
//       companyLogo: companyLogo,
//       companyName: companySettings.CompanyName,
//       companyEmail: companySettings.Email
//     });
//   } catch (error) {
//     console.error('Error fetching user and company details:', error);
//     res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });



// router.post('/saveCompanySettings', upload.single('companyImage'), async (req, res) => {
//   const { 
//     username, password, clientId, clientSecret, companyName, industry, country, about, address, phone, email, tin, brn, idValue
//   } = req.body;

//   try {
//     const currentUsername = req.session.user.username;

//     let user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });

//     if (!user) {
//       return res.status(404).json({ success: false, message: 'User not found' });
//     }

//     if (!user.TIN) {
//       return res.status(400).json({ success: false, message: 'User does not have a TIN' });
//     }

//     // Update user data
//     user.ClientID = clientId;
//     user.ClientSecret = clientSecret;

//     // Hash the password if provided and update it
//     if (password) {
//       const hashedPassword = await bcrypt.hash(password, saltRounds);
//       user.Password = hashedPassword;
//     }

//     await user.save();

//     let companySettings = await WP_COMPANY_SETTINGS.findOne({ where: { TIN: user.TIN } });
//     if (!companySettings) {
//       return res.status(404).json({ success: false, message: 'Company settings not found for the given TIN' });
//     }

//     // Update company settings data
//     if (req.file) {
//       companySettings.CompanyImage = '/images/' + req.file.filename; // Save the file path
//     }
//     companySettings.CompanyName = companyName;
//     companySettings.Industry = industry;
//     companySettings.Country = country;
//     companySettings.About = about;
//     companySettings.Address = address;
//     companySettings.Phone = phone;
//     companySettings.Email = email;

//     await companySettings.save();

//     res.json({ success: true });
//   } catch (error) {
//     console.error('Error:', error);
//     res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });

router.post('/users-add', async (req, res) => {
  try {
    const hashedPassword = await bcrypt.hash(req.body.Password, saltRounds);
    
    const userData = {
      ...req.body,
      Password: hashedPassword
    };

    const newUser = await WP_USER_REGISTRATION.create(userData);
    res.status(201).json(newUser);
  } catch (error) {
    res.status(500).json({ error: 'Failed to add new user' });
  }
});

// Endpoint to get a specific user by ID
router.get('/users/:id', async (req, res) => {
  const { id } = req.params;
  try {
      const user = await WP_USER_REGISTRATION.findByPk(id);
      if (!user) {
          return res.status(404).json({ success: false, message: 'User not found' });
      }
      res.json(user);
  } catch (error) {
      console.error('Error:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

router.put('/users-update/:id', async (req, res) => {
  const { id } = req.params;
  const { fullName, email, username, password, admin } = req.body;

  try {
      let user = await WP_USER_REGISTRATION.findByPk(id);

      if (!user) {
          return res.status(404).json({ success: false, message: 'User not found' });
      }

      user.FullName = fullName;
      user.Email = email;
      user.Username = username;
      user.Admin = admin;

      if (password) {
          const hashedPassword = await bcrypt.hash(password, saltRounds);
          user.Password = hashedPassword;
      }

      await user.save();

      res.json({ success: true });
  } catch (error) {
      console.error('Error:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

router.get('/api/users/:id', async (req, res) => {
  const { id } = req.params;
  try {
      const user = await WP_USER_REGISTRATION.findByPk(id);
      if (!user) {
          return res.status(404).json({ success: false, message: 'User not found' });
      }
      res.json(user);
  } catch (error) {
      console.error('Error:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

router.delete('/users-delete/:id', async (req, res) => {
  const { id } = req.params;
  try {
      const result = await WP_USER_REGISTRATION.destroy({ where: { ID: id } });
      if (result === 0) {
          return res.status(404).json({ success: false, message: 'User not found' });
      }
      res.json({ success: true });
  } catch (error) {
      console.error('Error:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});



router.get('/extract', async (req, res) => {
  try {
    const data = await extractAndMapData();
    res.json(data);
  } catch (error) {
    res.status(500).send(error.message);
  }
});


// Test endpoint
router.get('/test-lhdn', async (req, res) => {
  try {
    const signedLhdnData = await signAndSendLHDNData();
    res.setHeader('Content-Type', 'application/xml');
    res.send(signedLhdnData);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post('/send-lhdn', async (req, res) => {
  try {
    await sendDataToLHDN();
    res.json({ success: true, message: 'Data sent to LHDN successfully' });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Failed to send data to LHDN', error: error.message });
  }
});

// GET /api/invoices/db - Fetch all invoices from database
router.get('/invoices/db', async (req, res) => {
    try {
        logger.info('Fetching invoices from database...');
        
        const invoices = await WP_OUTBOUND_BQE_INVOICES.findAll({
            order: [['date', 'DESC']],
            raw: true // Get plain objects instead of Sequelize instances
        });

        logger.info(`Found ${invoices.length} invoices in database`);
        
        res.json({
            success: true,
            data: invoices
        });

    } catch (error) {
        logger.error('Error fetching invoices from database:', {
            error: error.message,
            stack: error.stack
        });
        res.status(500).json({
            success: false,
            error: 'Failed to fetch invoices from database',
            details: error.message
        });
    }
});

router.get('/bqe/invoices', async (req, res) => {
    try {
        const { fromDate, toDate } = req.query;
        console.log('Backend: Received request for invoices with dates:', { fromDate, toDate });

        // Get your BQE API credentials from environment variables
        const BQE_API_URL = process.env.BQE_API_URL;
        const BQE_ACCESS_TOKEN = process.env.BQE_ACCESS_TOKEN;

        // Make request to BQE API
        const response = await axios.get(`${BQE_API_URL}/invoices`, {
            headers: {
                'Authorization': `Bearer ${BQE_ACCESS_TOKEN}`,
                'Content-Type': 'application/json'
            },
            params: {
                fromDate,
                toDate,
                pageSize: 100 // Adjust as needed
            }
        });

        console.log('Backend: BQE API Response:', response.data);

        res.json({
            success: true,
            data: response.data
        });

    } catch (error) {
        console.error('Backend: Error fetching from BQE API:', error.response?.data || error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;