// BQE Authentication Management
class BQEAuthManager {
    constructor() {
        this.authCheckInterval = null;
        this.hasShownAuthAlert = false;
        this.lastAuthState = null; // Cache last auth state
        this.lastCheck = 0; // Timestamp of last check
        this.checkThrottle = 2000; // Check every 2 seconds instead of 1
        this.initializeAuth();
    }

    async getAccessToken() {
        try {
            const response = await fetch('/bqe/check-auth');
            const data = await response.json();

            if (!data.isAuthorized || !data.authResponse?.access_token) {
                throw new Error('No valid access token available. Please authorize BQE first.');
            }

            // Check if token is expired
            if (data.details?.expiresIn <= 0) {
                throw new Error('Access token has expired. Please reauthorize BQE.');
            }

            return data.authResponse.access_token;
        } catch (error) {
            console.error('Error getting access token:', error);
            throw error;
        }
    }

    async checkAuthStatus() {
        try {
            // Throttle checks
            const now = Date.now();
            if (now - this.lastCheck < this.checkThrottle) {
                return;
            }
            this.lastCheck = now;

            const bqeAuthBtn = document.getElementById('bqeAuthBtn');
            const bqeAuthBtnText = document.getElementById('bqeAuthBtnText');
            const filtersSection = document.querySelector('.filters-section');

            if (!bqeAuthBtn || !bqeAuthBtnText) {
                return;
            }

            const response = await fetch('/bqe/check-auth');
            const data = await response.json();

            // Check if auth state has changed
            const currentState = JSON.stringify(data);
            if (currentState === this.lastAuthState) {
                return; // No change, skip update
            }
            this.lastAuthState = currentState;

            const tooltip = bootstrap.Tooltip.getInstance(bqeAuthBtn);
            const isHovering = bqeAuthBtn.matches(':hover');

            // Update UI based on auth status
            if (data.isAuthorized && data.details) {
                // Show filters section only when authorized AND in BQE mode
                if (filtersSection) {
                    // Check if we're in BQE mode before showing filters
                    const currentDataSource = window.invoiceTable?.currentDataSource || 'bqe';
                    if (currentDataSource === 'bqe') {
                        filtersSection.classList.remove('d-none');
                        filtersSection.style.display = '';
                    }
                }
                // Rest of your authorized state UI updates...
            } else {
                // Hide filters section when not authorized
                if (filtersSection) {
                    filtersSection.classList.add('d-none');
                }
                // Reset to unauthorized state
                bqeAuthBtn.classList.add('auth-btn');
                bqeAuthBtn.classList.remove('btn-danger');
                bqeAuthBtnText.textContent = 'Authorize BQE';

                const icon = bqeAuthBtn.querySelector('i');
                if (icon) {
                    icon.classList.remove('bi-shield-x');
                    icon.classList.add('bi-shield-lock');
                }
            }

            // Update the UI
            this.updateAuthUI(data, bqeAuthBtn, bqeAuthBtnText, tooltip, isHovering);
        } catch (error) {
            console.error('Error checking auth status:', error);
            // Hide filters section on error
            const filtersSection = document.querySelector('.filters-section');
            if (filtersSection) {
                filtersSection.classList.add('d-none');
            }
        }
    }

	updateAuthUI(data, bqeAuthBtn, bqeAuthBtnText, tooltip, isHovering) {
        // Add null checks for required elements
        if (!bqeAuthBtn || !bqeAuthBtnText) {
            return;
        }

        if (data.isAuthorized && data.details) {
            const expiresIn = data.details.expiresIn;
            const minutes = Math.floor(expiresIn / 60);
            const seconds = expiresIn % 60;

            // Update button state
            bqeAuthBtn.classList.add('auth-btn', 'disconnected');
            bqeAuthBtnText.textContent = 'Disconnect BQE';

            // Add null check for filters section
            const filtersSection = document.querySelector('.filters-section');
            if (filtersSection) {
                // Check if we're in BQE mode before showing filters
                const currentDataSource = window.invoiceTable?.currentDataSource || 'bqe';
                if (currentDataSource === 'bqe') {
                    filtersSection.classList.remove('d-none');
                    filtersSection.style.display = '';
                }
            }

            const icon = bqeAuthBtn.querySelector('i');
            if (icon) {
                icon.classList.remove('bi-shield-lock');
                icon.classList.add('bi-shield-x');
            }

            // Update connection status
            const connectionStatus = document.querySelector('.connection-status');
            if (connectionStatus) {
                connectionStatus.classList.remove('not-connected');
                connectionStatus.classList.add('connected');
                connectionStatus.innerHTML = '<i class="bi bi-check-circle"></i><span>Connected</span>';
            }

            // Update connection message
            const connectionMessage = document.querySelector('.connection-status').nextElementSibling;
            if (connectionMessage) {
                connectionMessage.innerHTML = `<small class="text-white-50">Session expires in: ${minutes}m ${seconds}s</small>`;
            }

            // Show success alert only if not shown before
            if (!this.hasShownAuthAlert) {
                Swal.fire({
                    icon: 'success',
                    title: 'Successfully Connected',
                    html: `
                        <div class="text-center">
                            <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
                            <p class="mt-3 mb-2">You have successfully authorized BQE connection!</p>
                            <p class="text-muted">You can now access all BQE features.</p>
                        </div>
                    `,
                    confirmButtonText: 'Got it',
                    confirmButtonColor: '#198754',
                    showClass: {
                        popup: 'animate__animated animate__fadeIn'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOut'
                    },
                    customClass: {
                        popup: 'rounded-4'
                    }
                });
                this.hasShownAuthAlert = true;
            }

            const tooltipContent = this.getAuthorizedTooltipContent(expiresIn, minutes, seconds);
            this.updateTooltip(tooltip, isHovering, bqeAuthBtn, tooltipContent);
        } else {
            // Reset to unauthorized state
            bqeAuthBtn.classList.add('auth-btn');
            bqeAuthBtn.classList.remove('disconnected');
            bqeAuthBtnText.textContent = 'Authorize BQE';

            const icon = bqeAuthBtn.querySelector('i');
            if (icon) {
                icon.classList.remove('bi-shield-x');
                icon.classList.add('bi-shield-lock');
            }

            // Update connection status
            const connectionStatus = document.querySelector('.connection-status');
            if (connectionStatus) {
                connectionStatus.classList.remove('connected');
                connectionStatus.classList.add('not-connected');
                connectionStatus.innerHTML = '<i class="bi bi-x-circle"></i><span>Not Connected</span>';
            }

            // Update connection message
            const connectionMessage = document.querySelector('.connection-status').nextElementSibling;
            if (connectionMessage) {
                connectionMessage.innerHTML = '<small class="text-white-50">Click to establish BQE connection</small>';
            }

            // Show unauthorized alert only if not shown before
            if (!this.hasShownAuthAlert) {
                Swal.fire({
                    icon: 'warning',
                    title: 'BQE Connection Required',
                    html: `
                        <div class="text-center">
                            <i class="bi bi-shield-lock text-warning" style="font-size: 3rem;"></i>
                            <p class="mt-3 mb-2">Your session is not connected to BQE.</p>
                            <p class="text-muted">Please authorize the connection to access BQE features.</p>
                        </div>
                    `,
                    confirmButtonText: 'Got it',
                    confirmButtonColor: '#405189',
                    showClass: {
                        popup: 'animate__animated animate__fadeIn'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOut'
                    },
                    customClass: {
                        popup: 'rounded-4'
                    }
                });
                this.hasShownAuthAlert = true;
            }

            const unauthorizedContent = this.getUnauthorizedTooltipContent();
            this.updateTooltip(tooltip, isHovering, bqeAuthBtn, unauthorizedContent);
        }
    }

    getAuthorizedTooltipContent(expiresIn, minutes, seconds) {
        return `
            <div class=''>

            </div>
        `;
    }
    getUnauthorizedTooltipContent() {
        return `
            <div class=''>

            </div>
        `;
    }

    updateTooltip(tooltip, isHovering, bqeAuthBtn, content) {
        // First dispose of any existing tooltip
        if (tooltip) {
            tooltip.dispose();
        }

        // Create new tooltip
        const newTooltip = new bootstrap.Tooltip(bqeAuthBtn, {
            title: content,
            html: true,
            placement: 'bottom',
            trigger: 'hover',
            template: `
                <div class="tooltip" role="tooltip">
                    <div class="tooltip-arrow"></div>
                    <div class="tooltip-inner"></div>
                </div>
            `
        });

        // If button is being hovered, show the tooltip immediately
        if (isHovering) {
            newTooltip.show();
        }
    }

    async handleAuthClick(event) {
        const button = event.currentTarget;
        const originalContent = button.innerHTML;
        const bqeAuthBtnText = document.getElementById('bqeAuthBtnText');

        if (bqeAuthBtnText.textContent === 'Disconnect BQE') {
            await this.handleDisconnect(button, originalContent);
        } else {
            await this.handleConnect(button, originalContent);
        }
    }

    startAuthStatusCheck() {
        // Initial check
        this.checkAuthStatus();

        // Clear existing interval if any
        if (this.authCheckInterval) {
            clearInterval(this.authCheckInterval);
        }

        // Set new interval with throttle
        this.authCheckInterval = setInterval(() => {
            this.checkAuthStatus();
        }, this.checkThrottle);
    }

    async initializeAuth() {
        try {
            // Hide filters section by default
            const filtersSection = document.querySelector('.filters-section');
            if (filtersSection) {
                filtersSection.classList.add('d-none');
            }

            // Initial auth check
            await this.checkAuthStatus();

            // Start periodic checks
            this.authCheckInterval = setInterval(() => {
                this.checkAuthStatus();
            }, this.checkThrottle);

        } catch (error) {
            console.error('Error initializing auth:', error);
        }
    }

    async handleConnect(button, originalContent) {
        try {
            // Show loading state
            button.disabled = true;
            const loadingIcon = document.createElement('span');
            loadingIcon.className = 'spinner-border spinner-border-sm me-2';
            button.innerHTML = '';
            button.appendChild(loadingIcon);
            button.appendChild(document.createTextNode('Initializing...'));

            // Get the authorization URL
            const response = await fetch('/bqe/auth');
            const data = await response.json();

            if (data.success && data.redirectUrl) {
                // Show success alert
                Swal.fire({
                    icon: 'success',
                    title: 'Authorization Successful',
                    text: 'Successfully initialized BQE authorization',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#198754'
                });

                // Show connecting dialog
                const loadingResult = await Swal.fire({
                    title: 'Connecting to BQE',
                    html: `
                        <div class="text-start">
                            <div class="alert alert-info mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    <div>
                                        <h6 class="mb-1">Authorization Process</h6>
                                        <small>Please wait while we establish a secure connection...</small>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center text-info">
                                <i class="bi bi-shield-check me-2"></i>
                                <small class="status-message">Preparing authentication request...</small>
                            </div>
                            <div class="mt-2">
                                <p class="small text-muted mb-1">
                                    <i class="bi bi-clock-history me-1"></i>
                                    Redirecting in <b class="timer">3</b> seconds
                                </p>
                            </div>
                        </div>
                    `,
                    timer: 3000,
                    timerProgressBar: true,
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                        const timer = Swal.getPopup().querySelector('b.timer');
                        const statusMsg = Swal.getPopup().querySelector('.status-message');

                        const timerInterval = setInterval(() => {
                            const timeLeft = Math.ceil(Swal.getTimerLeft() / 1000);
                            if (timer) timer.textContent = timeLeft;

                            // Update status message
                            if (timeLeft <= 1) {
                                statusMsg.textContent = 'Ready to connect...';
                            } else if (timeLeft <= 2) {
                                statusMsg.textContent = 'Validating security tokens...';
                            }
                        }, 100);

                        Swal.getPopup().addEventListener('close', () => {
                            clearInterval(timerInterval);
                        });
                    },
                    footer: `
                        <div class="alert alert-warning border-warning shadow-sm" role="alert" style="margin-bottom:0; font-size:0.9rem;">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-shield-exclamation me-2" style="font-size:1.2rem;"></i>
                                <div>
                                    <strong>Security Notice:</strong>
                                    <span class="d-block small">BQE Authorization is required to securely access Invoice Data via BQE API.</span>
                                </div>
                            </div>
                        </div>`
                });

                // Redirect to BQE auth page
                window.location.href = data.redirectUrl;
            } else {
                throw new Error(data.error || 'Failed to get authorization URL');
            }
        } catch (error) {
            console.error('Authorization Error:', error);
            // Reset button state
            button.disabled = false;
            button.innerHTML = originalContent;

            Swal.fire({
                icon: 'error',
                title: 'Authorization Failed',
                text: 'Failed to initialize BQE authorization. Please try again.',
                confirmButtonText: 'Close',
                confirmButtonColor: '#dc3545' // Bootstrap danger color
            });
        }
    }

    async handleDisconnect(button, originalContent) {
        try {
            // Show loading state
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Disconnecting...';

            const response = await fetch('/bqe/disconnect', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                // Dispose of any existing tooltip
                const tooltip = bootstrap.Tooltip.getInstance(button);
                if (tooltip) {
                    tooltip.dispose();
                }

                Swal.fire({
                    icon: 'success',
                    title: 'Disconnected',
                    text: 'Successfully disconnected from BQE',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#198754'
                });

                // Reset button state
                button.classList.add('auth-btn');
                button.classList.remove('disconnected');

                // Check if element exists before accessing
                const bqeAuthBtnText = document.getElementById('bqeAuthBtnText');
                if (bqeAuthBtnText) {
                    bqeAuthBtnText.textContent = 'Authorize BQE';
                }

                const icon = button.querySelector('i');
                if (icon) {
                    icon.classList.remove('bi-shield-x');
                    icon.classList.add('bi-shield-lock');
                }

                // Update connection status
                const connectionStatus = document.querySelector('.connection-status');
                if (connectionStatus) {
                    connectionStatus.classList.remove('connected');
                    connectionStatus.classList.add('not-connected');
                    connectionStatus.innerHTML = '<i class="bi bi-x-circle"></i><span>Not Connected</span>';
                }

                // Update connection message
                const connectionMessage = document.querySelector('.connection-status').nextElementSibling;
                if (connectionMessage) {
                    connectionMessage.innerHTML = '<small class="text-white-50">Click to establish BQE connection</small>';
                }

                // Initialize new tooltip with unauthorized content
                new bootstrap.Tooltip(button, {
                    title: this.getUnauthorizedTooltipContent(),
                    html: true,
                    placement: 'bottom',
                    trigger: 'hover'
                });

                // Hide the filters section if visible
                const filtersSection = document.querySelector('.filters-section');
                if (filtersSection) {
                    filtersSection.classList.add('d-none');
                }

                // Reset session info display
                const sessionInfo = document.getElementById('bqeSessionInfo');
                if (sessionInfo) {
                    sessionInfo.classList.add('d-none');
                }

            } else {
                throw new Error(data.error || 'Failed to disconnect');
            }
        } catch (error) {
            console.error('Disconnect Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Disconnect Failed',
                text: 'Failed to disconnect from BQE. Please try again.',
                confirmButtonText: 'Close',
                confirmButtonColor: '#dc3545'
            });
        } finally {
            // Reset button state
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    }

    // Add cleanup method
    cleanup() {
        if (this.authCheckInterval) {
            clearInterval(this.authCheckInterval);
            this.authCheckInterval = null;
        }
    }
}