const fetchRecentDocuments = require('../utils/fetchRecentDocuments');
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 60 });

const getCachedDocuments = async (req) => {
    const cacheKey = 'recentDocuments';
    
    let data = cache.get(cacheKey);
  
    if (!data) {
      // If data is not in the cache, fetch it from the source
      data = await fetchRecentDocuments(req);
      
      // Store the fetched data in the cache
      cache.set(cacheKey, data);
      
      console.log('Fetched documents and cached the result');
    } else {
      console.log('Serving documents from cache');
    }
  
    return data;
  };

module.exports = {
    getCachedDocuments
};
