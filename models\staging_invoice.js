'use strict';
module.exports = (sequelize, DataTypes) => {
  const StagingInvoice = sequelize.define('StagingInvoice', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    bqe_invoice_id: {
      type: DataTypes.STRING(255),
      unique: true,
      allowNull: false
    },
    invoice_number: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'Pending'
    },
    date_submitted: {
      type: DataTypes.DATE,
      allowNull: true
    },
    date_sync: {
      type: DataTypes.DATE,
      allowNull: true
    },
    date_cancelled: {
      type: DataTypes.DATE,
      allowNull: true
    },
    cancelled_by: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    cancellation_reason: {
      type: DataTypes.STRING(500),
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.fn('GETDATE')
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.fn('GETDATE')
    }
  }, {
    tableName: 'staging_invoices',
    timestamps: false
  });

  return StagingInvoice;
};