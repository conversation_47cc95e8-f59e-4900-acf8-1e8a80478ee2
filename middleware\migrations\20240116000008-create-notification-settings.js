'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notification_settings', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WP_USER_REGISTRATION',
          key: 'ID'
        }
      },
      notificationEmail: {
        type: Sequelize.STRING,
        allowNull: true
      },
      emailNewInvoice: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      emailStatusUpdate: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      emailPaymentReceived: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      emailDailyDigest: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      browserNotifications: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      soundNotifications: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      alertDuration: {
        type: Sequelize.INTEGER,
        defaultValue: 5
      },
      mobileNumber: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      mobileVerified: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      mobileVerificationCode: {
        type: Sequelize.STRING(6),
        allowNull: true
      },
      mobileVerificationExpires: {
        type: Sequelize.DATE,
        allowNull: true
      },
      smsNotifications: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      pushNotifications: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      quietHoursStart: {
        type: Sequelize.TIME,
        defaultValue: '22:00:00'
      },
      quietHoursEnd: {
        type: Sequelize.TIME,
        defaultValue: '08:00:00'
      },
      timezone: {
        type: Sequelize.STRING(50),
        defaultValue: 'Asia/Kuala_Lumpur'
      },
      workdaysOnly: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });

    // Add unique constraint on userId
    await queryInterface.addConstraint('notification_settings', {
      fields: ['userId'],
      type: 'unique'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notification_settings');
  }
}; 