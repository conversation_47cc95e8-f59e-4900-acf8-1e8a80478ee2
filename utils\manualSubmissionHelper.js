/**
 * Manual Submission Helper Utility
 * Provides helper functions for manual Excel file submission to LHDN
 * Consolidates common functionality used across manual submission workflows
 */

const moment = require('moment');
const path = require('path');
const fs = require('fs');

class ManualSubmissionHelper {
    /**
     * Validate manual submission parameters
     * @param {Object} params - Submission parameters
     * @returns {Object} Validation result
     */
    static validateSubmissionParams(params) {
        const { fileName, type, company, date, version } = params;

        const validationErrors = [];

        if (!fileName || typeof fileName !== 'string') {
            validationErrors.push('fileName is required and must be a string');
        }

        if (!type || typeof type !== 'string') {
            validationErrors.push('type is required and must be a string');
        }

        if (!company || typeof company !== 'string') {
            validationErrors.push('company is required and must be a string');
        }

        if (!date) {
            validationErrors.push('date is required');
        } else if (!moment(date).isValid()) {
            validationErrors.push('date must be a valid date');
        }

        if (!version || !['1.0', '1.1'].includes(version)) {
            validationErrors.push('version must be either "1.0" or "1.1"');
        }

        return {
            isValid: validationErrors.length === 0,
            errors: validationErrors
        };
    }

    /**
     * Extract document number from filename
     * @param {string} fileName - Excel filename
     * @returns {string} Document number
     */
    static extractDocumentNumber(fileName) {
        const match = fileName.match(/^(?:\d{2})_([^_]+)_/);
        return match ? match[1] : fileName;
    }

    /**
     * Generate consolidated file paths for manual submissions
     * @param {Object} params - Path parameters
     * @returns {Object} File paths
     */
    static generateConsolidatedPaths(params) {
        const { fileName, company, date, type } = params;
        const formattedDate = moment(date).format('YYYY-MM-DD');

        // Outgoing paths for consolidated processing
        const outgoingBasePath = path.join('C:\\SFTPRoot_Consolidation\\Outgoing', company, formattedDate);
        const outgoingFilePath = path.join(outgoingBasePath, fileName);

        // JSON file paths
        const baseFileName = fileName.replace(/\.xlsx?$/i, '');
        const jsonFileName = `${baseFileName}_consolidated.json`;
        const jsonFilePath = path.join(outgoingBasePath, jsonFileName);

        // Possible incoming paths to search (in order of preference)
        const possibleIncomingPaths = [
            path.join('C:\\SFTPRoot_Consolidation', 'Incoming', company, formattedDate, fileName),
            path.join('C:\\SFTPRoot_Consolidation', 'Outgoing', company, formattedDate, fileName),
            path.join('C:\\SFTPRoot', 'Incoming', company, formattedDate, fileName),
            path.join('C:\\SFTPRoot', 'Manual', company, formattedDate, fileName),
            path.join('C:\\SFTPRoot', type || 'Manual', company, formattedDate, fileName)
        ];

        return {
            outgoingBasePath,
            outgoingFilePath,
            jsonFilePath,
            possibleIncomingPaths,
            formattedDate
        };
    }

    /**
     * Find the actual file path from possible locations
     * @param {Array} possiblePaths - Array of possible file paths
     * @returns {string|null} Actual file path or null if not found
     */
    static findActualFilePath(possiblePaths) {
        for (const possiblePath of possiblePaths) {
            if (fs.existsSync(possiblePath)) {
                return possiblePath;
            }
        }
        return null;
    }

    /**
     * Create submission metadata for consolidated processing
     * @param {Object} params - Metadata parameters
     * @returns {Object} Submission metadata
     */
    static createSubmissionMetadata(params) {
        const {
            fileName,
            type,
            company,
            date,
            version,
            uuid,
            invoiceNumber,
            processedData
        } = params;

        return {
            fileName,
            type,
            company,
            date: moment(date).format('YYYY-MM-DD'),
            version,
            uuid,
            invoiceNumber,
            consolidatedProcessing: true,
            processedAt: new Date().toISOString(),
            documentsCount: Array.isArray(processedData) ? processedData.length : 1,
            submissionType: 'manual'
        };
    }

    /**
     * Generate JSON content for consolidated submissions
     * @param {Object} params - JSON content parameters
     * @returns {Object} JSON content
     */
    static generateConsolidatedJsonContent(params) {
        const {
            date,
            processedData,
            invoiceCodeNumber,
            originalInvoiceNumber,
            uuid
        } = params;

        return {
            "issueDate": moment(date).format('YYYY-MM-DD'),
            "issueTime": new Date().toISOString().split('T')[1].split('.')[0] + 'Z',
            "invoiceTypeCode": processedData[0]?.invoiceType || processedData[0]?.header?.invoiceType || "01",
            "invoiceNo": invoiceCodeNumber || originalInvoiceNumber,
            "originalInvoiceNo": originalInvoiceNumber,
            "uuid": uuid,
            "consolidatedProcessing": true,
            "processedAt": new Date().toISOString(),
            "documentsCount": processedData.length
        };
    }

    /**
     * Format error response for manual submissions
     * @param {Error} error - Error object
     * @param {string} stage - Stage where error occurred
     * @returns {Object} Formatted error response
     */
    static formatErrorResponse(error, stage = 'submission') {
        return {
            success: false,
            error: {
                code: 'MANUAL_SUBMISSION_ERROR',
                message: error.message || 'An error occurred during manual submission',
                stage,
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * Format success response for manual submissions
     * @param {Object} data - Success data
     * @returns {Object} Formatted success response
     */
    static formatSuccessResponse(data) {
        return {
            success: true,
            data: {
                ...data,
                timestamp: new Date().toISOString(),
                submissionType: 'manual'
            }
        };
    }

    /**
     * Log manual submission activity
     * @param {string} message - Log message
     * @param {Object} details - Additional details
     * @param {string} level - Log level (info, error, warn)
     */
    static logActivity(message, details = {}, level = 'info') {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            module: 'ManualSubmission',
            ...details
        };

        console.log(`[${timestamp}] [${level.toUpperCase()}] [ManualSubmission] ${message}`, details);

        // You can extend this to write to a log file if needed
        return logEntry;
    }
}

module.exports = ManualSubmissionHelper;
