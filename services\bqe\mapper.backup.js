const fs = require('fs');
const path = require('path');
const { getCertificatesHashedParams } = require('./service');
const moment = require('moment');
/**
 * Logger configuration for mapping process
 */
const createLogger = () => {
  const logs = {
    steps: [],
    mappings: [],
    errors: []
  };

  const logStep = (step, data) => {
    logs.steps.push({
      timestamp: new Date().toISOString(),
      step,
      data
    });
  };

  const logMapping = (section, input, output) => {
    logs.mappings.push({
      timestamp: new Date().toISOString(),
      section,
      input,
      output
    });
  };

  const logError = (error, context) => {
    logs.errors.push({
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      context
    });
  };

  const writeLogs = (invoiceNo, lhdnFormat) => {
    try {
      const logsDir = path.join(process.cwd(), 'logs', 'lhdn');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }

      // Sanitize invoice number for filenames
      const safeInvoiceNo = invoiceNo.replace(/[\/\\:*?"<>|]/g, '_');

      // Write processing logs
      const processLogFileName = `lhdn_process_${safeInvoiceNo}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      const processLogPath = path.join(logsDir, processLogFileName);
      fs.writeFileSync(processLogPath, JSON.stringify(logs, null, 2));
      console.log(`[INFO] LHDN Processing logs written to: ${processLogPath}`);

      // Write LHDN format JSON
      const lhdnFileName = `lhdn_output_${safeInvoiceNo}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      const lhdnPath = path.join(logsDir, lhdnFileName);
      fs.writeFileSync(lhdnPath, JSON.stringify(lhdnFormat, null, 2));
      console.log(`[INFO] LHDN Output JSON written to: ${lhdnPath}`);
    } catch (error) {
      console.error('[ERROR] Failed to write LHDN logs:', error);
    }
  };

  return {
    logStep,
    logMapping,
    logError,
    writeLogs,
    getLogs: () => logs
  };
};

// Helper functions
const convertToBoolean = (value) => {
  if (value === true || value === 'true' || value === 1) return true;
  if (value === false || value === 'false' || value === 0) return false;
  return false; // default to false if undefined/null
};

const wrapValue = (value, currencyID = null) => {
  // For currency amounts, keep as numbers or return undefined if invalid
  if (currencyID) {
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      return [{
        "_": numValue,
        "currencyID": currencyID
      }];
    }
    return undefined;
  }

  // For non-currency fields, convert null/undefined to empty string
  if (value === null || value === undefined || value === '') {
    return [{
      "_": ""
    }];
  }

  // Convert everything else to string
  return [{
    "_": String(value)
  }];
};

const wrapBoolean = (value) => {
  return [{
    "_": convertToBoolean(value)
  }];
};

const wrapNumericValue = (value) => {
  if (value === null || value === undefined || value === '') {
    return undefined;
  }
  
  // Convert to number and ensure it's a valid number
  const numValue = Number(value);
  if (isNaN(numValue)) {
    return undefined;
  }
  
  // Format the number to 2 decimal places for consistency
  // This is especially important for tax rates
  return [{
    "_": parseFloat(numValue.toFixed(2))
  }];
};

const formatDateTime = (date) => {
  if (!date) return undefined;
  
  // If date is already a string in correct format, return it
  if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(date)) {
    return date;
  }

  try {
    // Convert to Date object if it isn't already
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      return undefined;
    }
    // Format as ISO string and remove milliseconds and timezone
    return dateObj.toISOString().split('.')[0];
  } catch (error) {
    console.error('Error formatting date:', error);
    return undefined;
  }
};

const mapAddressLines = (line) => {
  if (!line) return undefined;
  
  // Split the address line by commas or line breaks
  const lines = line.split(/[,\n]/).map(l => l.trim()).filter(l => l);
  
  // Ensure we have at least one line
  if (lines.length === 0) {
    return [{ "Line": [{ "_": "NA" }] }];
  }
  
  // Map each line to the required format
  const mappedLines = lines.map(l => ({
    "Line": [{ "_": l }]
  }));
  
  // LHDN requires at least one line
  return mappedLines;
};

const mapAllowanceCharges = (charges) => {
  if (!charges || !Array.isArray(charges)) {
    charges = [charges];
  }
  
  return charges.map(charge => ({
    "ChargeIndicator": wrapBoolean(charge.indicator),
    "AllowanceChargeReason": wrapValue(charge.reason || 'NA'),
    "MultiplierFactorNumeric": charge.multiplierFactorNumeric ? [{
      "_": charge.multiplierFactorNumeric
    }] : undefined,
    "Amount": wrapValue(charge.amount || 0, 'MYR')
  })).filter(c => c);
};

const mapCommodityClassifications = (item) => {
  const classifications = [];
  
  if (item.classification?.code) {
    classifications.push({
      "ItemClassificationCode": [{
        "_": item.classification.code,
        "listID": item.classification.type || 'CLASS'
      }]
    });
  }
  
  // Add PTC classification if exists
  if (item.ptcCode) {
    classifications.push({
      "ItemClassificationCode": [{
        "_": item.ptcCode,
        "listID": "PTC"
      }]
    });
  }
  
  return classifications;
};

const mapPartyIdentifications = (identifications = []) => {
  const requiredTypes = ['TIN', 'BRN', 'SST', 'TTX'];
  
  const idMap = identifications.reduce((acc, id) => {
    if (id && id.schemeId) {
      acc[id.schemeId] = id.id || "NA";
    }
    return acc;
  }, {});

  return requiredTypes.map(schemeId => ({
    "ID": [{
      "_": idMap[schemeId] || "NA",
      "schemeID": schemeId,
    }]
  }));
};

const mapPartyAddress = (address) => {
  if (!address) {
    return {
      "CityName": wrapValue("NA"),
      "PostalZone": wrapValue("NA"),
      "CountrySubentityCode": wrapValue("14"),
      "AddressLine": mapAddressLines("NA"),
      "Country": [{
        "IdentificationCode": [{
          "_": "MYS",
          "listID": "ISO3166-1",
          "listAgencyID": "6"
        }]
      }]
    };
  }
  
  // Use formattedAddress if available, otherwise use line
  const addressLine = address.formattedAddress || address.line || "";
  
  return {
    "CityName": wrapValue(address.city || "NA"),
    "PostalZone": wrapValue(address.postcode || "NA"),
    "CountrySubentityCode": wrapValue(address.state || "14"),
    "AddressLine": mapAddressLines(addressLine),
    "Country": [{
      "IdentificationCode": [{
        "_": "MYS",
        "listID": "ISO3166-1",
        "listAgencyID": "6"
      }]
    }]
  };
};

const DEFAULT_VALUES = {
  TAX_SCHEME: {
    id: 'OTH',
    schemeId: 'UN/ECE 5153',
    schemeAgencyId: '6'
  },
  TAX_CATEGORY: {
    id: '01',
    exemptionReason: ''
  }
};

const mapTaxScheme = (scheme) => {
  // Define valid LHDN tax type codes
  const validTaxTypes = {
    '01': 'Sales Tax',
    '02': 'Service Tax',
    '03': 'Tourism Tax',
    '04': 'High-Value Goods Tax',
    '05': 'Sales Tax on Low Value Goods',
    '06': 'Not Applicable',
    'E': 'Tax Exemption'
  };

  // Get the tax type from the scheme or default to '02' (Service Tax)
  let taxType = scheme?.id || '02';
  
  // If tax type is 'OTH' or invalid, map to appropriate type
  if (taxType === 'OTH' || !validTaxTypes[taxType]) {
    taxType = '02'; // Default to Service Tax
  }

  return [{
    "ID": [{
      "_": taxType,
      "schemeID": "UN/ECE 5153",
      "schemeAgencyID": "6"
    }]
  }];
};

const mapTaxCategory = (taxCategory, taxScheme) => {
  // Define valid LHDN tax type codes
  const validTaxTypes = {
    '01': 'Sales Tax',
    '02': 'Service Tax',
    '03': 'Tourism Tax',
    '04': 'High-Value Goods Tax',
    '05': 'Sales Tax on Low Value Goods',
    '06': 'Not Applicable',
    'E': 'Tax Exemption'
  };

  // Get the tax type from the category or default to '02' (Service Tax)
  let taxType = taxCategory?.id || '02';
  
  // If tax type is invalid, map to appropriate type
  if (!validTaxTypes[taxType]) {
    taxType = '02'; // Default to Service Tax
  }

  return [{
    "ID": wrapValue(taxType),
    "TaxExemptionReason": taxCategory?.exemptionReason ? wrapValue(taxCategory.exemptionReason) : undefined,
    "TaxScheme": mapTaxScheme({ id: taxType })
  }];
};

const mapTaxTotal = (taxTotal, currency) => {
  if (!taxTotal) return [];

  return [{
      "TaxAmount": wrapValue(taxTotal.taxAmount || 0, currency),
      "TaxSubtotal": taxTotal.taxSubtotal?.map(subtotal => ({
          "TaxableAmount": wrapValue(subtotal.taxableAmount || 0, currency),
          "TaxAmount": wrapValue(subtotal.taxAmount || 0, currency),
          "TaxCategory": [{
              "ID": [{
                  "_": subtotal.taxCategory?.id || DEFAULT_VALUES.TAX_CATEGORY.id
              }],
              "Percent": wrapNumericValue(subtotal.taxCategory?.percent || 0),
              "TaxExemptionReason": subtotal.taxCategory?.exemptionReason ? 
                  wrapValue(subtotal.taxCategory.exemptionReason) : undefined,
              "TaxScheme": [{
                  "ID": [{
                      "_": "OTH", 
                      "schemeID": "UN/ECE 5153",
                      "schemeAgencyID": "6"
                  }]
              }]
          }]
      })) || []
  }];
};

const mapLineItem = (item, currency, index) => {
  if (!item) return null;

  // Get tax information
  const taxInfo = item.tax || {};
  
  // Determine tax status based on tax types
  const isTaxExempt = taxInfo.isExempted || 
                     (taxInfo.types && taxInfo.types.some(t => t.type === 'E' || t.type === '06'));
  
  // Calculate tax amount based on rate and amount
  const itemAmount = item.amount || 0;
  const taxRate = item.tax?.originalRate || 0;
  const taxAmount = isTaxExempt ? 0 : (itemAmount * taxRate / 100);

  // Ensure we have at least one tax type
  const taxTypes = (taxInfo.types && taxInfo.types.length > 0) ? 
    taxInfo.types : 
    [{
      type: isTaxExempt ? '06' : '02', // Default to Service Tax if not exempt
      rate: taxRate,
      originalRate: taxRate,
      amount: taxAmount
    }];
                  
  // Get classification code from item data
  const classificationCode = item.classifications?.invoice || 
                            item.classificationCode || 
                            "";
                            
  // Get unit code from item data
  const unitCode = item.unitCode || "EA";
  
  // Get item description from item data
  const itemDescription = item.description || "NA";

  return {
    "ID": wrapValue(index + 1),
    "InvoicedQuantity": [{
      "_": item.quantity || 1,
      "unitCode": unitCode
    }],
    "LineExtensionAmount": wrapValue(item.amount || 0, currency),
    "TaxTotal": [{
      "TaxAmount": wrapValue(taxAmount, currency),
      "TaxSubtotal": taxTypes.map(tax => {
        const isThisTaxExempt = tax.type === 'E' || tax.type === '06';
        const lineItemTaxAmount = isThisTaxExempt ? 0 : 
            ((item.amount || 0) * (tax.originalRate || 8.00) / 100);
        
        return {
          "TaxableAmount": wrapValue(item.amount || 0, currency),
          "TaxAmount": wrapValue(lineItemTaxAmount, currency),
          "TaxCategory": [{
            "ID": wrapValue(tax.type || '02'),
            "Percent": wrapNumericValue(tax.originalRate || 8.00),
            "TaxExemptionReason": isThisTaxExempt ? 
                wrapValue(taxInfo.exemption || "Not Applicable") : 
                undefined,
            "TaxScheme": [{
              "ID": [{
                "_": "OTH",
                "schemeID": "UN/ECE 5153",
                "schemeAgencyID": "6"
              }]
            }]
          }]
        };
      })
    }],
    "Item": [{
      "CommodityClassification": [
        {
          "ItemClassificationCode": [{
            "_": classificationCode,
            "listID": "CLASS"
          }]
        },
      ],
      "Description": wrapValue(itemDescription),
      "OriginCountry": [{
        "IdentificationCode": [{
          "_": item.originCountry || "MYS",
          "listID": "ISO3166-1",
          "listAgencyID": "6"
        }]
      }]
    }],
    "Price": [{
      "PriceAmount": wrapValue((item.amount || 0) - taxAmount, currency),
      "BaseQuantity": [{
        "_": item.quantity || 1,
        "unitCode": unitCode
      }]
    }]
  };
};

const mapInvoiceLines = (lineItems, currency = 'MYR') => {
  if (!lineItems || !Array.isArray(lineItems)) {
    return [];
  }
  return lineItems.map((item, index) => mapLineItem(item, currency, index)).filter(Boolean);
};

// helper function  before mapToLHDNFormat
const cleanObject = (obj) => {
    if (!obj) return obj;
    
    if (Array.isArray(obj)) {
        return obj
            .map(item => cleanObject(item))
            .filter(item => item !== null && item !== undefined);
    }
    
    if (typeof obj === 'object') {
        const cleaned = Object.entries(obj)
            .reduce((acc, [key, value]) => {
                const cleanedValue = cleanObject(value);
                if (cleanedValue !== null && cleanedValue !== undefined) {
                    // Handle empty arrays
                    if (Array.isArray(cleanedValue) && cleanedValue.length === 0) {
                        return acc;
                    }
                    // Handle empty objects
                    if (typeof cleanedValue === 'object' && 
                        !Array.isArray(cleanedValue) && 
                        Object.keys(cleanedValue).length === 0) {
                        return acc;
                    }
                    acc[key] = cleanedValue;
                }
                return acc;
            }, {});
            
        return Object.keys(cleaned).length ? cleaned : undefined;
    }
    
    return obj;
};

// helper functions as well for better data cleaning
const isEmptyValue = (value) => {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string' && value.trim() === '') return true;
    if (Array.isArray(value) && value.length === 0) return true;
    if (typeof value === 'object' && Object.keys(value).length === 0) return true;
    return false;
};

const cleanEmptyFields = (obj) => {
    if (!obj) return obj;
    
    if (Array.isArray(obj)) {
        return obj
            .map(item => cleanEmptyFields(item))
            .filter(item => !isEmptyValue(item));
    }
    
    if (typeof obj === 'object') {
        const cleaned = {};
        for (const [key, value] of Object.entries(obj)) {
            const cleanedValue = cleanEmptyFields(value);
            if (!isEmptyValue(cleanedValue)) {
                cleaned[key] = cleanedValue;
            }
        }
        return cleaned;
    }
    
    return obj;
};

const getFormattedDateTime = () => {
  const currentDate = new Date();
  return {
    date: currentDate.toISOString().split('T')[0],
    time: currentDate.toISOString().split('T')[1].split('.')[0] + 'Z'
  };
};

const mapBQEToLHDNFormat = (bqeData, version) => {
  const logger = createLogger();

  if (!bqeData || !bqeData._rawInvoice) {
    const error = new Error('No document data provided');
    logger.logError(error, { bqeData });
    throw error;
  }

  try {
    const { date, time } = getFormattedDateTime();
    const invoice = bqeData._rawInvoice;
    const supplier = bqeData.supplier || {};
    const projTitle = bqeData.projTitle || ''; 
    const buyer = bqeData.buyer || {};
    const taxInfo = bqeData.tax_info || {
      taxRate: 0,
      taxType: '06',
      taxTypeDescription: 'Not Applicable',
      taxExemption: taxInfo.taxExemption,
      isExempted: false,
      taxableAmount: 0,
      taxAmount: 0
    };
    
    // Safely access invoice details
    const invoiceDetails = bqeData._invoiceDetails && bqeData._invoiceDetails.length > 0 
      ? bqeData._invoiceDetails[0] 
      : null;
      
    // Get payment information from the data
    const paymentInfo = bqeData.payment_info || '';
    
    const paymentMeansCode = invoice.paymentMeansCode || 
                            (invoice.customFields?.find(f => f.label === 'Payment Method')?.value) || 
                            "08";

    logger.logStep('Starting BQE to LHDN mapping', { version });
    
    // Use invoice dates if available, otherwise use current date
    const invoiceFrom = invoice.invoiceFrom 
      ? moment(invoice.invoiceFrom).format('YYYY-MM-DD')
      : bqeData.invoice?.invoiceFrom || date;
      
    const invoiceTo = invoice.invoiceTo
      ? moment(invoice.invoiceTo).format('YYYY-MM-DD')
      : bqeData.invoice?.invoiceTo || date;

    // Ensure version is properly formatted
    let versionValue;
    if (typeof version === 'object' && version !== null) {
      versionValue = version.value || "1.0";
    } else {
      versionValue = version || "1.0";
    }

    // Get tax type and exemption status
    const projectCustomFields = bqeData._projectDetailsArray?.[0]?.details?.customFields || [];
    const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
    const isTaxExempt = taxTypeField?.description?.includes('E');

    // Get tax rate (keep original rate for reference)
    const taxRate = parseFloat(
        invoice.customFields?.find(f => f.label === 'Tax rate')?.value ||
        projectCustomFields.find(f => f.label === 'SERVICE TAX RATE' || f.label === 'Tax Rate')?.value ||
        bqeData._projectDetailsArray?.[0]?.details?.mainServiceTax ||
        invoice.mainServiceTax ||
        8.00
    );

    // Extract monetary values following LHDN SDK mathematical mappings
    const invoiceAmount = parseFloat(invoice.invoiceAmount || 0); // Total Including Tax
    const lineExtensionAmount = invoiceAmount; // Total net amount (for tax exempt)
    const taxableAmount = invoiceAmount; // Total taxable amount per tax type
    const taxAmount = isTaxExempt ? 0 : (taxableAmount * taxRate / 100); // Calculate tax amount for non-exempt cases

    // For line items
    const lineItems = bqeData.line_items || [];
    const mappedLineItems = lineItems.map((item, index) => ({
        ...item,
        lineExtensionAmount: item.amount, // Line level Total Excluding Tax
        taxableAmount: item.amount, // Line level taxable amount
        taxAmount: isTaxExempt ? 0 : (item.amount * taxRate / 100) // Line level tax amount (calculate for non-exempt)
    }));

    console.log('Monetary values following LHDN mappings:', {
        invoiceAmount,
        lineExtensionAmount,
        taxableAmount,
        taxAmount,
        taxRate,
        isTaxExempt,
        taxType: taxTypeField?.description
    });

    // Map tax type according to LHDN SDK API Tax Types
    // Ensure tax type is properly formatted (must be 2 characters, except for 'E')
    let taxType = taxTypeField?.description?.includes('E') ? 'E' : '02';
    
    // Validate and map tax type
    if (taxType.length !== 2 && taxType !== 'E') {
      // Map to appropriate tax type or default to service tax
      if (taxType === 'S') taxType = '02'; // Service Tax
      else if (taxType === 'G') taxType = '01'; // Sales Tax
      else if (taxType === 'T') taxType = '03'; // Tourism Tax
      else if (taxType === 'H') taxType = '04'; // High-Value Goods Tax
      else if (taxType === 'L') taxType = '05'; // Low Value Goods Tax
      else if (taxType === 'N' || taxType === 'NA') taxType = '06'; // Not Applicable
      else if (taxType === 'OTH') taxType = '06'; // Map OTH to Not Applicable
      else if (taxInfo.isExempted) taxType = 'E'; // Tax exemption
      else taxType = '02'; // Default to Service Tax
    }

    // Get tax type description based on code
    const taxTypeDescriptions = {
      '01': 'Sales Tax',
      '02': 'Service Tax',
      '03': 'Tourism Tax',
      '04': 'High-Value Goods Tax',
      '05': 'Sales Tax on Low Value Goods',
      '06': 'Not Applicable',
      'E': 'Tax Exemption'
    };
    
    const taxTypeDescription = taxTypeDescriptions[taxType] || 'Not Applicable';

    // Update tax category following LHDN format
    const taxCategory = {
        "ID": wrapValue(taxType), // Use determined tax type rather than hardcoding 'E'
        "Percent": wrapNumericValue(taxRate), // Keep original rate for reference
        "TaxExemptionReason": isTaxExempt ? wrapValue(
            projectCustomFields.find(f => f.label === 'Details of Tax Exemption')?.description || 
            "Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018"
        ) : undefined,
        "TaxScheme": [{
            "ID": [{
                "_": "OTH",
                "schemeID": "UN/ECE 5153",
                "schemeAgencyID": "6"
            }]
        }]
    };
    
    // Get document type from invoice data
    const documentType = invoice.documentType || 
                        (invoice.customFields?.find(f => f.label === 'Document Type')?.value) || 
                        "Project Title";
    const attentionType = "Attention";
    const attentionDescription = invoice.attention || 
                            (invoice._clientDetails?.firstName && invoice._clientDetails?.lastName ? 
                                `${invoice._clientDetails.firstName} ${invoice._clientDetails.lastName}`.trim() : 
                                invoice._clientDetails?.name) || 
                            "NA";

    // Get invoice message from invoice data
    const invoiceMessage = invoice.messageOnInvoice || 
                          (invoice.customFields?.find(f => f.label === 'Message on Invoice')?.value) || 
                          "";

    // Update TaxTotal section following LHDN mathematical mappings
    const taxTotal = [{
        "TaxAmount": wrapValue(taxAmount, invoice.currency || 'MYR'),
        "TaxSubtotal": [{
            "TaxableAmount": wrapValue(taxableAmount, invoice.currency || 'MYR'),
            "TaxAmount": wrapValue(taxAmount, invoice.currency || 'MYR'),
            "TaxCategory": [taxCategory]
        }]
    }];

    // Update LegalMonetaryTotal following LHDN mathematical mappings
    const legalMonetaryTotal = [{
        "LineExtensionAmount": wrapValue(lineExtensionAmount, invoice.currency || 'MYR'),
        "TaxExclusiveAmount": wrapValue(taxableAmount, invoice.currency || 'MYR'),
        "TaxInclusiveAmount": wrapValue(invoiceAmount, invoice.currency || 'MYR'),
        "PayableAmount": wrapValue(invoiceAmount, invoice.currency || 'MYR')
    }];

    const lhdnFormat = {
      "_D": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
      "_A": "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
      "_B": "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
      "Invoice": [{
        "ID": wrapValue(invoice.invoiceNumber),
        "IssueDate": [{
          "_": date
        }],
        "IssueTime": [{
          "_": time
        }],
        "InvoiceTypeCode": [{
          "_": invoice.EinvoiceTypeCode || '01',
          "listVersionID": versionValue
        }],
        "AdditionalDocumentReference": [{
          "ID": wrapValue(invoice.invoiceNumber),
          "DocumentType": wrapValue(documentType),
          "DocumentDescription": wrapValue(projTitle),
        },{
          "ID": wrapValue(invoice.invoiceNumber),
          "DocumentType": wrapValue(attentionType),
          "DocumentDescription": wrapValue(attentionDescription),
        }],
        "DocumentCurrencyCode": wrapValue(invoice.currency || 'MYR'),
        "TaxCurrencyCode": wrapValue(invoice.currency || 'MYR'),
        "InvoicePeriod": [{
          "StartDate": wrapValue(invoiceFrom),
          "EndDate": wrapValue(invoiceTo),
          "Description": wrapValue(invoice.periodDescription || "")
        }],
        "AccountingSupplierParty": [{
          "AdditionalAccountID": [{
            "_": supplier.certExId || "",
            "schemeAgencyName": "CertEX"
          }],
          "Party": [{
            "IndustryClassificationCode": [{
              "_": supplier.msicCode || "",
              "name": supplier.businessActivity || ""
            }],
            "PartyIdentification": mapPartyIdentifications([
              { id: supplier.tin, schemeId: 'TIN' },
              { id: supplier.registrationNumber, schemeId: 'BRN' },
              { id: supplier.sstId, schemeId: 'SST' },
              { id: supplier.ttxId, schemeId: 'TTX' }
            ]),
            "PostalAddress": [mapPartyAddress(supplier.address)],
            "PartyLegalEntity": [{
              "RegistrationName": wrapValue(supplier.name || "")
            }],
            "Contact": [{
              "Telephone": wrapValue(supplier.phone || ""),
              "ElectronicMail": wrapValue(supplier.email || "")
            }]
          }]
        }],
        "AccountingCustomerParty": [{
          "Party": [{
            "PartyIdentification": mapPartyIdentifications([
              { id: buyer.tin, schemeId: 'TIN' },
              { id: buyer.registrationNumber, schemeId: 'BRN' },
              { id: buyer.sstId, schemeId: 'SST' },
              { id: buyer.ttxId, schemeId: 'TTX' }
            ]),
            "PostalAddress": [mapPartyAddress(buyer.address)],
            "PartyLegalEntity": [{
              "RegistrationName": wrapValue(buyer.name || "")
            }],
            "Contact": [{
              "Telephone": wrapValue(buyer.phone || ""),
              "ElectronicMail": wrapValue(buyer.email || "")
            }]
          }]
        }],
        "PaymentMeans": [{
          "PaymentMeansCode": wrapValue(paymentMeansCode),
          "PayeeFinancialAccount": [{
            "ID": wrapValue(paymentInfo || "")
          }]
        }],
        "PaymentTerms": [{
          "Note": wrapValue(invoiceMessage)
        }],
        "TaxTotal": taxTotal,
        "LegalMonetaryTotal": legalMonetaryTotal,
        "InvoiceLine": bqeData.line_items && bqeData.line_items.length > 0 
          ? mapInvoiceLines(bqeData.line_items, invoice.currency || 'MYR')
          : [{
              "ID": wrapValue("1"),
              "InvoicedQuantity": [{
                "_": 1,
                "unitCode": invoice.unitCode || "EA" // Use data from invoice
              }],
              "LineExtensionAmount": wrapValue(lineExtensionAmount, invoice.currency || 'MYR'),
              "TaxTotal": [{
                "TaxAmount": wrapValue(taxAmount, invoice.currency || 'MYR'),
                "TaxSubtotal": [{
                  "TaxableAmount": wrapValue(taxableAmount, invoice.currency || 'MYR'),
                  "TaxAmount": wrapValue(taxAmount, invoice.currency || 'MYR'),
                  "TaxCategory": [taxCategory]
                }]
              }],
              "Item": [{
                "CommodityClassification": [{
                  "ItemClassificationCode": [{
                    "_": invoiceDetails?.classifications?.invoice || 
                         invoice.classificationCode || 
                         (invoice.customFields?.find(f => f.label === 'Classification Code')?.value) || 
                         "",
                    "listID": "CLASS"
                  }]
                }],
                "Description": wrapValue(invoiceDetails?.description || 
                                        invoice.description || 
                                        (invoice.customFields?.find(f => f.label === 'Description')?.value) || 
                                        ""),
                "OriginCountry": [{
                  "IdentificationCode": [{
                    "_": invoice.originCountry || supplier.countryCode || "MYS",
                    "listID": "ISO3166-1",
                    "listAgencyID": "6"
                  }]
                }]
              }],
              "Price": [{
                "PriceAmount": wrapValue(lineExtensionAmount, invoice.currency || 'MYR'),
              }]
            }]
      }],
    };

    // Clean up the format by removing undefined values
    const cleanedFormat = cleanObject(lhdnFormat);

    // Write logs
    logger.writeLogs(invoice.invoiceNumber, cleanedFormat);
    console.log('Mapped to LHDN format:', cleanedFormat);

    return cleanedFormat;
  } catch (error) {
    logger.logError(error, { bqeData });
    console.error('Error mapping BQE to LHDN format:', error);
    throw error;
  }
};


module.exports = {
  mapBQEToLHDNFormat,
  cleanObject,
  cleanEmptyFields,
  isEmptyValue
};