class InvoiceTableManager {
    constructor() {
        this.authManager = new BQEAuthManager(); // Use BQEAuthManager instead of AuthManager
        this.tooltips = new Set(); // Track active tooltips
        this._currentInvoice = null; // Add this line to store current invoice
        
        // Bind methods to preserve 'this' context
        this.initializeTable = this.initializeTable.bind(this);
        this.initializeEventListeners = this.initializeEventListeners.bind(this);
        this.refreshInvoiceTable = this.refreshInvoiceTable.bind(this);
        this.submitToLHDN = this.submitToLHDN.bind(this);
        this.updateTotalInvoicesFetched = this.updateTotalInvoicesFetched.bind(this);
        this.updateCardCounts = this.updateCardCounts.bind(this);
        this.isTableInitialized = this.isTableInitialized.bind(this);
        this.saveInvoicesInBatches = this.saveInvoicesInBatches.bind(this);
        this.formatRelativeTime = this.formatRelativeTime.bind(this);
        
        // Initialize immediately instead of using requestAnimationFrame
        this.initializeTable();
        this.initializeEventListeners();

        this.cardCounts = {
            total: 0,
            submitted: 0,
            pending: 0,
            cancelled: 0
        };

        this.loadingDotsInterval = null;
        this.currentDots = '';
    }

    initializeTable() {
        try {
            console.log('Initializing table...');
            const table = $('#reportsTable');

           
            if (!table.length) {

                
                throw new Error('Table element not found');
            }

            console.log('Creating new DataTable instance...');
            this.dataTable = table.DataTable({
                responsive: false,
                scrollX: false,
                processing: true,
                deferRender: true,
                pageLength: 10,
                columns: [
                    { 
                        data: 'checkbox',
                        orderable: false,
                        render: function() {
                            return '<div class="form-check"><input type="checkbox" class="form-check-input row-checkbox"></div>';
                        }
                    },
                    { 
                        data: 'invoice_number',
                        width: '160px',
                        render: function(data) {
                            return `<span class="badge-invoice" data-bs-toggle="tooltip" title="Invoice Number: ${data}">${data}</span>`;
                        }
                    },
                    { 
                        data: 'type',
                        render: function(data, type, row) {
                            // Add null check and default value
                            const typeValue = data || 'Invoice';
                            const typeClass = typeValue.toLowerCase().replace(/\s+/g, '-');
                            return `<span class="badge-type ${typeClass}" data-bs-toggle="tooltip" title="Document Type: ${typeValue}">${typeValue}</span>`;
                        }
                    },
                    { 
                        data: 'customer_name',
                        render: function(data, type, row) {
                            const details = row._clientDetails || {};
                            // Get values from custom fields if available
                            const customFieldsMap = {};
                            if (details.customFields?.length) {
                                details.customFields.forEach(field => {
                                    customFieldsMap[field.label] = field.value;
                                });
                            }

                            const tooltipContent = `
                                <div class="client-tooltip">
                                    <div><strong>Client:</strong> ${details.name || 'NA'}</div>
                                    <div><strong>Company:</strong> ${details.company || 'NA'}</div>
                                    <div><strong>Tax ID:</strong> ${customFieldsMap["BUYER'S TAX ID"] || details.taxId || 'NA'}</div>
                                    <div><strong>Reg No:</strong> ${customFieldsMap["Buyer's Registration No"] || details.registrationNo || 'NA'}</div>
                                    <div><strong>Address:</strong> ${details.address?.formattedAddress || 'NA'}</div>
                                </div>
                            `;
                            return `<span class="customer-name" data-bs-toggle="tooltip" data-bs-html="true" title="${tooltipContent.replace(/"/g, '&quot;')}">${data}</span>`;
                        }
                    },
                    { 
                        data: 'bqe_date',
                        render: function(data) {
                            // Format BQE date with tooltip showing full date/time
                            if (!data) return '-';
                            const formattedDate = moment(data).format('MM-DD-YYYY');
                            const fullDateTime = moment(data).format('YYYY-MM-DD HH:mm:ss');
                            const textFormat = moment(data).format('dddd, MMMM D, YYYY');
                            return `
                                <div>
                                    <span data-bs-toggle="tooltip" title="Full Date: ${fullDateTime}">${formattedDate}</span>
                                    <div class="text-muted small">${textFormat}</div>
                                </div>
                            `;
                        }
                    },
                    { 
                        data: 'date_sync',
                        title: 'Sync Time',
                        render: (data, type, row) => {
                            if (type === 'display') {
                                if (!data) return '-';
                                return this.formatRelativeTime(data);
                            }
                            return data;
                        }
                    },
                    { 
                        data: 'date_submitted',
                        render: function(data, type, row) {
                            if (type === 'display') {
                                if (row.status === 'Submitted' && data) {
                                    // Convert SQL timestamp to local date by removing Z suffix
                                    const localDate = moment(data.replace('Z', ''));
                                    const formattedDate = localDate.format('MM-DD-YYYY');
                                    const fullDateTime = localDate.format('YYYY-MM-DD HH:mm:ss');
                                    const textFormat = localDate.format('dddd, MMMM D, YYYY');
                                    const timeFormat = localDate.format('hh:mm:ss A'); // Changed to 12-hour format with AM/PM
                                    return `
                                        <div>
                                            <span data-bs-toggle="tooltip" title="Full Date: ${fullDateTime}">${formattedDate}</span>
                                            <div class="text-muted small">${textFormat}</div>
                                            <div class="text-muted small">${timeFormat}</div>
                                        </div>
                                    `;
                                }
                                return '-';
                            }
                            return data;
                        }
                    },
                    { 
                        data: null, // Change from 'cancellation_period' to null since we calculate it
                        render: (data, type, row) => {
                            // Only render for display type to prevent multiple calls
                            if (type !== 'display') {
                                return '';
                            }

                            // For cancelled or pending status, show Not Applicable
                            if (row.status === 'Cancelled' || row.status === 'Pending') {
                                return `<span class="badge-cancellation not-applicable">
                                    <i class="bi bi-dash-circle"></i>
                                    Not Applicable
                                </span>`;
                            }

                            // For submitted status, show cancellation countdown
                            if (row.status === 'Submitted' && row.date_submitted) {
                                const time = InvoiceHelper.calculateCancellationTime(row.date_submitted, row.status);
                                row.cancellationTime = time.text;
                                row.cancellationStatus = time.expired ? 'expired' : time.isUrgent ? 'urgent' : 'success';

                                if (time.expired) {
                                    return `<span class="badge-cancellation expired">
                                        <i class="bi bi-x-circle"></i>
                                        Expired
                                    </span>`;
                                }

                                if (time.isUrgent) {
                                    return `<span class="badge-cancellation urgent">
                                        <i class="bi bi-clock-fill"></i>
                                        ${time.text}
                                    </span>`;
                                }

                                return `<span class="badge-cancellation success">
                                    <i class="bi bi-clock me-1"></i>
                                    ${time.text}
                                </span>`;
                            }

                            // For any other status, show Not Applicable
                            return `<span class="badge-cancellation not-applicable">
                                <i class="bi bi-dash-circle"></i>
                                Not Applicable
                            </span>`;
                        }
                    },
                    { 
                        data: 'status',
                        render: function(data, type, row) {
                            if (type === 'display') {
                                const status = data || 'Pending';
                                const statusClass = status.toLowerCase();
                                const customClass = status.toLowerCase() === 'cancelled' ? 'cancelled' : statusClass;
                                
                                // Add icon based on status
                                let icon = '';
                                switch(status.toLowerCase()) {
                                    case 'pending':
                                        icon = '<i class="bi bi-hourglass-split me-1"></i>';
                                        break;
                                    case 'submitted':
                                        icon = '<i class="bi bi-check-circle me-1"></i>';
                                        break;
                                    case 'cancelled':
                                        icon = '<i class="bi bi-x-circle me-1"></i>';
                                        break;
                                }
                                
                                return `<span class="status-badge ${customClass}">${icon}${status}</span>`;
                            }
                            return data;
                        }
                    },
                    { 
                        data: null,
                        orderable: false,
                        render: (data, type, row) => {
                            // Make sure we have a valid invoice ID
                            const invoiceId = row.id;
                            if (!invoiceId) {
                                console.warn('No invoice ID found for row:', row);
                                return '<button class="outbound-action-btn submit" disabled><i class="bi bi-eye"></i><span>View Details</span></button>';
                            }
                            return `<button class="outbound-action-btn submit" data-invoice-id="${invoiceId}">
                                <i class="bi bi-eye"></i>
                                <span>View Details</span>
                            </button>`;
                        }
                    }
                ],
                order: [[1, 'desc']],
                drawCallback: () => {
                    try {
                        // Safely dispose tooltips
                        this.tooltips.forEach(tooltip => {
                            try {
                                if (tooltip && typeof tooltip.dispose === 'function') {
                                    // Check if tooltip element still exists
                                    const element = tooltip._element;
                                    if (element && document.body.contains(element)) {
                                        tooltip.dispose();
                                    }
                                }
                            } catch (tooltipError) {
                                console.warn('Error disposing tooltip:', tooltipError);
                            }
                        });
                        this.tooltips.clear();

                        // Initialize new tooltips
                        requestAnimationFrame(() => {
                            try {
                                document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(el => {
                                    if (el) {
                                        const tooltip = new bootstrap.Tooltip(el, {
                                            placement: 'top',
                                            trigger: 'hover',
                                            container: 'body',
                                            html: true,
                                            template: `
                                                <div class="tooltip" role="tooltip">
                                                    <div class="tooltip-arrow"></div>
                                                    <div class="tooltip-inner"></div>
                                                </div>
                                            `
                                        });
                                        this.tooltips.add(tooltip);
                                    }
                                });
                            } catch (initError) {
                                console.warn('Error initializing tooltips:', initError);
                            }
                        });

                        // Update card counts and total count after each draw
                        requestAnimationFrame(() => {
                            this.updateCardCounts();
                            // Get count directly from DataTable
                            const count = this.dataTable?.rows().count() || 0;
                            this.updateTotalInvoicesFetched(count);
                        });
                    } catch (error) {
                        console.warn('Error in drawCallback:', error);
                    }
                },
                // Add language options for better info display
                language: {
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    emptyTable: "No data available in table"
                },
                // Add initialization complete callback
                initComplete: (settings, json) => {
                    console.log('Table initialization complete');
                    // Show filters section once table is ready
                    document.querySelector('.filters-section')?.classList.remove('d-none');
                }
            });

            // Add event listener for view buttons with error handling
            table.on('click', '.btn-lhdn', async (e) => {
                const invoiceId = e.currentTarget.getAttribute('data-invoice-id');
                if (!invoiceId) {
                    console.error('No invoice ID found');
                    return;
                }
                await this.viewInvoiceDetails(invoiceId);
            });

            console.log('Table initialization successful');
            return true;

        } catch (error) {
            console.error('Error initializing DataTable:', error);
            Swal.fire({
                icon: 'error',
                title: 'Initialization Error',
                text: 'Failed to initialize the table. Please refresh the page.',
                confirmButtonText: 'Refresh',
                showCancelButton: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.reload();
                }
            });
            return false;
        }
    }


    initializeEventListeners() {
        const searchButton = document.getElementById('searchBqeBtn');
        const periodSelect = document.querySelector('.filters-section .form-select');
        const fromDateInput = document.getElementById('fromDate');
        const toDateInput = document.getElementById('toDate');
    
        // Initialize search button handler if it exists
        if (searchButton) {
            searchButton.addEventListener('click', async () => {
                try {
                    // Validate date range
                    const fromDate = fromDateInput?.value;
                    const toDate = toDateInput?.value;
                    
                    if (!fromDate || !toDate) {
                        await Swal.fire({
                            icon: 'warning',
                            title: 'Invalid Date Range',
                            text: 'Please select both From and To dates',
                            confirmButtonText: 'OK'
                        });
                        return;
                    }
    
                    // Check if date range is valid
                    if (moment(fromDate).isAfter(moment(toDate))) {
                        await Swal.fire({
                            icon: 'warning',
                            title: 'Invalid Date Range',
                            text: 'From date cannot be after To date',
                            confirmButtonText: 'OK'
                        });
                        this.hideLoading();
                        return;
                    }
    
                    // Show loading state on button
                    const originalContent = searchButton.innerHTML;
                    searchButton.disabled = true;
                    searchButton.innerHTML = `
                        <span class="spinner-border spinner-border-sm me-2"></span>
                        Searching...
                    `;
    
                    // Refresh the table with current filters
                    await this.refreshInvoiceTable();
    
                } catch (error) {
                    console.error('Error during search:', error);
                    await Swal.fire({
                        icon: 'error',
                        title: 'Search Failed',
                        text: 'Failed to fetch invoices. Please try again.',
                        confirmButtonText: 'OK'
                    });
                } finally {
                    // Reset button state
                    if (searchButton) {
                        searchButton.disabled = false;
                        searchButton.innerHTML = `
                            <i class="bi bi-search me-1"></i>
                            Search BQE Invoice
                        `;
                    }
                }
            });
        }

        // Initialize period handler
        if (periodSelect) {
            console.log('Period select found:', periodSelect);
            periodSelect.addEventListener('change', async (e) => {
                console.log('Period changed to:', e.target.value);
                const period = e.target.value.toLowerCase();
                const dates = this.getDateRangeForPeriod(period);
                
                // Show/hide search button based on period
                if (searchButton) {
                    searchButton.style.display = period === 'custom' ? 'block' : 'none';
                }
                
                if (fromDateInput && toDateInput) {
                    fromDateInput.value = dates.fromDate;
                    toDateInput.value = dates.toDate;
                    fromDateInput.disabled = period !== 'custom';
                    toDateInput.disabled = period !== 'custom';
                    
                    // Automatically trigger search for non-custom periods
                    if (period !== 'custom') {
                        await this.refreshInvoiceTable();
                    }
                }
            });

            // Set initial state
            const initialPeriod = periodSelect.value.toLowerCase();
            if (searchButton) {
                searchButton.style.display = initialPeriod === 'custom' ? 'block' : 'none';
            }
            if (fromDateInput && toDateInput) {
                fromDateInput.disabled = initialPeriod !== 'custom';
                toDateInput.disabled = initialPeriod !== 'custom';
            }
        }

        // Add view details event listener
        $('#reportsTable').on('click', '.outbound-action-btn', async (e) => {
            e.preventDefault();
            const invoiceId = e.currentTarget.dataset.invoiceId;
            await this.viewInvoiceDetails(invoiceId);
        });

        // Add BQE Auth button initialization
        const bqeAuthBtn = document.getElementById('bqeAuthBtn');
        if (bqeAuthBtn) {
            bqeAuthBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                try {
                    const button = e.currentTarget;
                    const originalContent = button.innerHTML;
                    const bqeAuthBtnText = document.getElementById('bqeAuthBtnText');

                    if (bqeAuthBtnText && bqeAuthBtnText.textContent === 'Disconnect BQE') {
                        // Show confirmation dialog
                        const result = await Swal.fire({
                            icon: 'warning',
                            title: 'Disconnect BQE?',
                            text: 'Are you sure you want to disconnect from BQE?',
                            showCancelButton: true,
                            confirmButtonText: 'Yes, disconnect',
                            cancelButtonText: 'No, keep connected',
                            confirmButtonColor: '#dc3545',
                            cancelButtonColor: '#6c757d'
                        });

                        if (result.isConfirmed) {
                            button.disabled = true;
                            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Disconnecting...';

                            const response = await fetch('/bqe/disconnect', {
                                method: 'POST'
                            });
                            const data = await response.json();

                            if (data.success) {
                                // Update button state
                                button.classList.add('auth-btn');
                                button.classList.remove('btn-danger');
                                if (bqeAuthBtnText) {
                                    bqeAuthBtnText.textContent = 'Authorize BQE';
                                }
                                
                                const icon = button.querySelector('i');
                                if (icon) {
                                    icon.classList.remove('bi-shield-x');
                                    icon.classList.add('bi-shield-lock');
                                }

                                // Hide filters section
                                const filtersSection = document.querySelector('.filters-section');
                                if (filtersSection) {
                                    filtersSection.classList.add('d-none');
                                }

                                // Show success message
                                await Swal.fire({
                                    icon: 'success',
                                    title: 'Disconnected',
                                    text: 'Successfully disconnected from BQE'
                                });
                            } else {
                                throw new Error(data.error || 'Failed to disconnect');
                            }
                        }
                    } else {
                        // Show loading state
                        button.disabled = true;
                        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Connecting...';

                        // Get authorization URL
                        const response = await fetch('/bqe/auth');
                        const data = await response.json();

                        if (data.success && data.redirectUrl) {
                            // Show connecting dialog
                            await Swal.fire({
                                title: 'Connecting to BQE',
                                html: `
                                    <div class="text-start">
                                        <div class="alert alert-info mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-info-circle-fill me-2"></i>
                                                <div>
                                                    <h6 class="mb-1">Authorization Process</h6>
                                                    <small>Please wait while we establish a secure connection...</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center text-info">
                                            <i class="bi bi-shield-check me-2"></i>
                                            <small class="status-message">Preparing authentication request...</small>
                                        </div>
                                        <div class="mt-2">
                                            <p class="small text-muted mb-1">
                                                <i class="bi bi-clock-history me-1"></i>
                                                Redirecting in <b class="timer">3</b> seconds
                                            </p>
                                        </div>
                                    </div>
                                `,
                                timer: 3000,
                                timerProgressBar: true,
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                    const timer = Swal.getPopup().querySelector('b.timer');
                                    const statusMsg = Swal.getPopup().querySelector('.status-message');
                                    
                                    const timerInterval = setInterval(() => {
                                        const timeLeft = Math.ceil(Swal.getTimerLeft() / 1000);
                                        if (timer) timer.textContent = timeLeft;
                                        
                                        // Update status message
                                        if (timeLeft <= 1) {
                                            statusMsg.textContent = 'Ready to connect...';
                                        } else if (timeLeft <= 2) {
                                            statusMsg.textContent = 'Validating security tokens...';
                                        }
                                    }, 100);

                                    Swal.getPopup().addEventListener('close', () => {
                                        clearInterval(timerInterval);
                                    });
                                }
                            });

                            // Redirect to BQE auth page
                            window.location.href = data.redirectUrl;
                        } else {
                            throw new Error(data.error || 'Failed to get authorization URL');
                        }
                    }
                } catch (error) {
                    console.error('BQE Auth Error:', error);
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = originalContent;
                    }
                    
                    await Swal.fire({
                        icon: 'error',
                        title: 'Authorization Failed',
                        text: error.message || 'Failed to process BQE authorization',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }

        // Add Submit to LHDN button event listener if it exists
        const submitToLhdnBtn = document.getElementById('submitToLhdnBtn');
        if (submitToLhdnBtn) {
            submitToLhdnBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                const modalElement = document.getElementById('viewDetailsModal');
                const invoiceId = modalElement?.dataset?.invoiceId;
                const button = e.currentTarget;
                
                if (!invoiceId) {
                    console.error('No invoice ID found');
                    return;
                }

                // Check if it's a cancel action
                if (button.classList.contains('btn-cancel')) {
                    await this.cancelInvoice(invoiceId);
                } else {
                    await this.submitToLHDN(invoiceId);
                }
            });
        }
    }


    getDateRangeForPeriod(period) {
        const now = moment();
        let fromDate, toDate;

        switch (period.toLowerCase()) {
            case 'today':
                fromDate = now.clone().startOf('day');
                toDate = now.clone().endOf('day');
                break;
            case 'yesterday':
                fromDate = now.clone().subtract(1, 'days').startOf('day');
                toDate = now.clone().subtract(1, 'days').endOf('day');
                break;
            case 'this week':
                fromDate = now.clone().startOf('week');
                toDate = now.clone().endOf('week');
                break;
            case 'last week':
                fromDate = now.clone().subtract(1, 'week').startOf('week');
                toDate = now.clone().subtract(1, 'week').endOf('week');
                break;
            case 'this month':
                fromDate = now.clone().startOf('month');
                toDate = now.clone().endOf('month');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'last month':
                fromDate = now.clone().subtract(1, 'month').startOf('month');
                toDate = now.clone().subtract(1, 'month').endOf('month');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'this year':
                fromDate = now.clone().startOf('year');
                toDate = now.clone().endOf('year');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'last year':
                fromDate = now.clone().subtract(1, 'year').startOf('year');
                toDate = now.clone().subtract(1, 'year').endOf('year');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'custom':
                // Return empty dates for custom
                return {
                    fromDate: '',
                    toDate: ''
                };
            default:
                fromDate = null;
                toDate = null;
        }

        return {
            fromDate: fromDate ? fromDate.format('YYYY-MM-DD') : '',
            toDate: toDate ? toDate.format('YYYY-MM-DD') : ''
        };
    }

    // Add this new method to check date range size and show warning if needed
    checkDateRangeSize(fromDate, toDate) {
        const daysDifference = toDate.diff(fromDate, 'days');
        const estimatedRecords = daysDifference * 10; // Rough estimate of records per day

        if (estimatedRecords > 2500) {
            Swal.fire({
                icon: 'warning',
                title: 'Large Date Range Selected',
                html: `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        The selected date range is quite large and might exceed BQE API limits.
                        <hr>
                        <small>
                            • BQE API can only return up to 2,500 records (25 pages × 100 records)<br>
                            • Consider using a smaller date range for more accurate results<br>
                            • Selected range: ${daysDifference} days
                        </small>
                    </div>
                `,
                confirmButtonText: 'I Understand'
            });
        }
    }

    
    showLoadingIndicator() {
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-0 text-muted">Loading invoice details...</p>
                </div>
            `;
        }
    }


    
    async fetchInvoicesFromBQE() {
        try {
            console.log('Starting fetchInvoicesFromBQE...');
            
            const authResponse = await fetch('/bqe/check-auth');
            const authData = await authResponse.json();
            
            if (!authData.isAuthorized || !authData.authResponse) {
                throw new Error('No valid auth response available. Please authorize BQE first.');
            }

            const accessToken = authData.authResponse.access_token;
            const baseUrl = authData.authResponse.endpoint;

            const fromDateInput = document.getElementById('fromDate');
            const toDateInput = document.getElementById('toDate');
            
            const fromDate = fromDateInput?.value || moment().startOf('year').format('YYYY-MM-DD');
            const toDate = toDateInput?.value || moment().endOf('year').format('YYYY-MM-DD');

           // console.log('Fetching with date range:', { fromDate, toDate });

            let allInvoices = [];
            let currentPage = 0;
            const pageSize = 100;
            const maxPages = 25;

            try {
                while (currentPage < maxPages) {
                    // Format dates according to BQE API format
                    const formattedFromDate = moment(fromDate).format('YYYY-MM-DD');
                    const formattedToDate = moment(toDate).format('YYYY-MM-DD');
                    
                    const url = new URL(`${baseUrl}/invoice`);
                    const params = new URLSearchParams({
                        'where': `date >= '${formattedFromDate}' AND date <= '${formattedToDate}'`,
                        '$orderby': 'date desc',
                        '$top': pageSize.toString(),
                        '$skip': (currentPage * pageSize).toString(),
                        '$count': 'true',
                        'expand': 'customFields,workflow,lineItems,accountSplits,extendedAccountSplit,invoiceDetails'
                    });
                    
                    url.search = params.toString();
                   // console.log('Request URL:', url.toString());

                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${accessToken}`,
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('API Error:', errorText);
                        throw new Error(`Failed to fetch invoices: ${response.status}`);
                    }

                    const data = await response.json();
                    //console.log('Page response:', data);

                    // Check if data.value exists and is an array
                    const invoices = Array.isArray(data.value) ? data.value : 
                                   Array.isArray(data) ? data : [];
                    
                    if (!invoices.length) {
                        break;
                    }

                    allInvoices = allInvoices.concat(invoices);
                    this.updateTotalInvoicesFetched(allInvoices.length);

                    // Check if we have more records
                    const totalCount = data['@odata.count'] || data.count || 0;
                    if (!totalCount || allInvoices.length >= totalCount) {
                        break;
                    }

                    currentPage++;
                }

                //console.log('Total invoices fetched:', allInvoices.length);

                // Map the invoices before returning
                if (allInvoices.length > 0) {
                    const mappedInvoices = await this.mapBQEInvoices(allInvoices);
                   // console.log('Mapped invoices:', mappedInvoices);
                    return mappedInvoices;
                }

                return [];

            } catch (error) {
                console.error('Error fetching invoices:', error);
                this.hideLoading();
                Swal.fire({
                    icon: 'error',
                    title: 'Error Fetching Invoices',
                    text: 'An error occurred while fetching invoices. Please try again later.',
                    confirmButtonText: 'OK'
                });
                throw error;
            } finally {
                this.hideLoading();
            }

        } catch (error) {
            console.error('Error fetching invoices:', error);
            this.hideLoading();
            Swal.fire({
                icon: 'error',
                title: 'Error Fetching Invoices',
                text: 'An error occurred while fetching invoices. Please try again later.',
                confirmButtonText: 'OK'
            });
            return [];
        }
    }

    async fetchClientDetails(clientId) {
        if (!clientId) {
            console.log('No client ID provided for fetchClientDetails');
            return null;
        }
        
        try {
            const authToken = await this.authManager.getAccessToken();
            if (!authToken) {
                throw new Error('No valid BQE authentication found');
            }

            const response = await fetch(`/bqe/client/${clientId}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch client details');
            }
            
            const clientData = await response.json();
            //console.log('Raw client data:', clientData);

            // Format address properly
            const formattedAddress = clientData.address ? {
                street1: clientData.address.street1 || '',
                street2: clientData.address.street2 || '',
                city: clientData.address.city?.trim() || '',
                state: clientData.address.state?.trim() || '',
                zip: clientData.address.zip?.trim() || '',
                country: clientData.address.country || 'MYS',
                // Create a clean formatted address string
                formattedAddress: [
                    clientData.address.street1,
                    clientData.address.street2,
                    clientData.address.city?.trim(),
                    clientData.address.state?.trim(),
                    clientData.address.zip?.trim(),
                    clientData.address.country || 'MYS'
                ].filter(part => part && part.trim() !== '').join(', ')
            } : {
                street1: 'NA',
                street2: '',
                city: 'NA',
                state: 'NA',
                zip: 'NA',
                country: 'MYS',
                formattedAddress: 'NA'
            };

            // Get communications
            const communications = [
                ...(clientData.communications || []),
                ...(clientData.address?.communications || [])
            ].filter(comm => comm.value && comm.typeName);

            // Map client data
            const mappedClient = {
                ...clientData,
                company: clientData.company || clientData.formattedName || clientData.name,
                taxId: clientData.taxId || clientData.customFields?.find(f => 
                    f.label === "Buyer's Tax ID" || 
                    f.label === "Tax ID"
                )?.value,
                registrationNumber: clientData.customFields?.find(f => 
                    f.label === "Buyer's Registration No" || 
                    f.label === "Registration Number"
                )?.value,
                address: formattedAddress,
                communications: communications,
                // Add additional fields that might be needed
                msicCode: clientData.customFields?.find(f => f.label === "Buyer's MSIC Code")?.value,
                businessActivity: clientData.customFields?.find(f => f.label === "Buyer's Business Activity")?.value,
                countryCode: clientData.customFields?.find(f => f.label === "BUYER'S COUNTRY CODE")?.value,
                stateCode: clientData.customFields?.find(f => f.label === "BUYER'S ADDRESS STATE CODE")?.value
            };

            //console.log('Mapped client data:', mappedClient);
            return mappedClient;

        } catch (error) {
            console.error('Error fetching client details:', error);
            return null;
        }
    }

    async fetchProjectDetails(projectId) {
        if (!projectId) {
            console.log('No Project ID provided for fetchProjectDetails');
            return null;
        }
        
        try {
            const authToken = await this.authManager.getAccessToken();
            if (!authToken) {
                throw new Error('No valid BQE authentication found');
            }

            const response = await fetch(`/bqe/project/${projectId}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch client details');
            }
            
            const projectData = await response.json();
   
            return projectData;

        } catch (error) {
            console.error('Error fetching client details:', error);
            return null;
        }
    }
    async mapBQEInvoices(invoices) {
        if (!invoices || !Array.isArray(invoices)) {
            console.warn('Invalid invoices format:', invoices);
            return [];
        }
    
        // Get auth data first for client detail fetching
        const authResponse = await fetch('/bqe/check-auth');
        const authData = await authResponse.json();
        
        if (!authData.isAuthorized || !authData.authResponse) {
            throw new Error('No valid auth response available');
        }
    
        return await Promise.all(invoices.map(async (invoice, index) => {
            try {
                if (!invoice) {
                    console.warn('Received null or undefined invoice');
                    return null;
                }
    
                // Map type from number to string
                let type = 'Invoice';
                if (invoice.type === 14) {
                    type = 'Credit Note';
                }
    
                // Initialize arrays to store project details for each line item
                let projectDetailsArray = [];
                let clientInfo = '';
                let clientDetails = null;
    
                // First try invoice details
                if (invoice.invoiceDetails?.length > 0) {
                    const clientId = invoice.invoiceDetails[0].clientId;
    
                    // Fetch client details
                    if (clientId) {
                        clientDetails = await this.fetchClientDetails(clientId);
                        if (clientDetails) {
                            clientInfo = clientDetails.company || clientDetails.formattedName;
                        }
                    }
    
                    // Fetch project details for each invoice detail
                    projectDetailsArray = await Promise.all(
                        invoice.invoiceDetails.map(async detail => {
                            if (detail.projectId) {
                                const projectDetail = await this.fetchProjectDetails(detail.projectId);
                                return {
                                    projectId: detail.projectId,
                                    details: projectDetail,
                                    amount: detail.amount,
                                    memo: detail.memo1,
                                    incomeAccount: detail.incomeAccount
                                };
                            }
                            return null;
                        })
                    );
                }
                // Fallback to invoice level clientId
                else if (invoice.clientId) {
                    clientDetails = await this.fetchClientDetails(invoice.clientId);
                    if (clientDetails) {
                        clientInfo = clientDetails.company || clientDetails.formattedName;
                    }
                }
    
                // Format amount with validation
                const amount = parseFloat(invoice.invoiceAmount || 0);
                const formattedAmount = `MYR ${(amount || 0).toFixed(2)}`;
    
                // Create the processed invoice object
                const processedInvoice = {
                    checkbox: '',
                    id: invoice.id || '',
                    invoice_number: invoice.invoiceNumber || '',
                    type: type,
                    customer_name: clientInfo || 'Unknown Client',
                    bqe_date: moment(invoice.date).format('YYYY-MM-DD'),
                    status: invoice.status === 1 ? 'Submitted' : 'Pending',
                    amount: formattedAmount,
                    date_submitted: null,
                    submission_timestamp: null,
                    _rawInvoice: invoice,
                    _clientDetails: clientDetails,
                    _projectDetailsArray: projectDetailsArray, 
                    _invoiceDetails: invoice.invoiceDetails || [],
                    version: invoice.version || '1.0',
                    currency: invoice.currency || 'MYR',
                    due_date: invoice.dueDate ? moment(invoice.dueDate).format('YYYY-MM-DD') : null
                };
    
                return processedInvoice;
    
            } catch (error) {
                console.error(`Error mapping invoice ${invoice?.invoiceNumber || 'Unknown'}:`, error);
                console.debug('Problematic invoice data:', JSON.stringify(invoice, null, 2));
                return null;
            }
        })).then(results => {
            const validResults = results.filter(Boolean);
            return validResults;
        });
    }

    
    isTableInitialized() {
        return this.dataTable && $.fn.DataTable.isDataTable('#reportsTable');
    }


        showLoading(initialState = 'checking_staging') {
            try {
                const modal = document.getElementById('loadingModal');
                if (!modal) {
                    console.error('Loading modal not found');
                    return;
                }

                // Initialize or get Bootstrap modal instance
                let bsModal = bootstrap.Modal.getInstance(modal);
                if (!bsModal) {
                    bsModal = new bootstrap.Modal(modal, {
                        backdrop: 'static',
                        keyboard: false
                    });
                }

                // Define steps and their configurations
                const steps = [
                    { id: 'checking_staging', label: 'Checking Database', icon: 'bi-database-check' },
                    { id: 'retrieving', label: 'Retrieving Invoices', icon: 'bi-cloud-download' },
                    { id: 'saving', label: 'Saving Records', icon: 'bi-save' },
                    { id: 'completed', label: 'Completed', icon: 'bi-check-circle' }
                ];

                // Reset all steps to waiting state
                const stepElements = modal.querySelectorAll('.step');
                stepElements.forEach((stepEl, index) => {
                    const step = steps[index];
                    stepEl.innerHTML = `
                        <div class="step-icon">
                            <i class="bi ${step.icon}"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-label">${step.label}</div>
                            <span class="step-status waiting">Waiting...</span>
                        </div>
                    `;
                    stepEl.setAttribute('data-status', 'waiting');
                });

                // Show modal
                bsModal.show();

                // Update to initial state after a brief delay
                setTimeout(() => {
                    this.updateLoadingState(initialState);
                }, 100);

            } catch (error) {
                console.error('Error showing loading overlay:', error);
            }
        }


    // Update the refreshInvoiceTable method
    async refreshInvoiceTable() {
        let mappedInvoices = [];
        try {
            this.startTime = Date.now(); // Add start time tracking
            this.showLoading();

            // Step 1: Check staging database
            this.updateLoadingState('checking_staging');
            const { stagingMap } = await this.checkStagingDatabase();
            await this.delay(300); // Add small delay for visual feedback

            // Step 2: Retrieve invoices
            this.updateLoadingState('retrieving', 'Fetching invoices from BQE... This may take a few moments depending on the number of invoices.');
            const invoices = await this.fetchInvoicesFromBQE();
            if (!invoices?.length) {
                this.updateLoadingState('retrieving', 'Fetching invoices from BQE... This may take a few moments depending on the number of invoices.');
                throw new Error('');
            }
            // Step 3: Save and update UI
            this.updateLoadingState('saving', 'Saving records to database...');
            mappedInvoices = this.mapInvoicesWithStagingData(invoices, { stagingMap });
            await this.saveAndUpdateUI(mappedInvoices);
            await this.delay(300);

            this.updateLoadingState('completed');
            this.hideLoading();


        } catch (error) {
            console.error('Error refreshing table:', error);
            
            // Show error in the current step
            const loadingMessage = document.querySelector('.loading-message');

            
            if (loadingMessage) {
                loadingMessage.innerHTML = `
                    <div class="text-danger">
                        ${error.message || ''}
                    </div>
                `;
            }
            
            await Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error.message || 'Failed to refresh invoice data. Please try again.'
            });

            // Hide loading modal after error
            const modal = document.getElementById('loadingModal');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
        }
    }

    updateLoadingState(state, message = '') {
        try {
            const modal = document.getElementById('loadingModal');
            if (!modal) return;
    
            const steps = ['checking_staging', 'retrieving', 'saving', 'completed'];
            const currentStepIndex = steps.indexOf(state);
    
            if (currentStepIndex === -1) {
                console.warn('Invalid state:', state);
                return;
            }
    
            // Update progress bar
            const progressBar = modal.querySelector('.progress-bar');
            if (progressBar) {
                const progress = ((currentStepIndex + 1) / steps.length) * 100;
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
            }
    
            // Update loading message and time
            const loadingMessage = modal.querySelector('.loading-message');
            const loadingTimeLeft = modal.querySelector('.loading-time-left');
    
            if (loadingMessage && loadingTimeLeft) {
                if (state === 'completed') {
                    const completedMessages = [
                        'Great success! All invoices are here.',
                        'Successfully retrieved all invoices!',
                        'All done! Your invoices are ready.',
                        'Fetching Invoices completed successfully!',
                        'Mission accomplished! Invoices fetched.'
                    ];
                    const randomMessage = completedMessages[Math.floor(Math.random() * completedMessages.length)];
                    loadingMessage.textContent = randomMessage;
    
                    const duration = this.calculateDuration();
                    loadingTimeLeft.textContent = duration ? `Completed in ${duration}` : '';
                    this.hideLoading();
                } else {
                    loadingMessage.textContent = message || 'Processing...';
                    const estimatedTimeLeft = this.calculateEstimatedTimeLeft(currentStepIndex + 1, steps.length);
                    loadingTimeLeft.textContent = estimatedTimeLeft;
                }
            }
    
            // Update steps
            modal.querySelectorAll('.step').forEach((stepEl, index) => {
                const stepIcon = stepEl.querySelector('.step-icon i');
                const stepStatus = stepEl.querySelector('.step-status');
    
                if (index < currentStepIndex) {
                    // Completed steps
                    stepEl.setAttribute('data-status', 'completed');
                    if (stepIcon) stepIcon.className = 'bi bi-check-circle-fill';
                    if (stepStatus) stepStatus.textContent = 'Completed';
                } else if (index === currentStepIndex) {
                    // Current step
                    stepEl.setAttribute('data-status', 'in-progress');
                    if (stepIcon) stepIcon.className = 'bi bi-arrow-repeat spin';
                    if (stepStatus) stepStatus.textContent = message || 'Processing...';
                    this.updateLoadingDots(stepStatus);
                } else {
                    // Upcoming steps
                    stepEl.setAttribute('data-status', 'waiting');
                    if (stepIcon) stepIcon.className = 'bi bi-circle';
                    if (stepStatus) stepStatus.textContent = 'Waiting...';
                }
            });
    
        } catch (error) {
            console.error('Error updating loading state:', error);
        }
    }
            
        // Add this back near the top of the class with other helper methods
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

    
        // Hide loading overlay
        hideLoading() {
            try {
                // Clear loading dots interval if it exists
                if (this.loadingDotsInterval) {
                    clearInterval(this.loadingDotsInterval);
                    this.loadingDotsInterval = null;
                }

                const modal = document.getElementById('loadingModal');
                if (!modal) return;

                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    // Hide modal directly
                    bsModal.hide();
                    
                    // Reset progress bar if it exists
                    const progressBar = modal.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '0%';
                        progressBar.setAttribute('aria-valuenow', '0');
                    }
                }
            } catch (error) {
                console.error('Error hiding loading overlay:', error);
            }
        }
    // Add cleanup method
    cleanup() {
        try {
            // Safely dispose tooltips
            this.tooltips.forEach(tooltip => {
                try {
                    if (tooltip && typeof tooltip.dispose === 'function') {
                        tooltip.dispose();
                    }
                } catch (error) {
                    console.warn('Error disposing tooltip during cleanup:', error);
                }
            });
            this.tooltips.clear();

            // Destroy DataTable
            if (this.dataTable) {
                try {
                    this.dataTable.destroy();
                } catch (error) {
                    console.warn('Error destroying DataTable:', error);
                }
            }

            // Clean up BQE auth manager
            if (this.authManager) {
                try {
                    this.authManager.cleanup();
                } catch (error) {
                    console.warn('Error cleaning up BQE auth manager:', error);
                }
            }

            // Remove event listeners
            try {
                $('#reportsTable').off('click', '.btn-lhdn');
            } catch (error) {
                console.warn('Error removing event listeners:', error);
            }
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }

    // Add new method to update card counts
    updateCardCounts() {
        if (!this.dataTable) return;

        // Reset counts
        this.cardCounts = {
            total: 0,
            submitted: 0,
            pending: 0,
            cancelled: 0
        };

        // Get all data from table
        const data = this.dataTable.rows().data();
        
        // Count totals
        data.each(row => {
            // Increment total count
            this.cardCounts.total++;
            
            // Check status and increment appropriate counter
            const status = (row.status || '').toLowerCase();
            switch(status) {
                case 'submitted':
                    this.cardCounts.submitted++;
                    break;
                case 'pending':
                    this.cardCounts.pending++;
                    break;
                case 'cancelled':
                    this.cardCounts.cancelled++;
                    break;
            }
        });

        // Update UI immediately after counting
        this.updateCardUI();
    }

    // Add method to update card UI
    updateCardUI() {
        // Update total invoices card
        const totalCard = document.querySelector('.invoices-card .count-info h6');
        if (totalCard) {
            totalCard.textContent = this.cardCounts.total;
        }

        // Update submitted card
        const submittedCard = document.querySelector('.submitted-card .count-info h6');
        if (submittedCard) {
            submittedCard.textContent = this.cardCounts.submitted;
        }

        // Update pending card
        const pendingCard = document.querySelector('.pending-card .count-info h6');
        if (pendingCard) {
            pendingCard.textContent = this.cardCounts.pending;
        }

        // Update cancelled card
        const cancelledCard = document.querySelector('.cancelled-card .count-info h6');
        if (cancelledCard) {
            cancelledCard.textContent = this.cardCounts.cancelled;
        }
    }

    // Add method to get current counts
    getCardCounts() {
        return { ...this.cardCounts };
    }


async viewInvoiceDetails(invoiceId) {
        try {
            if (!invoiceId) {
                throw new Error('Invalid invoice ID');
            }

            // Show modal first
            const modalElement = document.getElementById('viewDetailsModal');
            if (!modalElement) {
                throw new Error('Modal element not found');
            }

              // Show loading overlay
              const loadingOverlay = document.getElementById('modalLoadingOverlay');
              if (loadingOverlay) {
                  loadingOverlay.classList.remove('d-none');
              }

            // Store the invoice ID in the modal's data attribute
            modalElement.dataset.invoiceId = invoiceId;

            // Initialize and show modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Get the invoice data from the DataTable
            const invoice = this.dataTable.rows()
                .data()
                .filter(row => row.id === invoiceId)[0];

            if (!invoice) {
                throw new Error('Invoice not found in table data');
            }

            // Fetch detailed invoice data
            const invoiceResponse = await fetch(`/bqe/invoice/${invoiceId}`);
            if (!invoiceResponse.ok) {
                throw new Error('Failed to fetch invoice details');
            }
            // Fetch company (supplier) details
            try {
                const companyResponse = await fetch('/bqe/company');
                if (!companyResponse.ok) {
                    throw new Error('Failed to fetch company details');
                }
                
                const companyData = await companyResponse.json();
               // console.log('Company Data:', companyData);
                invoice.company = companyData;
                
                // Process company custom fields for supplier information
                invoice.supplier = {
                    tin: this.getCustomFieldValue(companyData.customFields, "Supplier's TIN"),
                    registrationNumber: this.getCustomFieldValue(companyData.customFields, "Supplier's Registration No"),
                    sstId: this.getCustomFieldValue(companyData.customFields, "Supplier's SST No"),
                    msicCode: this.getCustomFieldValue(companyData.customFields, "Supplier's MSIC Code"),
                    businessActivity: this.getCustomFieldValue(companyData.customFields, "Supplier's Business Activity")
                };
            } catch (error) {
                console.warn('Error fetching company details:', error);
            }

            // Update modal content
            //await this.updateModalContent(invoice);
            await this.updateModalContent(invoice);
   

        } catch (error) {
            console.error('Error viewing invoice details:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to load invoice details. Please try again.'
            });
        }
    }

    // Add formatDate function
    formatDate(date) {
        if (!date) return '-';
        return moment(date).format('DD-MM-YYYY');
    }

async updateModalContent(invoice) {
    try {
        // Hide loading spinner at the start
        const loadingSpinner = document.getElementById('loadingSpinner');
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }

        // Store the current invoice
        this._currentInvoice = invoice;
        console.log('Current Invoice: ', invoice);

        // Get version from custom fields
        const version = invoice._rawInvoice?.customFields?.find(f => 
            f.label === 'Invoice Version' || 
            f.label === 'Version'
        )?.value || '1.0';

        // Update invoice information with null checks
        const updateElement = (id, value, defaultValue = '-') => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value || defaultValue;
            }
        };

        updateElement('modalInvoiceNumber', invoice.invoice_number);
        updateElement('modalInvoiceDate', this.formatDate(invoice._rawInvoice?.date));
        updateElement('modalInvoiceVersion', version);
        updateElement('modalInvoiceType', invoice.type);
        updateElement('invoice-number', `#${invoice.invoice_number || '-'}`);

        // Update status with badge - using status from table data
        const statusElement = document.getElementById('modalInvoiceStatus');
        if (statusElement) {
            const status = invoice.status?.toLowerCase() || 'pending';
            statusElement.innerHTML = `<div class="status-badge ${status}">
                <i class="bi bi-${this.getStatusIcon(status)}"></i>
                ${invoice.status || 'Pending'}
            </div>`;
        }

        // Update supplier information with improved layout and null checks
        const updateInfoSection = (id, label, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = `
                    <div class="info-label">${label}</div>
                    <div class="info-value">${value || '-'}</div>
                `;
            }
        };

        updateInfoSection('modalSupplierName', 'Company Name', invoice.company?.name);
        updateInfoSection('modalSupplierTin', 'Tax ID Number (TIN)', invoice.supplier?.tin);
        updateInfoSection('modalSupplierBrn', 'Business Registration No.', invoice.supplier?.registrationNumber);
        updateInfoSection('modalSupplierSst', 'SST Registration No.', invoice.supplier?.sstId);

        // Update buyer information with improved layout and null checks
        updateInfoSection('modalBuyerName', 'Company Name', invoice._clientDetails?.company);
        updateInfoSection('modalBuyerTin', 'Tax ID Number (TIN)', invoice._clientDetails?.taxId);
        updateInfoSection('modalBuyerBrn', 'Business Registration No.', invoice._clientDetails?.registrationNumber);
        updateInfoSection('modalBuyerAddress', 'Address', invoice._clientDetails?.address?.formattedAddress);

      // Render line items
      const lineItemsContainer = document.getElementById('lineItemsBody');
      if (lineItemsContainer) {
          const lineItemsHtml = this.renderLineItems();
          lineItemsContainer.innerHTML = lineItemsHtml;
      }


        // Get the submit button and update its state if it exists
        const submitButton = document.getElementById('submitToLhdnBtn');
        if (submitButton) {
            const status = invoice.status?.toLowerCase() || 'pending';
            
            if (status === 'submitted' && invoice.date_submitted) {
                // Calculate cancellation time
                const cancellationTime = InvoiceHelper.calculateCancellationTime(invoice.date_submitted, status);
                
                if (!cancellationTime.expired && !cancellationTime.isNA) {
                    // Within cancellation period - show Cancel Invoice button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="bi bi-x-circle"></i> Cancel Invoice';
                    submitButton.classList.remove('btn-lhdn');
                    submitButton.classList.add('btn-lhdn', 'btn-cancel');
                } else {
                    // Past cancellation period - show Submitted and disable
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="bi bi-check-circle"></i> Submitted';
                    submitButton.classList.remove('btn-lhdn', 'btn-cancel');
                    submitButton.classList.add('btn-lhdn');
                }
            } else {
                // Not submitted - show Submit to LHDN button
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="bi bi-send"></i> Submit to LHDN';
                submitButton.classList.remove('btn-lhdn', 'btn-cancel');
                submitButton.classList.add('btn-lhdn');
            }
        }

    } catch (error) {
        console.error('Error updating modal content:', error);
        // Make sure to hide spinner even if there's an error
        const loadingSpinner = document.getElementById('loadingSpinner');
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }
        throw error;
    }
}

    // Add helper function for status icons
    getStatusIcon(status) {
        const icons = {
            'pending': 'hourglass-split',
            'submitted': 'check-circle',
            'cancelled': 'x-circle'
        };
        return icons[status] || 'question-circle';
    }
   
    
    renderLineItems() {
        // Check if we have invoice data
        if (!this._currentInvoice || !this._currentInvoice._invoiceDetails?.length) {
            return `<tr><td colspan="8" class="text-center">No items found</td></tr>`;
        }

        let subtotal = 0;
        let taxTotal = 0;
        let total = 0;
        let exemptedTaxTotal = 0;

        // Get tax information first
        const taxInfo = this.getTaxInfo(this._currentInvoice);
        const taxRate = taxInfo.rate; // This will be 0 for exempt invoices
        const taxType = taxInfo.type;
        const isTaxExempt = this.isTaxExempt(this._currentInvoice);
        
        // Get the applicable tax rate that would have been applied if not exempt
        let applicableTaxRate = taxInfo.applicableTaxRate;
        if (isTaxExempt && applicableTaxRate === 0) {
            // If no applicable tax rate was found in getTaxInfo, try to get it from other sources
            const projectCustomFields = this._currentInvoice?._projectDetailsArray?.[0]?.details?.customFields || [];
            const taxRateField = projectCustomFields.find(f => 
                f.label === 'SERVICE TAX RATE' || 
                f.label === 'Tax Rate'
            );
            
            if (taxRateField?.value) {
                applicableTaxRate = parseFloat(taxRateField.value);
            } else {
                // Look for mainServiceTax
                const projectDetailsTaxRate = this._currentInvoice?._projectDetailsArray?.[0]?.details?.mainServiceTax;
                if (typeof projectDetailsTaxRate === 'number' && !isNaN(projectDetailsTaxRate)) {
                    applicableTaxRate = projectDetailsTaxRate;
                } else {
                    // Default to standard rate of 6%
                    applicableTaxRate = 6.00;
                }
            }
        }
        
        // Calculate what the tax would have been if not exempt
        const taxExemptionAmount = taxInfo.exemption;
        exemptedTaxTotal = taxExemptionAmount;

        // Add line items
        let rows = this._currentInvoice._invoiceDetails.map((item, index) => {
            // Get the corresponding project details
            const projectDetails = this._currentInvoice._projectDetailsArray?.[index]?.details || {};
            
            // Get classification from project custom fields
            const classificationField = projectDetails?.customFields?.find(f => 
                f.label === 'Invoice Classification'
            );
            const classification = classificationField?.description || projectDetails?.code || '022';
            
            // Get description from memo1, clean any HTML tags
            const description = item.memo1?.replace(/<[^>]*>/g, '') || item.description || 'NA';
            
            // Parse numbers with validation
            const quantity = parseFloat(item.quantity || 1);
            const amount = parseFloat(item.serviceAmount || 0);
            const rate = amount; // For single quantity, rate equals amount
            
            // Calculate tax based on the tax rate
            let taxAmount = 0;
            if (!isTaxExempt) {
                taxAmount = (amount * taxRate) / 100;
            }
            
            // Calculate what the tax would have been if not exempt (for display only)
            let exemptedTaxAmount = 0;
            if (isTaxExempt) {
                exemptedTaxAmount = (amount * applicableTaxRate) / 100;
            }
            
            const itemTotal = amount + taxAmount;

            // Add to totals
            subtotal += amount;
            taxTotal += taxAmount;
            total += itemTotal;

            return `
                <tr>
                    <td style="white-space: normal; word-wrap: break-word;">${classification}</td>
                    <td class="description-cell" style="white-space: normal; word-wrap: break-word;">${description}</td>
                    <td class="text-end">${quantity}</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${this.formatCurrency(rate)}</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${this.formatCurrency(amount)}</td>
                    <td class="text-end">${this.formatNumber(applicableTaxRate, true)}%</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${isTaxExempt ? this.formatCurrency(exemptedTaxAmount) : this.formatCurrency(taxAmount)}</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${this.formatCurrency(itemTotal)}</td>
                </tr>
            `;
        }).join('');

        // Get tax exemption details from project custom fields
        const projectCustomFields = this._currentInvoice._projectDetailsArray?.[0]?.details?.customFields || [];
        const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
        const taxExemptionField = projectCustomFields.find(f => f.label === 'Details of Tax Exemption');
        
        // Extract just the code from the tax type description
        const taxTypeCode = taxTypeField?.description?.split('|')?.[0]?.trim() || '02';
        
        // Add tax information section
        rows += `
            <tr>
                <td colspan="8" class="border-0 pt-3">
                    <div class="details-section border rounded p-3 bg-light">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2"><strong>Tax Information</strong></div>
                                <div class="mb-2">Tax Type: ${taxTypeCode}${isTaxExempt ? ' (Tax Exempt)' : ''}</div>
                                <div class="mb-2">Tax Rate: ${isTaxExempt ? 
                                    `${this.formatNumber(applicableTaxRate, true)}% <span class="text-muted small">(Exempt - 0.00% applied)</span>` : 
                                    this.formatNumber(taxRate, true) + '%'}</div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">Amount Tax Exemption: ${isTaxExempt ? this.formatCurrency(exemptedTaxTotal) : '0.00'}</div>
                                <div class="mb-2">Details of Tax Exemption: ${isTaxExempt ? (taxExemptionField?.description || 'Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018') : 'Not Applicable'}</div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Add summary rows
        rows += `
            <tr class="summary-row">
                <td colspan="4" class="text-end fw-bold">Subtotal (Excluding Tax):</td>
                <td colspan="4" class="text-end fw-bold">${this.formatCurrency(subtotal)}</td>
            </tr>
            <tr class="summary-row">
                <td colspan="4" class="text-end fw-bold">Total Tax Amount:</td>
                <td colspan="4" class="text-end fw-bold">${isTaxExempt ? '0.00' : this.formatCurrency(taxTotal)}</td>
            </tr>
            <tr class="summary-row">
                <td colspan="4" class="text-end fw-bold">Total Amount (Including Tax):</td>
                <td colspan="4" class="text-end fw-bold">${this.formatCurrency(total)}</td>
            </tr>
        `;

        // Add payment information if available
        const paymentInfo = this.getPaymentInfo(this._currentInvoice);
        if (paymentInfo) {
            rows += `
                <tr>
                    <td colspan="8" class="border-0 pt-4">
                        <div class="fw-bold mb-2">Please remit the payment to:</div>
                        <div>${paymentInfo}</div>
                    </td>
                </tr>
            `;
        }
                
        return rows;
    }

    // Helper method to check if an invoice is tax exempt
    isTaxExempt(invoice) {
        // Check if tax type starts with 'E' (Exempt)
        const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
        const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
        return taxTypeField?.description?.startsWith('E') || false;
    }

    formatCurrency(amount) {
        if (amount === null || amount === undefined || isNaN(amount)) {
            return '0.00';
        }
        return parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    // Helper function to format numbers with thousand separators
    formatNumber(number, isRate = false) {
        if (number === null || number === undefined || isNaN(number)) {
            return '0';
        }
        
        // For tax rates, always show 2 decimal places
        if (isRate) {
            return number.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        // For other numbers, follow existing logic
        if (Number.isInteger(number)) {
            return number.toLocaleString('en-US');
        }
        return number.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    // helper method to get payment info from company custom fields
getPaymentInfo(invoice) {
    try {
        // Get payment info from company custom fields
        const paymentField = invoice.company?.customFields?.find(f => f.label === 'Remit Payment To');
        
        if (paymentField?.value) {
            // Replace asterisks with line breaks and clean up any HTML entities
            return paymentField.value.replace(/\*/g, '<br>').replace(/&#10;/g, '<br>');
        }
        
        return null;
    } catch (error) {
        console.error('Error getting payment info:', error);
        return null;
    }
}

getTaxRate(invoice) {
    try {
        // Check if tax type is 'E' (Exempt) - if exempt, return 0 immediately
        const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
        const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
        if (taxTypeField?.description?.includes('E')) {
            return 0;
        }

        // PRIORITY 1: Check project level tax rate first (from project details or project custom fields)
        
        // 1.1: Check project details array (most reliable source for project tax rate)
        const projectDetailsTaxRate = invoice?._projectDetailsArray?.[0]?.details?.mainServiceTax;
        if (typeof projectDetailsTaxRate === 'number' && !isNaN(projectDetailsTaxRate) && projectDetailsTaxRate !== 0) {
            console.log('Using project details tax rate:', projectDetailsTaxRate);
            return projectDetailsTaxRate;
        }
        
        // 1.2: Check direct project tax rate 
        const projectTaxRate = invoice?.project?.mainServiceTax;
        if (typeof projectTaxRate === 'number' && !isNaN(projectTaxRate) && projectTaxRate !== 0) {
            console.log('Using project tax rate:', projectTaxRate);
            return projectTaxRate;
        }
        
        // 1.3: Check project custom fields
        const projectTaxRateField = projectCustomFields.find(f => 
            f.label === 'Tax Rate' || 
            f.label === 'SERVICE TAX RATE'
        );
        if (projectTaxRateField?.value && parseFloat(projectTaxRateField.value) !== 0) {
            const rate = parseFloat(projectTaxRateField.value);
            console.log('Using project custom field tax rate:', rate);
            return rate;
        }

        // PRIORITY 2: Check invoice custom fields (raw invoice)
        const invoiceCustomFields = invoice?._rawInvoice?.customFields || [];
        const taxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');
        if (taxRateField?.value) {
            const rate = parseFloat(taxRateField.value);
            console.log('Using invoice custom field tax rate from _rawInvoice:', rate);
            return rate;
        }
        
        // PRIORITY 3: Fall back to other invoice level checks
        
        // 3.1: Check invoice custom fields with alternative labels
        const altTaxRateField = invoice?._rawInvoice?.customFields?.find(f => 
            f.label === 'Tax rate' || 
            f.label === 'Tax Rate'
        );
        if (altTaxRateField?.value) {
            const rate = parseFloat(altTaxRateField.value);
            console.log('Using invoice custom field tax rate (alt labels):', rate);
            return rate;
        }
        
        // 3.2: Check raw invoice tax rate as last resort
        if (typeof invoice?._rawInvoice?.mainServiceTax === 'number' && 
            !isNaN(invoice._rawInvoice.mainServiceTax)) {
            console.log('Using raw invoice tax rate:', invoice._rawInvoice.mainServiceTax);
            return invoice._rawInvoice.mainServiceTax;
        }

        // Default to 0 if no tax rate is found
        console.log('No tax rate found, defaulting to 0');
        return 0;
    } catch (error) {
        console.error('Error getting tax rate:', error);
        return 0;
    }
}
    
        calculateTaxExemption(invoice) {
            try {
                // Get the tax type from project custom fields
                const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
                const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
                
                // If tax type is 'E' (Exempt), calculate what the tax would have been
                if (taxTypeField?.description?.includes('E')) {
                    // Get the tax rate that would apply if not exempt
                    let applicableTaxRate = 0;
                    
                    // First check invoice custom fields for tax rate
                    const invoiceCustomFields = invoice?._rawInvoice?.customFields || [];
                    const invoiceTaxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');
                    
                    if (invoiceTaxRateField?.value) {
                        applicableTaxRate = parseFloat(invoiceTaxRateField.value);
                        console.log('Using invoice custom field tax rate for exemption calculation:', applicableTaxRate);
                    } else {
                        // Try to get the actual tax rate from project details (ignoring exempt status)
                        const projectDetailsTaxRate = invoice?._projectDetailsArray?.[0]?.details?.mainServiceTax;
                        if (typeof projectDetailsTaxRate === 'number' && !isNaN(projectDetailsTaxRate)) {
                            applicableTaxRate = projectDetailsTaxRate;
                        } else {
                            // If not found, try standard rate or service tax rate from fields
                            const taxRateField = projectCustomFields.find(f => 
                                f.label === 'SERVICE TAX RATE' || 
                                f.label === 'Tax Rate'
                            );
                        
                            if (taxRateField?.value) {
                                applicableTaxRate = parseFloat(taxRateField.value);
                            } else {
                                // Use standard rate of 6% if no specific rate is found
                                applicableTaxRate = 6.00;
                            }
                        }
                    }
                    
                    // Calculate total amount from all invoice details
                    const totalAmount = invoice?._invoiceDetails?.reduce((sum, item) => {
                        return sum + parseFloat(item.serviceAmount || 0);
                    }, 0) || invoice?._rawInvoice?.serviceAmount || 0;
                    
                    // Calculate what the tax would have been if not exempt
                    const exemptionAmount = (totalAmount * applicableTaxRate) / 100;
                    console.log('Tax exemption calculation:', totalAmount, '*', applicableTaxRate, '% =', exemptionAmount);
                    return exemptionAmount;
                }
                
                return 0;
            } catch (error) {
                console.error('Error calculating tax exemption:', error);
                return 0;
            }
        }
// Helper function to get custom field value
getCustomFieldValue(customFields, label) {
    if (!customFields || !Array.isArray(customFields)) return '';
    const field = customFields.find(f => f.label === label);
    return field ? field.value : '';
}

getTaxInfo(invoice) {
    // First try to get tax info from company custom fields
    const companyCustomFields = invoice?.company?.customFields || [];
    const taxTypeField = companyCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
    
    // Get the tax rate - this will be 0 for exempt invoices
    const taxRate = this.getTaxRate(invoice);
    
    // Get the applicable tax rate that would have been applied if not exempt
    let applicableTaxRate = 0;
    const invoiceCustomFields = invoice?._rawInvoice?.customFields || [];
    const invoiceTaxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');
    
    if (invoiceTaxRateField?.value) {
        applicableTaxRate = parseFloat(invoiceTaxRateField.value);
        console.log('Using invoice custom field tax rate for tax info:', applicableTaxRate);
    }
    
    // Calculate tax exemption amount
    const exemption = this.calculateTaxExemption(invoice);
    
    if (taxTypeField?.value) {
        return {
            type: taxTypeField.value,
            rate: taxRate,
            exemption: exemption,
            applicableTaxRate: applicableTaxRate
        };
    }

    // Fallback to project custom fields if company fields not found
    const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
    const projectTaxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
    
    if (projectTaxTypeField?.description) {
        // Extract just the code from the tax type description
        const taxTypeCode = projectTaxTypeField.description.split('|')[0].trim();
        return {
            type: taxTypeCode,
            rate: taxRate,
            exemption: exemption,
            applicableTaxRate: applicableTaxRate
        };
    }
    
    // Default values if no tax info found
    return {
        type: 'SR',
        rate: taxRate,
        exemption: exemption,
        applicableTaxRate: applicableTaxRate
    };
}

    // Add helper method to initialize tooltips
    initializeTooltips() {
        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(element => {
            new bootstrap.Tooltip(element);
        });
    }

    // Add this helper method for showing confirmation dialog
    async showSubmissionConfirmDialog(invoice) {
        return Swal.fire({
            title: 'Submit Invoice to LHDN?',
            html: `
                <div class="confirmation-dialog">
                    <div class="d-flex align-items-center mb-3">
                        <div class="invoice-icon">
                            <i class="bi bi-file-earmark-text text-primary"></i>
                        </div>
                        <div class="ms-3">
                            <div class="invoice-number">${invoice._rawInvoice.invoiceNumber}</div>
                            <div class="invoice-details">
                                ${moment(invoice._rawInvoice.date).format('DD-MM-YYYY')} • ${this.formatCurrency(invoice._rawInvoice.invoiceAmount)}
                            </div>
                        </div>
                        <span class="ms-auto badge bg-warning">Pending</span>
                    </div>

                    <div class="verification-list">
                        <div class="form-check" style="justify-content: flex-start">
                            <input type="checkbox" class="form-check-input" id="check1" required>
                            <label class="form-check-label" for="check1">
                                I confirm all invoice details are accurate
                            </label>
                        </div>
                        <div class="form-check" style="justify-content: flex-start">
                            <input type="checkbox" class="form-check-input" id="check2" required>
                            <label class="form-check-label" for="check2">
                                I verify the tax information is correct
                            </label>
                        </div>
                        <div class="form-check" style="justify-content: flex-start">
                            <input type="checkbox" class="form-check-input" id="check3" required>
                            <label class="form-check-label" for="check3">
                                I understand this action cannot be undone
                            </label>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3 mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        You'll be asked to select document version in the next step
                    </div>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: '<i class="bi bi-arrow-right me-2"></i>Proceed',
            cancelButtonText: '<i class="bi bi-x me-2"></i>Cancel',
            confirmButtonColor: '#0d6efd',
            cancelButtonColor: '#6c757d',
            customClass: {
                confirmButton: 'outbound-action-btn submit',
                cancelButton: 'outbound-action-btn cancel'
            },
            preConfirm: () => {
                const check1 = document.getElementById('check1')?.checked;
                const check2 = document.getElementById('check2')?.checked;
                const check3 = document.getElementById('check3')?.checked;
                
                if (!check1 || !check2 || !check3) {
                    Swal.showValidationMessage('Please complete the verification checklist');
                    return false;
                }
                return true;
            }
        });
    }
    async submitToLHDN(invoiceId) {
        try {
            // After confirmation, show version selection dialog
            const versionResult = await this.showVersionSelectionDialog();
            if (!versionResult.isConfirmed) {
                return;
            }
    
            const version = versionResult.value || '1.0';
    
            // Show loading state
            Swal.fire({
                title: 'Submitting Invoice',
                html: `
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mb-0">Please wait while we process your submission...</p>
                    </div>
                `,
                allowOutsideClick: false,
                showConfirmButton: false
            });
    
            // Prepare invoice data
            const invoiceData = this.prepareInvoiceData(invoiceId);
    
            // Structure the request payload
            const payload = {
                invoiceId: invoiceData._rawInvoice.invoiceNumber,
                version: version,
                invoiceData: {
                    _rawInvoice: invoiceData._rawInvoice,
                    _clientDetails: invoiceData._clientDetails,
                    company: invoiceData.company,
                    supplier: invoiceData.supplier,
                    tax_info: invoiceData.tax_info,
                    _projectDetailsArray: invoiceData._projectDetailsArray,
                    _invoiceDetails: invoiceData._invoiceDetails
                }
            };
    
            // Make the submission request
            const response = await fetch('/bqe/submit-to-lhdn', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(payload)
            });
    
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            let errorData;
    
            if (contentType && contentType.includes('application/json')) {
                errorData = await response.json();
            } else {
                const textResponse = await response.text();
                console.error('Non-JSON response:', textResponse);
                errorData = { message: 'Invalid server response', details: [] };
            }
    
            if (!response.ok) {
                console.error('Submission Error Response:', errorData);
                throw {
                    message: errorData.message || 'Submission failed',
                    details: errorData.details || [],
                    status: response.status,
                    raw: errorData
                };
            }
    
            const result = errorData; // Use the already parsed response
    
            // Get the submission timestamp from the server response
            const submissionTime = result.submissionTime || moment().format('YYYY-MM-DD HH:mm:ss');
    
            // Close the modal first
            const modal = bootstrap.Modal.getInstance(document.getElementById('viewDetailsModal'));
            if (modal) {
                modal.hide();
            }
    
            // Then show success message
            await Swal.fire({
                icon: 'success',
                title: 'Success!',
                html: `
                    <div class="alert alert-success mb-3">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        Invoice successfully submitted to LHDN
                    </div>
                    <div class="text-start">
                        <strong>Details:</strong>
                        <ul class="list-unstyled mt-2 mb-0">
                            <li><i class="bi bi-receipt me-2"></i>Invoice: ${invoiceData._rawInvoice.invoiceNumber}</li>
                            <li><i class="bi bi-calendar me-2"></i>Date: ${submissionTime}</li>
                            <li><i class="bi bi-info-circle me-2"></i>Status: Submitted</li>
                        </ul>
                    </div>
                `,
                confirmButtonText: 'OK'
            });
    
            // Update the table data without refreshing
            if (this.dataTable) {
                const rowData = this.dataTable.rows().data().toArray();
                const updatedData = rowData.map(row => {
                    if (row.id === invoiceId) {
                        return {
                            ...row,
                            status: 'Submitted',
                            date_submitted: submissionTime,
                            date_sync: row.date_sync
                        };
                    }
                    return row;
                });
    
                // Update table without full refresh
                this.dataTable.clear();
                this.dataTable.rows.add(updatedData);
                this.dataTable.draw();
    
                // Update card counts
                this.updateCardCounts();
            }
    
        } catch (error) {
            console.error('Error in submitToLHDN:', error);
    
            let errorMessage = error.message || 'An unexpected error occurred';
            let errorDetails = error.details || [];
    
            // Add more specific error handling
            if (error.status === 500) {
                errorMessage = 'Internal server error occurred. Please try again later.';
                console.error('Full error details:', error);
    
                // Log the raw error data if available
                if (error.raw) {
                    console.error('Raw error data:', error.raw);
                }
            }
    
            await Swal.fire({
                icon: 'error',
                title: 'Submission Failed',
                html: `
                    <div class="text-start">
                        <p class="mb-2">${errorMessage}</p>
                        ${errorDetails.length > 0 ? `
                            <div class="mt-3 p-2 bg-light rounded">
                                <small class="text-muted">
                                    ${errorDetails.map(d => `
                                        <div class="mb-1">
                                            <strong>${d.code || 'Error'}:</strong> ${d.message}
                                        </div>
                                    `).join('')}
                                </small>
                            </div>
                        ` : ''}
                    </div>
                `,
                confirmButtonText: 'OK'
            });
        }
    }

    async fetchCompanyDetails() {
        const response = await fetch('/bqe/company');
            if (!response.ok) {
            throw new Error('Failed to fetch company details');
        }
        return await response.json();
    }

    async fetchSupplierDetails() {
        const companyData = await this.fetchCompanyDetails();
            return {
            tin: companyData.customFields?.find(f => f.label === "Supplier's TIN")?.value,
            registrationNumber: companyData.customFields?.find(f => f.label === "Supplier's Registration No")?.value,
            sstId: companyData.customFields?.find(f => f.label === "Supplier's SST No")?.value,
            msicCode: companyData.customFields?.find(f => f.label === "Supplier's MSIC Code")?.value,
            businessActivity: companyData.customFields?.find(f => f.label === "Supplier's Business Activity")?.value
        };
    }

    // Add this method to InvoiceTableManager class
    async showVersionSelectionDialog() {
        return Swal.fire({
            title: 'Select Document Version',
            html: `
                <div class="notice-container mb-4 text-center" style="background-color: #f0f9ff; border-radius: 8px; padding: 16px 20px;">
                    <div class="d-flex align-items-center justify-content-center mb-1">
                        <i class="bi bi-info-circle" style="color: #3b82f6; font-size: 0.9rem;"></i>
                        <div style="color: #64748b; font-weight: 500; font-size: 0.9rem; margin-left: 6px;">Important Notice</div>
                    </div>
                    <p class="mb-0" style="color: #475569; line-height: 1.5; font-size: 0.85rem;">
                        Please select the appropriate document version based on your signing requirements. Your 
                        selection will determine the final document format and processing workflow.
                    </p>
            </div>
                
                <div class="version-options">
                    <div class="version-option mb-3">
                        <label class="w-100 p-0 rounded cursor-pointer position-relative" style="border: 1px solid #e2e8f0;">
                            <div class="d-flex align-items-start p-3">
                                <input type="radio" name="version" value="1.0" checked
                                       class="form-check-input mt-1" style="width: 16px; height: 16px;">
                                <div class="ms-3 text-center w-100">
                                    <div class="mb-1 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-file-text me-2" style="color: #3b82f6;"></i>
                                        <span style="color: #334155; font-weight: 500;">Version 1.0 (Standard Format)</span>
            </div>
                                    <div style="color: #64748b; font-size: 0.85rem;">
                                        Basic document format without digital signature capabilities
                                    </div>
                                </div>
                            </div>
                        </label>
                    </div>

                    <div class="version-option mb-3">
                        <label class="w-100 p-0 rounded cursor-pointer position-relative" style="border: 1px solid #e2e8f0; background-color: #f8fafc;">
                            <div class="d-flex align-items-start p-3">
                                <input type="radio" name="version" value="1.1" disabled
                                       class="form-check-input mt-1" style="width: 16px; height: 16px;">
                                <div class="ms-3 text-center w-100">
                                    <div class="mb-1 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-file-earmark-lock me-2" style="color: #94a3b8;"></i>
                                        <span style="color: #94a3b8; font-weight: 500;">Version 1.1 (Enhanced Security)</span>
            </div>
                                    <div style="color: #94a3b8; font-size: 0.85rem;">
                                        Advanced format with digital signature support
                                    </div>
                                </div>
                                <div class="tooltip-wrapper" style="margin-left: 8px;">
                                    <i class="bi bi-info-circle" 
                                       style="cursor: help; font-size: 1rem; color: #3b82f6;"
                                       data-bs-toggle="tooltip" 
                                       data-bs-html="true"
                                       data-bs-placement="right"
                                       title="<div style='min-width: 240px; text-align: left;'>
                                         <div style='margin-bottom: 8px; color: #000;'>
                                           Digital Certificate Required <span style='color: #dc2626;'>*</span>
                                         </div>
                                         <div style='color: #64748b; margin-bottom: 12px;'>
                                           To enable this version, please complete:
                                         </div>
                                         <div style='margin-left: 0;'>
                                           <div style='margin-bottom: 8px;'>
                                             <i class='bi bi-shield' style='color: #64748b; margin-right: 8px;'></i>
                                             <span style='color: #000;'>Must have digital certificate</span>
                                             <span style='color: #dc2626;'>*</span>
                                           </div>
                                           <div style='margin-bottom: 8px;'>
                                             <i class='bi bi-person-badge' style='color: #64748b; margin-right: 8px;'></i>
                                             <span style='color: #000;'>Set up signing credentials</span>
                                             <span style='color: #dc2626;'>*</span>
                                           </div>
                                           <div style='margin-bottom: 12px;'>
                                             <i class='bi bi-gear' style='color: #64748b; margin-right: 8px;'></i>
                                             <span style='color: #000;'>Configure signature parameters</span>
                                             <span style='color: #dc2626;'>*</span>
                                           </div>
                                         </div>
                                         <div style='color: #64748b; padding-top: 12px; border-top: 1px solid #e2e8f0;'>
                                           <i class='bi bi-headset'></i>
                                           <span style='margin-left: 8px;'>Contact administrator for assistance</span>
                                         </div>
                                       </div>"></i>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '<i class="bi bi-arrow-right-circle me-2"></i>Continue',
            cancelButtonText: '<i class="bi bi-x me-2"></i>Cancel',
            customClass: {
                container: 'version-selection-dialog',
                popup: 'shadow-sm rounded-3',
                confirmButton: 'btn btn-primary px-4',
                cancelButton: 'btn btn-secondary px-4',
                actions: 'gap-2'
            },
            buttonsStyling: false,
            preConfirm: () => {
                return document.querySelector('input[name="version"]:checked')?.value;
            }
        });
    }

    // getCustomFieldValue(fields, label) {
    //     if (!fields || !Array.isArray(fields)) {
    //         return 'NA';
    //     }
        
    //     const field = fields.find(f => f.label === label);
        
    //     if (!field) {
    //         return 'NA';
    //     }

    //     if (field.value !== undefined && field.value !== null) {
    //         return field.value;
    //     }
        
    //     if (field.description !== undefined && field.description !== null) {
    //         if (label.includes('MSIC') || label.includes('TAX TYPE')) {
    //             return field.description.split('|')[0]?.trim() || field.description;
    //         }
    //         return field.description;
    //     }

    //     return 'NA';
    // }

    // Add this method to InvoiceTableManager class
    validateMandatoryFields(invoice) {
        const requiredFields = {
            'Invoice Number': invoice._rawInvoice?.invoiceNumber,
            'Invoice Date': invoice._rawInvoice?.date,
            'Invoice Amount': invoice._rawInvoice?.invoiceAmount,
            'Supplier Name': invoice.company?.name,
            'Supplier TIN': invoice.supplier?.tin,
            'Supplier Registration': invoice.supplier?.registrationNumber,
            'Customer Name': invoice._clientDetails?.company,
            'Customer TIN': invoice._clientDetails?.taxId,
           // 'Customer Registration': invoice._clientDetails?.registrationNumber,
        };

        const missingFields = [];
        for (const [field, value] of Object.entries(requiredFields)) {
            if (!value || value === 'NA') {
                missingFields.push(field);
            }
        }

        return {
            isValid: missingFields.length === 0,
            missingFields
        };
    }

    // Add this method to the InvoiceTableManager class
    updateTotalInvoicesFetched(count = 0) {
        try {
            // Update the records count display
            const recordsCount = document.querySelector('.records-count');
            if (recordsCount) {
                recordsCount.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span>Total Invoices Fetched: ${count}</span>
                        <i class="bi bi-question-circle ms-2" 
                           data-bs-toggle="tooltip" 
                           title="Total number of invoices found for selected date range"></i>
                    </div>`;
                
                // Initialize tooltip on the newly added icon
                const tooltipIcon = recordsCount.querySelector('[data-bs-toggle="tooltip"]');
                if (tooltipIcon) {
                    new bootstrap.Tooltip(tooltipIcon, {
                        placement: 'top',
                        trigger: 'hover'
                    });
                }
            }

            // Update table info if available
            const tableInfo = document.querySelector('.dataTables_info');
            if (tableInfo && this.dataTable) {
                const pageInfo = this.dataTable.page.info();
                if (pageInfo) {
                    const currentPage = pageInfo.page + 1;
                    const totalPages = pageInfo.pages;
                    tableInfo.textContent = `Showing ${count} entries (Page ${currentPage} of ${totalPages})`;
                }
            }

            // Update card counts
            this.updateCardCounts();

        } catch (error) {
            console.error('Error updating total invoices count:', error);
        }
    }

    // Add this method to handle saving invoices in batches
    async saveInvoicesInBatches(invoices, batchSize = 5) {
        try {
            const batches = [];
            for (let i = 0; i < invoices.length; i += batchSize) {
                batches.push(invoices.slice(i, i + batchSize));
            }

            console.log(`Saving ${invoices.length} invoices in ${batches.length} batches`);
            
            let savedCount = 0;
            const results = [];

            for (let i = 0; i < batches.length; i++) {
                try {
                    const batch = batches[i];
                    const saveResponse = await fetch('/bqe/save-invoices', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ invoices: batch })
                    });

                    if (!saveResponse.ok) {
                        throw new Error(`Failed to save batch ${i + 1}`);
                    }

                    const saveResult = await saveResponse.json();
                    results.push(saveResult);
                    savedCount += batch.length;

                } catch (error) {
                    console.error(`Error saving batch ${i + 1}:`, error);
                    // Continue with next batch even if one fails
                }
            }

            return {
                success: true,
                totalSaved: savedCount,
                results: results
            };

        } catch (error) {
            console.error('Error in saveInvoicesInBatches:', error);
            throw new Error(`Failed to save invoices: ${error.message}`);
        }
    }

    // Add this method to format relative time
    formatRelativeTime(date) {
        if (!date) return '-';
        
        try {
            const now = moment();
            const syncDate = moment(date);
            const diffMinutes = now.diff(syncDate, 'minutes');
            
            if (diffMinutes < 1) return 'just now';
            if (diffMinutes < 60) return `${diffMinutes} minutes ago`;
            
            const diffHours = now.diff(syncDate, 'hours');
            if (diffHours < 24) return `${diffHours} hours ago`;
            
            const diffDays = now.diff(syncDate, 'days');
            if (diffDays < 30) return `${diffDays} days ago`;
            
            return syncDate.format('YYYY-MM-DD');
        } catch (error) {
            console.error('Error formatting relative time:', error);
            return '-';
        }
    }

    // prepareInvoiceData(invoiceId) {
    //     try {
    //         // Get the invoice data from the DataTable
    //         const invoice = this.dataTable.rows().data().toArray()
    //             .find(row => row.id === invoiceId);

    //         if (!invoice) {
    //             throw new Error('Invoice not found');
    //         }

    //         // Ensure _rawInvoice has the correct structure
    //         const rawInvoice = {
    //             id: invoiceId, // Add the invoice ID here
    //             invoiceNumber: invoice._rawInvoice.invoiceNumber || invoice.invoice_number,
    //             date: invoice._rawInvoice.date,
    //             invoiceAmount: invoice._rawInvoice.invoiceAmount || parseFloat(invoice.amount.replace('MYR ', '')),
    //             serviceTaxAmount: invoice._rawInvoice.serviceTaxAmount || 0,
    //             currency: invoice._rawInvoice.currency || 'MYR',
    //             type: invoice._rawInvoice.type || 13,
    //             invoiceFrom: invoice._rawInvoice.invoiceFrom,
    //             invoiceTo: invoice._rawInvoice.invoiceTo,
    //             rfNumber: invoice._rawInvoice.rfNumber || `RF${invoice.invoice_number}`,
    //             referenceNumber: invoice._rawInvoice.referenceNumber,
    //             referenceType: invoice._rawInvoice.referenceType,
    //             referenceDescription: invoice._rawInvoice.referenceDescription
    //         };
          
    //         // Return the prepared data structure
    //         return {
    //             id: invoiceId, 
    //             _rawInvoice: rawInvoice,
    //             _clientDetails: invoice._clientDetails,
    //             company: invoice.company,
    //             supplier: {
    //                 tin: invoice.supplier?.tin || invoice.company?.customFields?.find(f => 
    //                     f.label === "Supplier's TIN"
    //                 )?.value,
    //                 registrationNumber: invoice.supplier?.registrationNumber || invoice.company?.customFields?.find(f => 
    //                     f.label === "Supplier's Registration No"
    //                 )?.value,
    //                 sstId: invoice.supplier?.sstId || invoice.company?.customFields?.find(f => 
    //                     f.label === "Supplier's SST No"
    //                 )?.value,
    //                 msicCode: invoice.supplier?.msicCode || invoice.company?.customFields?.find(f => 
    //                     f.label === "Supplier's MSIC Code"
    //                 )?.value,
    //                 businessActivity: invoice.supplier?.businessActivity || "Engineering Services"
    //             },
    //             invoiceDetails: invoice._rawInvoice.invoiceDetails || [{
    //                 description: invoice._rawInvoice.invoiceDetails?.[0]?.memo1 || 'Service Fee',
    //                 amount: invoice._rawInvoice.serviceAmount || parseFloat(invoice.amount.replace('MYR ', '')),
    //                 taxRate: parseFloat(invoice._rawInvoice.customFields?.find(f => f.label === 'Tax Rate')?.value) || 0,
    //                 project: invoice._rawInvoice.invoiceDetails?.[0]?.project
    //             }],
    //             lineItems: invoice._rawInvoice.lineItems || [{
    //                 description: invoice._rawInvoice.invoiceDetails?.[0]?.memo1 || 'Service Fee',
    //                 amount: invoice._rawInvoice.serviceAmount || parseFloat(invoice.amount.replace('MYR ', '')),
    //                 taxRate: parseFloat(invoice._rawInvoice.customFields?.find(f => f.label === 'Tax Rate')?.value) || 0,
    //                 project: invoice._rawInvoice.invoiceDetails?.[0]?.project
    //             }]
    //         };
    //     } catch (error) {
    //         console.error('Error preparing invoice data:', error);
    //         throw new Error(`Failed to prepare invoice data: ${error.message}`);
    //     }
    // }
    prepareInvoiceData(invoiceId) {
        try {
            // Get the invoice data from the DataTable
            const invoice = this.dataTable.rows().data().toArray()
                .find(row => row.id === invoiceId);
    
            if (!invoice) {
                throw new Error('Invoice not found');
            }
    
            // Get payment info from company custom fields
            const paymentInfo = this.getPaymentInfo(invoice);
            
            // Get tax rate and tax information
            const taxRate = this.getTaxRate(invoice);
            const taxExemption = this.calculateTaxExemption(invoice);
    
            // Extract project details and tax type
            const projectDetails = invoice._projectDetailsArray?.[0]?.details;
            const taxTypeField = projectDetails?.customFields?.find(f => f.label === 'TAX TYPE (CODE)');
            const taxType = taxTypeField?.description?.split('|')?.[0]?.trim() || '';
    
            // Calculate payment status
            const payments = invoice.payments || [];
            const totalPaid = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
            const paymentDates = payments.map(p => new Date(p.paymentDate));
            const lastPaymentDate = paymentDates.length > 0 ? Math.max(...paymentDates) : null;
    
            // Create the raw invoice structure
            const rawInvoice = {
                invoiceNumber: invoice._rawInvoice.invoiceNumber || invoice.invoice_number,
                date: invoice._rawInvoice.date,
                dueDate: invoice._rawInvoice.dueDate,
                invoiceAmount: invoice._rawInvoice.invoiceAmount,
                serviceTaxAmount: invoice._rawInvoice.serviceTaxAmount || 0,
                currency: invoice._rawInvoice.currency || 'MYR',
                type: invoice._rawInvoice.type || 13,
                invoiceFrom: invoice._rawInvoice.invoiceFrom,
                invoiceTo: invoice._rawInvoice.invoiceTo,
                rfNumber: invoice._rawInvoice.rfNumber || `RF${invoice.invoice_number}`,
                version: invoice.version || '1.0',
                messageOnInvoice: paymentInfo,
                customFields: [{
                    label: 'Tax Rate',
                    value: taxRate.toString(),
                    description: null
                }]
            };
        
            // Prepare the complete data structure
            const preparedData = {
                _rawInvoice: rawInvoice,
                _clientDetails: invoice._clientDetails,
                company: invoice.company,
                supplier: {
                    tin: invoice.supplier?.tin,
                    registrationNumber: invoice.supplier?.registrationNumber,
                    sstId: invoice.supplier?.sstId,
                    msicCode: invoice.supplier?.msicCode,
                    businessActivity: invoice.supplier?.businessActivity || "Engineering Services"
                },
                tax_info: {
                    taxRate: taxRate,
                    taxType: taxType,
                    taxExemption: taxExemption,
                },
                _projectDetailsArray: invoice._projectDetailsArray,
                _invoiceDetails: invoice._invoiceDetails?.map(detail => ({
                    ...detail,
                    taxRate: taxRate,
                    clientId: detail.clientId,
                    projectId: detail.projectId,
                    memo1: detail.memo1,
                    serviceAmount: detail.serviceAmount,
                    serviceTaxAmount: detail.serviceTaxAmount,
                    expenseAmount: detail.expenseAmount || 0,
                    expenseTaxAmount: detail.expenseTaxAmount || 0
                })) || [],
                // Add payment information
                payments: payments.map(payment => ({
                    id: payment.id,
                    paymentDate: payment.paymentDate,
                    amount: payment.amount,
                    referenceNumber: payment.referenceNumber,
                    paymentMethod: payment.paymentMethod?.name,
                    status: payment.status,
                    account: payment.account?.name,
                    customFields: payment.customFields || []
                })),
                paymentStatus: {
                    totalPaid: totalPaid,
                    hasPayments: payments.length > 0,
                    lastPaymentDate: lastPaymentDate,
                    isPaid: totalPaid >= invoice._rawInvoice.invoiceAmount,
                    remainingAmount: invoice._rawInvoice.invoiceAmount - totalPaid
                }
            };
    
            // Add detailed console logging
            console.log('Prepared Invoice Data:', {
                rawInvoice: preparedData._rawInvoice,
                clientDetails: preparedData._clientDetails,
                company: preparedData.company,
                supplier: preparedData.supplier,
                taxInfo: preparedData.tax_info,
                projectDetails: preparedData._projectDetailsArray,
                invoiceDetails: preparedData._invoiceDetails,
                payments: preparedData.payments,
                paymentStatus: preparedData.paymentStatus
            });
    
            return preparedData;
    
        } catch (error) {
            console.error('Error preparing invoice data:', error);
            throw new Error(`Failed to prepare invoice data: ${error.message}`);
        }
    }
    
    async checkStagingDatabase() {
        const fromDate = document.getElementById('fromDate')?.value;
        const toDate = document.getElementById('toDate')?.value;

        const stagingResponse = await fetch('/bqe/check-staging', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ fromDate, toDate })
        });

        const stagingData = await stagingResponse.json();

        // Create staging map with all available data
        const stagingMap = {};
        if (stagingData.hasData) {
            stagingData.invoices.forEach(inv => {
                if (inv.invoice_number) {
                    stagingMap[inv.invoice_number] = {
                        status: inv.status || 'Pending',
                        date_submitted: inv.date_submitted,
                        date_sync: inv.date_sync,
                    };
                }
            });
        }

        return { stagingData, stagingMap };
    }

    mapInvoicesWithStagingData(invoices, { stagingMap }) {
        return invoices.map(invoice => {
            const stagingInfo = stagingMap[invoice.invoice_number];
            
            if (stagingInfo) {
                // For submitted invoices
                if (stagingInfo.status === 'Submitted') {
                    return {
                        ...invoice,
                        status: stagingInfo.status,
                        date_submitted: stagingInfo.date_submitted,
                        date_sync: stagingInfo.date_sync,
                        submission_timestamp: stagingInfo.submission_timestamp
                    };
                }
                
                // For other statuses (Pending, Cancelled)
                return {
                    ...invoice,
                    status: stagingInfo.status,
                    date_submitted: null,
                    date_sync: stagingInfo.date_sync,
                    submission_timestamp: null
                };
            }

            // For new invoices not in staging
            return {
                ...invoice,
                status: 'Pending',
                date_submitted: null,
                date_sync: moment().format('YYYY-MM-DD HH:mm:ss'),
                submission_timestamp: null
            };
        });
    }

    async saveAndUpdateUI(mappedInvoices) {
        try {
            // First save the invoices
            const saveResults = await this.saveInvoicesInBatches(mappedInvoices);
            console.log('Save results:', saveResults);

            // After saving, get fresh staging data
            const { stagingMap } = await this.checkStagingDatabase();

            // Re-map the invoices with the fresh staging data
            const updatedInvoices = mappedInvoices.map(invoice => {
                const stagingInfo = stagingMap[invoice.invoice_number];
                if (stagingInfo) {
                    // Use staging data for timestamps and status
                    return {
                        ...invoice,
                        status: stagingInfo.status,
                        date_submitted: stagingInfo.date_submitted,
                        date_sync: stagingInfo.date_sync,
                    };
                }
                return invoice;
            });

            // Update the table with the re-mapped data
            this.dataTable.clear();
            this.dataTable.rows.add(updatedInvoices);
            this.dataTable.draw();

            requestAnimationFrame(() => {
                this.updateCardCounts();
                this.updateTotalInvoicesFetched(updatedInvoices.length);
            });
        } catch (error) {
            console.error('Error in saveAndUpdateUI:', error);
            throw error;
        }
    }

    // Add these methods to the InvoiceTableManager class
    calculateDuration() {
        if (!this.startTime) return '';
        const duration = Date.now() - this.startTime;
        
        if (duration < 1000) {
            return 'less than a second';
        } else if (duration < 60000) {
            const seconds = Math.floor(duration / 1000);
            return `${seconds} second${seconds !== 1 ? 's' : ''}`;
        } else {
            const minutes = Math.floor(duration / 60000);
            const seconds = Math.floor((duration % 60000) / 1000);
            return `${minutes} minute${minutes !== 1 ? 's' : ''} ${seconds} second${seconds !== 1 ? 's' : ''}`;
        }
    }

    calculateEstimatedTimeLeft(currentStep, totalSteps) {
        if (!this.startTime || currentStep === 0) return '';
        
        const elapsedTime = Date.now() - this.startTime;
        const averageTimePerStep = elapsedTime / currentStep;
        const remainingSteps = totalSteps - currentStep;
        const estimatedTimeLeft = averageTimePerStep * remainingSteps;
        
        if (estimatedTimeLeft < 5000) return '';
        
        if (estimatedTimeLeft < 60000) {
            const seconds = Math.ceil(estimatedTimeLeft / 1000);
            return ` • Est. ${seconds}s remaining`;
        } else {
            const minutes = Math.ceil(estimatedTimeLeft / 60000);
            return ` • Est. ${minutes}m remaining`;
        }
    }

    // Add this method to handle loading dots animation
    updateLoadingDots(element) {
        if (!this.loadingDotsInterval) {
            let dots = 0;
            this.loadingDotsInterval = setInterval(() => {
                if (element && document.body.contains(element)) {
                    const currentText = element.textContent.replace(/\.+$/, '');
                    dots = (dots + 1) % 4;
                    element.textContent = currentText + '.'.repeat(dots);
                } else {
                    // Clear interval if element is no longer in the DOM
                    clearInterval(this.loadingDotsInterval);
                    this.loadingDotsInterval = null;
                }
            }, 500);
        }
    }

    async cancelInvoice(invoiceId) {
        try {
            // Get the invoice data from the DataTable
            const invoice = this.dataTable.rows().data().toArray()
                .find(row => row.id === invoiceId);

            if (!invoice) {
                throw new Error('Invoice not found');
            }

            // Show confirmation dialog
            const confirmResult = await Swal.fire({
                title: 'Cancel Invoice',
                html: `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Are you sure you want to cancel this invoice?
                    </div>
                    <div class="mt-3">
                        <strong>Invoice Details:</strong><br>
                        Invoice Number: ${invoice.invoice_number}<br>
                        Date Submitted: ${moment(invoice.date_submitted).format('DD-MM-YYYY HH:mm:ss')}<br>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, cancel it',
                cancelButtonText: 'No, keep it',
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d'
            });

            if (!confirmResult.isConfirmed) {
                return;
            }

            // Show loading state
            Swal.fire({
                title: 'Cancelling Invoice',
                html: 'Please wait while we process your request...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Make the cancellation request
            const response = await fetch('/bqe/cancel-invoice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    invoiceId,
                    invoiceNumber: invoice.invoice_number
                })
            });

            const cancelResult = await response.json();

            if (!response.ok) {
                throw new Error(cancelResult.message || 'Failed to cancel invoice');
            }

            // Close the modal first
            const modal = bootstrap.Modal.getInstance(document.getElementById('viewDetailsModal'));
            if (modal) {
                modal.hide();
            }

            // Show success message
            await Swal.fire({
                icon: 'success',
                title: 'Success!',
                html: `
                    <div class="alert alert-success mb-3">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        Invoice successfully cancelled
                    </div>
                    <div class="text-start">
                        <strong>Details:</strong>
                        <ul class="list-unstyled mt-2 mb-0">
                            <li><i class="bi bi-receipt me-2"></i>Invoice: ${invoice.invoice_number}</li>
                            <li><i class="bi bi-calendar me-2"></i>Date: ${moment().format('DD MMM YYYY, HH:mm:ss')}</li>
                            <li><i class="bi bi-info-circle me-2"></i>Status: Cancelled</li>
                        </ul>
                    </div>
                `,
                confirmButtonText: 'OK'
            });

            // Update the table data without refreshing
            const table = this.dataTable;
            if (table) {
                const rowData = table.rows().data().toArray();
                const updatedData = rowData.map(row => {
                    if (row.id === invoiceId) {
                        return {
                            ...row,
                            status: 'Cancelled',
                            date_sync: moment().format('YYYY-MM-DD HH:mm:ss')
                        };
                    }
                    return row;
                });

                // Update table without full refresh
                table.clear();
                table.rows.add(updatedData);
                table.draw();

                // Update card counts
                this.updateCardCounts();
            }

        } catch (error) {
            console.error('Error in cancelInvoice:', error);
            
            await Swal.fire({
                icon: 'error',
                title: 'Cancellation Failed',
                html: `
                    <div class="text-start">
                        <p class="mb-2">${error.message}</p>
                    </div>
                `,
                confirmButtonText: 'OK'
            });
        }
    }

} 

// Add this function to handle address toggle
function toggleAddress(btn) {
    const container = btn.previousElementSibling;
    container.classList.toggle('expanded');
    btn.textContent = container.classList.contains('expanded') ? 'View less' : 'View more';
} 

// Add this helper function to format address
function formatAddress(address) {
    if (!address) return null;
    
    const parts = [
        address.street1,
        address.street2,
        address.city,
        address.state,
        address.zip,
        address.country
    ].filter(part => part && part.trim());
    
    return parts.length > 0 ? parts.join(', ') : null;
} 


// Add this to properly handle modal closing
document.addEventListener('DOMContentLoaded', function() {
    const viewDetailsModal = document.getElementById('viewDetailsModal');
    if (viewDetailsModal) {
        viewDetailsModal.addEventListener('hidden.bs.modal', function () {
            // Clear current invoice data
            window.invoiceTable._currentInvoice = null;

            // Reset modal content
            const loadingSpinner = document.getElementById('loadingSpinner');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'block';
            }

            const lineItemsContainer = document.getElementById('lineItemsBody');
            if (lineItemsContainer) {
                lineItemsContainer.innerHTML = '';
            }

            const modalFields = [
                'modalInvoiceNumber', 'modalInvoiceDate', 'modalInvoiceVersion', 'modalInvoiceType',
                'modalInvoiceStatus', 'modalSupplierName', 'modalSupplierTin', 'modalSupplierBrn',
                'modalSupplierSst', 'modalBuyerName', 'modalBuyerTin', 'modalBuyerBrn', 'modalBuyerAddress'
            ];

            modalFields.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = '-';
                }
            });

            const statusElement = document.getElementById('modalInvoiceStatus');
            if (statusElement) {
                statusElement.innerHTML = '';
            }

            const submitButton = document.getElementById('submitToLhdnBtn');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-send"></i> Submit to LHDN';
            }
        });
    }
});

window.InvoiceTableManager = InvoiceTableManager;