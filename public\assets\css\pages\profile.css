.divider {
    border-bottom: 1px solid #dee2e6;
    margin: 10px 0;
  }

/* Profile Container */
.profile-container {
  max-width: 1800px;
  margin: 0 auto;
}

/* Welcome Card */
.profile-welcome-card {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #fff;
  padding: 2rem;
  padding-top: 2.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  margin-top: -1.5rem;
}

.profile-welcome-card h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.profile-welcome-card p {
  margin: 5px 0 0;
  opacity: 0.9;
}

/* Profile Layout */
.profile-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 25px;
}

/* Sidebar */
.profile-sidebar {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 25px;
  height: fit-content;
  position: sticky;
  top: 20px;
}

/* Enhanced Profile Image Container */
.profile-image-container {
  position: relative;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.image-wrapper {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
}

/* Enhanced Profile Image */
.profile-image {
  width: 100%;
  height: 200px;
  object-fit: contain;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e9ecef;
  padding: 10px;
  transition: all 0.3s ease;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.image-overlay button {
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.image-overlay button:hover {
  background: #c82333;
  transform: scale(1.05);
}

.profile-info {
  text-align: center;
  width: 100%;
}

.profile-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c384e;
  margin: 15px 0 10px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.profile-email {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 10px;
}
.profile-tin,
.profile-email,
.profile-phone,
.profile-industry,
.profile-country,
.profile-brn {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  text-align: left;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  background-color: #f8f9fa;
}

.profile-tin:hover,
.profile-brn:hover,
.profile-email:hover,
.profile-phone:hover {
  background-color: #e9ecef;
}

.profile-tin i,
.profile-email i,
.profile-phone i,
.profile-industry i,
.profile-country i,
.profile-brn i {
  font-size: 1rem;
  color: #0d6efd;
  width: 16px;
  text-align: center;
}

.profile-badge {
  display: inline-block;
  padding: 5px 12px;
  background: #e8f3ff;
  color: #0d6efd;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Main Content */
.profile-details {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 25px;
}

.profile-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
}

.profile-details-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c384e;
  margin: 0;
}

/* Buttons */
.profile-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #0d6efd;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-btn:hover {
  background-color: #0b5ed7;
}

.profile-btn-sm {
  padding: 4px 8px;
  font-size: 0.875rem;
}

.profile-btn-danger {
  background-color: #dc3545;
}

.profile-btn-danger:hover {
  background-color: #bb2d3b;
}

.profile-btn-upload {
  background-color: transparent;
  border: 1px solid #0d6efd;
  color: #0d6efd;
}

.profile-btn-upload:hover {
  background-color: #0d6efd;
  color: white;
}

/* Secondary button style */
.profile-btn[style*="background: #6c757d"] {
  background: #6c757d !important;
}

.profile-btn[style*="background: #6c757d"]:hover {
  background: #5a6268 !important;
}

/* Danger button style */
.profile-btn[style*="background: #dc3545"] {
  background: #dc3545 !important;
}

.profile-btn[style*="background: #dc3545"]:hover {
  background: #c82333 !important;
}

/* Button group spacing */
.profile-image-upload-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-btn {
    width: 100%;
    margin: 5px 0;
  }
  
  .profile-image-upload-buttons {
    flex-direction: column;
  }
  
  .profile-btn-upload {
    margin: 5px 0;
  }
}

/* Info Grid */
.profile-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.profile-info-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-top: 10px;
}

.profile-info-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #2c384e;
  margin: 0 0 15px;
}

.profile-info-item {
  margin-bottom: 15px;
}

.profile-info-label {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 5px;
}

.profile-info-value {
  font-size: 0.95rem;
  color: #2c384e;
  font-weight: 500;
}

/* Forms */
.profile-edit-form {
  display: none;
}

.profile-edit-form.active {
  display: block;
}

.profile-form-group {
  margin-bottom: 20px;
}

.profile-form-label {
  display: block;
  font-size: 0.9rem;
  color: #2c384e;
  margin-bottom: 8px;
  font-weight: 500;
}

.profile-form-control {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.profile-form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);
  outline: none;
}

/* Utilities */
.profile-important {
  color: red;
  margin-left: 3px;
}

.profile-separator {
  margin: 30px 0;
  border-top: 1px solid #eee;
}

/* Responsive */
@media (max-width: 992px) {
  .profile-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .profile-info-grid {
    grid-template-columns: 1fr;
  }
}

.edit-mode {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-upload-label {
  display: inline-block;
  margin-top: 10px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-upload-label:hover {
  background: #e9ecef;
}

.image-upload-label i {
  margin-right: 8px;
}

.profile-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.form-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #495057;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

.btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #007bff;
  color: #fff;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: #fff;
}

.btn-secondary:hover {
  background: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-form {
    padding: 15px;
  }

  .form-section {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    margin-bottom: 10px;
  }
}

.profile-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.profile-input-group .profile-form-control {
  flex: 1;
  padding-right: 40px; /* Space for the edit icon */
}


.profile-info-item .profile-input-group {
  position: relative;
  display: flex;
  align-items: center;
  padding-right: 40px;
}

.profile-info-item .profile-edit-icon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 50%;
}

.profile-info-item .profile-edit-icon:hover {
  color: #0d6efd;
  background-color: #e9ecef;
}

.profile-info-item .profile-edit-icon i {
  font-size: 1.1rem;
}

.profile-input-group {
  display: flex;
  align-items: center;
}

.profile-info-value {
  flex: 1;
}

.profile-edit-icon {
  background: none;
  border: none;
  color: #6c757d;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.profile-edit-icon:hover {
  color: #0d6efd;
}

.profile-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.profile-form-control {
  width: 100%;
  padding: 8px 35px 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  color: #495057;
  background-color: #fff;
}

.profile-form-control:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

/* Add styles for the edit icon container */
.profile-edit-icon-container {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 10px;
}

/* Style for the icon itself */
.profile-edit-icon i {
  font-size: 14px;
}

/* Add hover effect for the edit button */
.profile-edit-icon:hover {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
  border-radius: 4px;
}

/* Add focus styles */
.profile-edit-icon:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
}

/* Add active state */
.profile-edit-icon:active {
  transform: translateY(-50%) scale(0.95);
}

.image-upload-controls {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

/* No Image Placeholder */
.profile-image[src="/assets/img/noimage.png"] {
  opacity: 0.7;
  background-color: #f8f9fa;
  padding: 20px;
  object-fit: contain;
}

/* Loading State */
.profile-image.loading {
  opacity: 0.6;
  filter: grayscale(50%);
}

/* Preview Container */
.image-preview-container {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

/* Image Upload Button */
#uploadImageBtn {
  width: auto;
  padding: 10px 20px;
  font-size: 0.9rem;
  color: #0d6efd;
  background: #fff;
  border: 1px solid #0d6efd;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  margin-top: 10px;
  cursor: pointer;
}

#uploadImageBtn:hover {
  background: #0d6efd;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(13, 110, 253, 0.2);
}

#uploadImageBtn:active {
  transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .profile-image-container {
    max-width: 250px;
    padding: 10px;
  }

  .profile-image {
    height: 180px;
  }

  #uploadImageBtn {
    padding: 8px 16px;
    font-size: 0.85rem;
  }
}

.settings-container {
  max-width: 1800px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Welcome Card */
.profile-welcome-card {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #fff;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
}

.profile-welcome-card h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.profile-welcome-card p {
  margin: 5px 0 0;
  opacity: 0.9;
}


/* Left Sidebar */
.settings-nav-card {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 0;
  height: fit-content;
}

.company-info {
  padding: 20px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #2c384e;
  display: flex;
  align-items: center;
  gap: 10px;
}

.company-profile {
  padding: 20px;
  color: #2c384e;
}


/* Company Details */
.company-name {
  text-align: center;
  margin-bottom: 20px;
}

.company-name h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c384e;
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  font-size: 0.9rem;
}

.detail-item i {
  color: #0d6efd;
  width: 16px;
  text-align: center;
}

/* Main Content */
.settings-form-section {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 25px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c384e;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-title i {
  color: #0d6efd;
}

/* Alert */
.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 10px;
  background: #cfe2ff;
  border: 1px solid #b6d4fe;
  color: #084298;
}

/* Section Styles */
.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2c384e;
  margin: 30px 0 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-title i {
  color: #0d6efd;
}

.info-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
}

.info-item {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.item-label {
  color: #2c384e;
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.required {
  color: #dc3545;
}

.item-value {
  color: #6c757d;
  margin-bottom: 8px;
}

.item-description {
  font-size: 0.875rem;
  color: #6c757d;
}


@media (max-width: 768px) {
  .settings-container {
    padding: 0 15px;
  }
  
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .content-header .btn {
    width: 100%;
  }
  
  .info-item {
    padding: 15px;
  }
}

/* Edit Mode Styles */
.edit-mode {
  display: none;
  margin-top: 5px;
}

.edit-mode.active {
  display: block;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-group .form-control {
  flex: 1;
}

.input-group .btn {
  padding: 6px 12px;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #495057;
  background-color: #fff;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  outline: none;
}

.form-control:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

select.form-control {
  padding-right: 24px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px 12px;
  appearance: none;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #ced4da;
  background-color: #fff;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

/* Responsive Edit Mode */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .btn {
    width: 100%;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .input-group .btn {
    width: 100%;
    margin-top: 8px;
  }
}

/* Edit Mode Transitions */
.view-mode,
.edit-mode {
  transition: all 0.3s ease-in-out;
  opacity: 1;
}

.view-mode.hidden,
.edit-mode.hidden {
  opacity: 0;
  height: 0;
  overflow: hidden;
}

/* Enhanced Form Controls */
.form-control {
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background-color: #f8f9fa;
}

.form-control:hover {
  background-color: #fff;
  border-color: #e9ecef;
}

.form-control:focus {
  background-color: #fff;
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.15rem rgba(13, 110, 253, 0.25);
}

/* Enhanced Info Items */
.info-item {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.info-item:hover {
  border-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.04);
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #0d6efd;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  z-index: 2;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Enhanced Section Titles */
.section-title {
  position: relative;
  padding-left: 20px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background: #0d6efd;
  border-radius: 2px;
}

/* Enhanced Profile Image */
.profile-image-container {
  position: relative;
}

.profile-image {
  transition: transform 0.3s ease, filter 0.3s ease;
}

.profile-image:hover {
  transform: scale(1.02);
}

#uploadImageBtn {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

#uploadImageBtn:hover {
  transform: translateY(-2px);
}

/* Enhanced Alert Styles */
.alert {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Validation Styles */
.form-control.is-invalid {
  border-color: #dc3545;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid {
  border-color: #198754;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}


