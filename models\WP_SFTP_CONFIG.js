const moment = require('moment');

module.exports = (sequelize, DataTypes) => {
  const WP_SFTP_CONFIG = sequelize.define('WP_SFTP_CONFIG', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    host: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    port: {
      type: DataTypes.STRING(10),
      defaultValue: '22'
    },
    username: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    root_path: {
      type: DataTypes.STRING(255),
      defaultValue: '/eInvoiceFTP'
    },
    incoming_manual_template: {
      type: DataTypes.STRING(255)
    },
    incoming_schedule_template: {
      type: DataTypes.STRING(255)
    },
    outgoing_manual_template: {
      type: DataTypes.STRING(255)
    },
    outgoing_schedule_template: {
      type: DataTypes.STRING(255)
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: () => moment().format('YYYY-MM-DD HH:mm:ss')
    },
    updatedAt: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: () => moment().format('YYYY-MM-DD HH:mm:ss')
    }
  }, {
    tableName: 'WP_SFTP_CONFIG',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['host', 'username']
      }
    ]
  });

  return WP_SFTP_CONFIG;
}; 