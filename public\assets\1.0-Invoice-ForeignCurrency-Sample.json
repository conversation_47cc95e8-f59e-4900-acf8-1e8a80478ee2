{"_D": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", "_A": "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2", "_B": "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2", "Invoice": [{"ID": [{"_": "JSON-INV12345"}], "IssueDate": [{"_": "2024-07-23"}], "IssueTime": [{"_": "00:30:00Z"}], "InvoiceTypeCode": [{"_": "01", "listVersionID": "1.0"}], "DocumentCurrencyCode": [{"_": "USD"}], "TaxCurrencyCode": [{"_": "MYR"}], "InvoicePeriod": [{"StartDate": [{"_": "2024-01-01"}], "EndDate": [{"_": "2024-07-31"}], "Description": [{"_": "Monthly"}]}], "BillingReference": [{"AdditionalDocumentReference": [{"ID": [{"_": "E12345678912"}]}]}], "AdditionalDocumentReference": [{"ID": [{"_": "E12345678912"}], "DocumentType": [{"_": "CustomsImportForm"}]}, {"ID": [{"_": "sa313321312"}], "DocumentType": [{"_": "213312dddddd"}], "DocumentDescription": [{"_": "asddasdwqfd ddq"}]}, {"ID": [{"_": "E12345678912"}], "DocumentType": [{"_": "K2"}]}, {"ID": [{"_": "CIF"}]}], "AccountingSupplierParty": [{"AdditionalAccountID": [{"_": "CPT-CCN-W-211111-KL-000002", "schemeAgencyName": "CertEX"}], "Party": [{"IndustryClassificationCode": [{"_": "46510", "name": "Wholesale of computer hardware, software and peripherals"}], "PartyIdentification": [{"ID": [{"_": "Supplier's TIN", "schemeID": "TIN"}]}, {"ID": [{"_": "Supplier's BRN", "schemeID": "BRN"}]}, {"ID": [{"_": "NA", "schemeID": "SST"}]}, {"ID": [{"_": "NA", "schemeID": "TTX"}]}], "PostalAddress": [{"CityName": [{"_": "Kuala Lumpur"}], "PostalZone": [{"_": "50480"}], "CountrySubentityCode": [{"_": "10"}], "AddressLine": [{"Line": [{"_": "Lot 66"}]}, {"Line": [{"_": "Bangunan Merdeka"}]}, {"Line": [{"_": "Persiaran Jaya"}]}], "Country": [{"IdentificationCode": [{"_": "MYS", "listID": "ISO3166-1", "listAgencyID": "6"}]}]}], "PartyLegalEntity": [{"RegistrationName": [{"_": "Supp<PERSON>'s Name"}]}], "Contact": [{"Telephone": [{"_": "+***********"}], "ElectronicMail": [{"_": "<EMAIL>"}]}]}]}], "AccountingCustomerParty": [{"Party": [{"PostalAddress": [{"CityName": [{"_": "Kuala Lumpur"}], "PostalZone": [{"_": "50480"}], "CountrySubentityCode": [{"_": "10"}], "AddressLine": [{"Line": [{"_": "Lot 66"}]}, {"Line": [{"_": "Bangunan Merdeka"}]}, {"Line": [{"_": "Persiaran Jaya"}]}], "Country": [{"IdentificationCode": [{"_": "MYS", "listID": "ISO3166-1", "listAgencyID": "6"}]}]}], "PartyLegalEntity": [{"RegistrationName": [{"_": "Buyer's Name"}]}], "PartyIdentification": [{"ID": [{"_": "Buyer's TIN", "schemeID": "TIN"}]}, {"ID": [{"_": "Buyer's BRN", "schemeID": "BRN"}]}, {"ID": [{"_": "NA", "schemeID": "SST"}]}, {"ID": [{"_": "NA", "schemeID": "TTX"}]}], "Contact": [{"Telephone": [{"_": "+***********"}], "ElectronicMail": [{"_": "<EMAIL>"}]}]}]}], "Delivery": [{"DeliveryParty": [{"PartyLegalEntity": [{"RegistrationName": [{"_": "Recipient's Name"}]}], "PostalAddress": [{"CityName": [{"_": "Kuala Lumpur"}], "PostalZone": [{"_": "50480"}], "CountrySubentityCode": [{"_": "10"}], "AddressLine": [{"Line": [{"_": "Lot 66"}]}, {"Line": [{"_": "Bangunan Merdeka"}]}, {"Line": [{"_": "Persiaran Jaya"}]}], "Country": [{"IdentificationCode": [{"_": "MYS", "listID": "ISO3166-1", "listAgencyID": "6"}]}]}], "PartyIdentification": [{"ID": [{"_": "Recipient's TIN", "schemeID": "TIN"}]}, {"ID": [{"_": "Recipient's BRN", "schemeID": "BRN"}]}]}], "Shipment": [{"ID": [{"_": "1234"}], "FreightAllowanceCharge": [{"ChargeIndicator": [{"_": true}], "AllowanceChargeReason": [{"_": "Service charge"}], "Amount": [{"_": 100, "currencyID": "USD"}]}]}]}], "PaymentMeans": [{"PaymentMeansCode": [{"_": "03"}], "PayeeFinancialAccount": [{"ID": [{"_": "*************"}]}]}], "PaymentTerms": [{"Note": [{"_": "Payment method is cash"}]}], "PrepaidPayment": [{"ID": [{"_": "E12345678912"}], "PaidAmount": [{"_": 1, "currencyID": "USD"}], "PaidDate": [{"_": "2024-07-23"}], "PaidTime": [{"_": "00:30:00Z"}]}], "AllowanceCharge": [{"ChargeIndicator": [{"_": false}], "AllowanceChargeReason": [{"_": "Sample Description"}], "Amount": [{"_": 100, "currencyID": "USD"}]}, {"ChargeIndicator": [{"_": true}], "AllowanceChargeReason": [{"_": "Service charge"}], "Amount": [{"_": 100, "currencyID": "USD"}]}], "TaxExchangeRate": [{"SourceCurrencyCode": [{"_": "USD"}], "TargetCurrencyCode": [{"_": "MYR"}], "CalculationRate": [{"_": 4.72}]}], "TaxTotal": [{"TaxAmount": [{"_": 87.63, "currencyID": "MYR"}], "TaxSubtotal": [{"TaxableAmount": [{"_": 87.63, "currencyID": "USD"}], "TaxAmount": [{"_": 87.63, "currencyID": "MYR"}], "TaxCategory": [{"ID": [{"_": "01"}], "TaxScheme": [{"ID": [{"_": "OTH", "schemeID": "UN/ECE 5153", "schemeAgencyID": "6"}]}]}]}]}], "LegalMonetaryTotal": [{"LineExtensionAmount": [{"_": 1436.5, "currencyID": "USD"}], "TaxExclusiveAmount": [{"_": 1436.5, "currencyID": "USD"}], "TaxInclusiveAmount": [{"_": 1436.5, "currencyID": "USD"}], "AllowanceTotalAmount": [{"_": 1436.5, "currencyID": "USD"}], "ChargeTotalAmount": [{"_": 1436.5, "currencyID": "USD"}], "PayableRoundingAmount": [{"_": 0.3, "currencyID": "USD"}], "PayableAmount": [{"_": 1436.5, "currencyID": "USD"}]}], "InvoiceLine": [{"ID": [{"_": "1234"}], "InvoicedQuantity": [{"_": 1, "unitCode": "C62"}], "LineExtensionAmount": [{"_": 1436.5, "currencyID": "USD"}], "AllowanceCharge": [{"ChargeIndicator": [{"_": false}], "AllowanceChargeReason": [{"_": "Sample Description"}], "MultiplierFactorNumeric": [{"_": 0.15}], "Amount": [{"_": 100, "currencyID": "USD"}]}, {"ChargeIndicator": [{"_": true}], "AllowanceChargeReason": [{"_": "Sample Description"}], "MultiplierFactorNumeric": [{"_": 0.1}], "Amount": [{"_": 100, "currencyID": "USD"}]}], "TaxTotal": [{"TaxAmount": [{"_": 1460.5, "currencyID": "MYR"}], "TaxSubtotal": [{"TaxableAmount": [{"_": 1460.5, "currencyID": "USD"}], "TaxAmount": [{"_": 1460.5, "currencyID": "MYR"}], "Percent": [{"_": 6}], "TaxCategory": [{"ID": [{"_": "E"}], "TaxExemptionReason": [{"_": "Exempt New Means of Transport"}], "TaxScheme": [{"ID": [{"_": "OTH", "schemeID": "UN/ECE 5153", "schemeAgencyID": "6"}]}]}]}]}], "Item": [{"CommodityClassification": [{"ItemClassificationCode": [{"_": "9800.00.0010", "listID": "PTC"}]}, {"ItemClassificationCode": [{"_": "003", "listID": "CLASS"}]}], "Description": [{"_": "Laptop Peripherals"}], "OriginCountry": [{"IdentificationCode": [{"_": "MYS"}]}]}], "Price": [{"PriceAmount": [{"_": 17, "currencyID": "USD"}]}], "ItemPriceExtension": [{"Amount": [{"_": 100, "currencyID": "USD"}]}]}]}]}