{% extends 'layout.html' %}

{% block head %}
<!-- Template Main CSS Files -->

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Template Main CSS Files -->
<link href="/assets/css/pages/outbound/outbound-table.css" rel="stylesheet">

<link href="/assets/css/pages/inbound/card.css" rel="stylesheet">
<link href="/assets/css/pages/inbound/notice.css" rel="stylesheet">

<link href="/assets/css/pages/inbound/validation-modal.css" rel="stylesheet">

<link href="/assets/css/components/buttons.css" rel="stylesheet">
<link href="/assets/css/components/session-details.css" rel="stylesheet">
<link href="/assets/css/components/bqe-auth.css" rel="stylesheet">


<link href="/assets/css/components/filters.css" rel="stylesheet">
<link href="/assets/css/pages/inbound/inbound-modal.css" rel="stylesheet">


<link href="/assets/css/pages/oubound/dialog.css" rel="stylesheet">
<link href="/assets/css/pages/oubound/badges.css" rel="stylesheet">
<link href="/assets/css/manual-submission-modals.css" rel="stylesheet">

<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>


<script src="/assets/js/shared/utils.js"></script>
<script src="/assets/js/bqe-auth.js"></script>
<script src="/assets/js/modules/bqe/outbound.js"></script>
<script src="/assets/js/modules/bqe/invoice-helper.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.6/lottie.min.js"></script>

<style>
    .outbound-loading-modal .modal-content {
        border-radius: 20px;
        border: none;
        box-shadow: 0 20px 40px rgba(34, 56, 158, 0.1);
        background: linear-gradient(180deg, #ffffff 0%, #f8faff 100%);
        overflow: hidden;
        max-height: 80vh;
        overflow-y: auto;
    }

    .modal-processing-header {
        background: linear-gradient(135deg, #283572, #153053);
        padding: 1.25rem;
        position: relative;
        overflow: hidden;
    }

    .processing-icon {
        position: relative;
        width: 50px;
        height: 50px;
        margin: 0 auto 1rem;
    }

    .processing-pulse {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        animation: pulse 2s ease-in-out infinite;
    }

    .processing-icon i {
        position: relative;
        font-size: 1.75rem;
        color: white;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .processing-title {
        margin-bottom: 1rem;
        text-align: center;
        color: white;
    }

    .processing-title h5 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .processing-title p {

        opacity: 0.8;
        margin: 0;
        font-size: 0.9rem;
    }

    .modern-loading-container {
        padding: 1.25rem;
        text-align: center;
    }

    .loading-rings {
        position: relative;
        width: 100px;
        height: 50px;
        margin: 0 auto 1.5rem;
    }

    .ring {
        position: absolute;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #22389E;
        animation: spin 1.5s linear infinite;
    }

    .ring-1 {
        width: 100%;
        height: 100%;
        border-width: 3px;
    }

    .ring-2 {
        width: 80%;
        height: 80%;
        top: 10%;
        left: 10%;
        border-width: 2px;
        animation-duration: 1.2s;
    }

    .ring-3 {
        width: 60%;
        height: 60%;
        top: 20%;
        left: 20%;
        border-width: 1px;
        animation-duration: 0.9s;
    }

    .loading-status {
        margin-bottom: 1.5rem;
    }

    .loading-text {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        font-size: 1.1rem;
        color: #22389E;
    }

    .loading-dots span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #22389E;
        margin: 0 2px;
        animation: dots 1.4s ease-in-out infinite;
    }

    .loading-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .loading-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }

    .progress-container {
        padding: 1.5rem 2rem;
        background: rgba(34, 56, 158, 0.02);
    }

    .progress-status {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .status-label {
        color: #22389E;
        font-weight: 500;
    }

    .status-percentage {
        font-weight: 600;
        color: #22389E;
    }

    .progress {
        height: 8px !important;
        border-radius: 4px;
        background: rgba(34, 56, 158, 0.1);
        overflow: hidden;
    }

    .progress-bar {
        background: linear-gradient(90deg, #22389E, #1B2D7E);
        box-shadow: 0 0 10px rgba(34, 56, 158, 0.3);
    }

    .modern-fact-container {
        padding: 1.5rem 2rem;
        background: rgba(34, 56, 158, 0.02);
        border-top: 1px solid rgba(34, 56, 158, 0.05);
    }

    .fact-content {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .fact-content i {
        color: #22389E;
        font-size: 1.2rem;
        margin-top: 0.2rem;
    }

    .fact-text {
        flex: 1;
    }

    .fact-label {
        display: block;
        color: #22389E;
        font-weight: 600;
        margin-bottom: 0.2rem;
    }

    .fact-message {
        margin: 0;
        color: #4A5568;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .error-content {
        text-align: center;
        padding: 1.5rem;
        background: rgba(220, 53, 69, 0.05);
        border-radius: 12px;
        margin: 0 2rem 2rem;
    }

    .error-icon {
        color: #dc3545;
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .error-message {
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .retry-button {
        background: #dc3545;
        color: white;
        border: none;
        padding: 0.5rem 1.5rem;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .retry-button:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    /* Data source toggle styles */
.btn-group .btn-check:checked + .btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-group .btn-check:not(:checked) + .btn {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

    @keyframes pulse {
        0% {
            transform: scale(0.95);
            opacity: 0.5;
        }

        50% {
            transform: scale(1.05);
            opacity: 0.2;
        }

        100% {
            transform: scale(0.95);
            opacity: 0.5;
        }
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    @keyframes dots {

        0%,
        100% {
            transform: scale(1);
            opacity: 1;
        }

        50% {
            transform: scale(0.5);
            opacity: 0.5;
        }
    }

    /* Document Stack Animation */
    .document-stack {
        position: relative;
        width: 50px;
        height: 65px;
        margin: 0 auto 0.5rem;
    }

    .document {
        position: absolute;
        width: 100%;
        height: 80%;
        background: white;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .doc1 {
        transform: translateY(-5px) rotate(-5deg);
    }

    .doc2 {
        transform: translateY(0px);
    }

    .doc3 {
        transform: translateY(5px) rotate(5deg);
    }

    .document-stack:hover .doc1 {
        transform: translateY(-10px) rotate(-8deg);
    }

    .document-stack:hover .doc3 {
        transform: translateY(10px) rotate(8deg);
    }


    /* Invoice Paper Animation */
    .invoice-processing-container {
        padding: 1rem 1.5rem;
        text-align: center;
    }

    .invoice-animation {
        position: relative;
        width: 300px;
        height: 120px;
        margin: 0 auto 0.5rem;
    }

    .invoice-paper {
        position: relative;
        width: 100%;
        height: 100%;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 20px;
        animation: float 3s ease-in-out infinite;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .invoice-line {
        height: 8px;
        background: #f0f2f5;
        margin-bottom: 12px;
        border-radius: 2px;
        animation: scan 2s ease-in-out infinite;
    }

    .invoice-line:nth-child(1) {
        width: 60%;
    }

    .invoice-line:nth-child(2) {
        width: 85%;
    }

    .invoice-line:nth-child(3) {
        width: 70%;
    }

    .invoice-details {
        display: flex;
        justify-content: space-between;

    }

    .invoice-details-left,
    .invoice-details-right {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .invoice-details-line {
        height: 6px;
        background: #f0f2f5;
        width: 120px;
        border-radius: 2px;
    }



    .invoice-stamp {
        position: absolute;
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        border: 2px solid #22389E;
        border-radius: 50%;
        opacity: 0;
        animation: stamp 3s ease-in-out infinite;
    }

    /* Processing Steps */
    .processing-steps {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
        color: #22389E;
        opacity: 0.5;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .step-item.active {
        opacity: 1;
        transform: scale(1.1);
    }

    .step-arrow {
        color: #22389E;
        opacity: 0.3;
    }

    /* Progress Section */
    .progress-section {
        padding: 1rem 1.5rem;
        background: rgba(34, 56, 158, 0.02);
    }

    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .document-count {
        font-size: 0.9rem;
        color: #22389E;
    }

    /* Animations */
    @keyframes float {

        0%,
        100% {
            transform: translateY(0);
        }

        50% {
            transform: translateY(-10px);
        }
    }

    @keyframes scan {
        0% {
            transform: translateX(-100%);
            opacity: 0;
        }

        50% {
            transform: translateX(0);
            opacity: 1;
        }

        100% {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    @keyframes stamp {

        0%,
        100% {
            opacity: 0;
            transform: scale(0.8) rotate(-10deg);
        }

        50% {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }

    @keyframes spin {
        to {
            transform: translate(-50%, -50%) rotate(360deg);
        }
    }

    .spin {
        animation: spin 1s linear infinite;
    }

    /* Processing Status */
    .processing-status {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin: 0.75rem 0;
        padding: 0.75rem;
        background: rgba(34, 56, 158, 0.05);
        border-radius: 8px;
    }

    .status-icon {
        color: #22389E;
    }

    .status-text {
        color: #22389E;
        font-weight: 500;
    }

    /* Info Box */
    .processing-info {
        padding: 1rem 1.5rem;
        background: rgba(34, 56, 158, 0.02);
        border-top: 1px solid rgba(34, 56, 158, 0.05);
    }

    .info-box {
        display: flex;
        gap: 0.75rem;
        padding: 0.75rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(34, 56, 158, 0.05);
    }

    .info-icon {
        color: #22389E;
        font-size: 1.2rem;
    }

    .info-label {
        display: block;
        color: #22389E;
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 0.2rem;
    }

    .info-message {
        margin: 0;
        color: #4A5568;
        font-size: 0.9rem;
        line-height: 1.4;
    }


    #cooldownModal .progress {
        height: 10px;
        margin: 10px 0;
    }

    #cooldownModal .spinner-border {
        width: 3rem;
        height: 3rem;
    }

    #cooldownTimer {
        font-size: 0.9rem;
        color: #666;
    }

    /* Tutorial Modal Styles */
    .tutorial-content {
        position: relative;
    }

    .tutorial-progress {
        padding: 15px 15px 5px;
        background-color: #f8f9fa;
    }

    .step-indicators {
        margin-top: 10px;
    }

    .step-dot {
        width: 12px;
        height: 12px;
        background-color: #dee2e6;
        border-radius: 50%;
        display: inline-block;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .step-dot.active {
        background-color: #0d6efd;
        transform: scale(1.2);
    }

    .tutorial-step {
        display: none;
    }

    .tutorial-step.active {
        display: block;
    }


    .step-image img {
        max-height: 200px;
        object-fit: contain;
        border: 1px solid #dee2e6;
    }

    /* Highlight elements during tutorial */
    .tutorial-highlight {
        position: relative;
        z-index: 1060;
        box-shadow: 0 0 0 4px rgba(13, 110, 253, 0.5), 0 0 15px rgba(0, 0, 0, 0.3) !important;
        border-radius: 4px;
        transition: box-shadow 0.3s ease;
    }

    /* Tutorial tip styles */
    .tutorial-tip {
        background-color: #e7f5ff;
        border-left: 4px solid #0d6efd;
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }

    .tutorial-note {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }

    /* Modal header styling */
    #tutorialModal .modal-header {
        background: linear-gradient(135deg, #0d6efd, #0a58ca);
    }

    /* Progress bar styling */
    #tutorialModal .progress {
        height: 8px;
        border-radius: 4px;
        background-color: #e9ecef;
    }

    #tutorialModal .progress-bar {
        background: linear-gradient(90deg, #0d6efd, #0a58ca);
        transition: width 0.5s ease;
    }

    /* Guided Tour Styles */
    .guided-tour-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2) !important;
        backdrop-filter: blur(1px);
        z-index: 1050;
        display: none;
    }

    /* Prevent scrolling during tutorial */
    body.tour-active {
        overflow: hidden !important;
        position: fixed;
        width: 100%;
        height: 100%;
    }

    .guided-tour-tooltip {
        position: fixed;
        width: min(360px, 90vw);
        background-color: #fff;
        border-radius: 16px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        z-index: 1060;
        display: none;
        overflow: hidden;
        transition: all 0.3s ease;
        --arrow-position: 50%;
        max-height: min(600px, 90vh);
        display: flex;
        flex-direction: column;
    }

    /* Header styling */
    .tooltip-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        position: sticky;
        top: 0;
        z-index: 3;
        flex-shrink: 0;
    }

    .tooltip-step-indicator {
        font-size: 14px;
        font-weight: 600;
        color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
        padding: 4px 12px;
        border-radius: 20px;
    }

    .tooltip-close-btn {
        background: none;
        border: none;
        color: #6c757d;
        font-size: 20px;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        transition: all 0.2s;
    }

    .tooltip-close-btn:hover {
        background-color: rgba(108, 117, 125, 0.1);
        color: #495057;
    }

    /* Body styling */
    .tooltip-body {
        padding: 20px;
        overflow-y: auto;
        flex-grow: 1;
        min-height: 100px;
        max-height: calc(90vh - 180px);
    }

    .tooltip-title {
        font-size: clamp(16px, 4vw, 18px);
        font-weight: 600;
        color: #212529;
        margin-bottom: 16px;
    }

    .tooltip-content {
        font-size: clamp(13px, 3.5vw, 14px);
        line-height: 1.6;
        color: #495057;
    }

    .tooltip-content p {
        margin-bottom: 12px;
    }

    .tooltip-content ul,
    .tooltip-content ol {
        padding-left: 20px;
        margin-bottom: 12px;
    }

    .tooltip-content li {
        margin-bottom: 6px;
    }

    /* Progress styling */
    .tooltip-progress {
        padding: 12px 20px;
        background: #fff;
        border-top: 1px solid #eee;
        position: sticky;
        bottom: 56px;
        z-index: 3;
        flex-shrink: 0;
    }

    .tooltip-progress .progress {
        height: 4px;
        border-radius: 2px;
        background-color: #e9ecef;
        margin-bottom: 12px;
        overflow: hidden;
    }

    .tooltip-progress .progress-bar {
        background: linear-gradient(90deg, #0d6efd, #6610f2);
        transition: width 0.5s ease;
    }

    .step-dot {
        width: 8px;
        height: 8px;
        background-color: #dee2e6;
        border-radius: 50%;
        display: inline-block;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .step-dot.active {
        background-color: #0d6efd;
        transform: scale(1.3);
    }

    /* Footer styling */
    .tooltip-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
        position: sticky;
        bottom: 0;
        z-index: 3;
        flex-shrink: 0;
        height: 56px;
    }

    .tooltip-btn {
        border: none;
        background: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: clamp(12px, 3.5vw, 14px);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
        min-width: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
    }

    .tooltip-btn-prev {
        color: #6c757d;
        background-color: #f8f9fa;
    }

    .tooltip-btn-prev:hover:not(:disabled) {
        background-color: #e9ecef;
    }

    .tooltip-btn-prev:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .tooltip-btn-skip {
        color: #dc3545;
        background-color: #fff;
        border: 1px solid #dc3545;
    }

    .tooltip-btn-skip:hover {
        background-color: #dc3545;
        color: #fff;
    }

    .tooltip-btn-next {
        color: #fff;
        background-color: #0d6efd;
        border-radius: 8px;
        padding: 8px 20px;
        min-width: 80px;
    }

    .tooltip-btn-next:hover {
        background-color: #0b5ed7;
    }

    /* Highlight elements during tutorial */
    .tour-highlight {
        position: relative !important;
        z-index: 1055 !important;
        background-color: #ffffff !important;
        box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.4),
            0 0 15px 5px rgba(13, 110, 253, 0.2) !important;
        border-radius: 4px !important;
        animation: glow-highlight 2s infinite !important;
        isolation: isolate;
    }

    /* Row highlighting for table rows */
    .tour-highlight-row {
        position: relative !important;
        z-index: 1054 !important;
        background-color: rgba(13, 110, 253, 0.04) !important;
    }

    /* Special handling for table cells */
    .outbound-table td.tour-highlight,
    .outbound-table th.tour-highlight {
        position: relative !important;
        z-index: 1056 !important;
        background-color: rgba(255, 255, 255, 0.98) !important;
        animation: glow-highlight 2s infinite;
    }

    /* Special handling for action buttons */
    .tour-highlight .outbound-action-btn {
        position: relative !important;
        z-index: 1057 !important;
        animation: glow-highlight 2s infinite;
    }

    /* Special handling for checkboxes */
    .tour-highlight .outbound-checkbox {
        position: relative !important;
        z-index: 1057 !important;
        animation: glow-highlight 2s infinite;
    }

    /* Enhanced UI Styles */
    .statistics-section .card,
    .quick-actions-section .card,
    .enhanced-filter-section .card,
    .document-preview-section .card {
        border: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .statistics-section .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .quick-actions-section .btn {
        transition: all 0.2s ease;
        border: none;
        padding: 0.5rem 1rem;
    }

    .quick-actions-section .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .enhanced-filter-section .form-floating>.form-control,
    .enhanced-filter-section .form-floating>.form-select {
        border-radius: 6px;
        border: 1px solid #dee2e6;
        padding: 1rem 0.75rem;
    }

    .enhanced-filter-section .form-floating>label {
        padding: 1rem 0.75rem;
    }

    .document-preview-section .document-info {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 6px;
    }

    .document-preview-section .info-item {
        padding: 0.5rem 0;
        border-bottom: 1px solid #dee2e6;
    }

    .document-preview-section .info-item:last-child {
        border-bottom: none;
    }

    .document-preview-frame {
        min-height: 300px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        overflow: hidden;
    }

    /* Animation for refresh icon */
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    /* Chart container styles */
    .statistics-section canvas {
        max-height: 200px;
    }

    /* Card title styles */
    .card-title {
        color: #495057;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Quick action button styles */
    .quick-actions-section .btn i {
        font-size: 1rem;
    }

    /* Enhanced filter styles */
    .enhanced-filter-section .btn-link {
        color: #6c757d;
        text-decoration: none;
    }

    .enhanced-filter-section .btn-link:hover {
        color: #495057;
    }

    /* Document preview styles */
    .document-preview-section .badge {
        padding: 0.5em 0.75em;
        font-weight: 500;
    }

    .document-preview-section .fw-medium {
        font-weight: 500;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .statistics-section .card {
            margin-bottom: 1rem;
        }

        .quick-actions-section .btn {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .enhanced-filter-section .form-floating {
            margin-bottom: 1rem;
        }
    }

    /* Filter Styles */
    .filter-container {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .search-wrapper {
        position: relative;
    }

    .search-wrapper .form-control {
        padding-left: 2.5rem;
        height: 48px;
        border-radius: 24px;
        border: 2px solid #e9ecef;
        transition: all 0.2s ease;
    }

    .search-wrapper .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, .15);
    }


    .quick-filters .btn.active {
        background-color: #0a3d8a;
        color: white;
        box-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
    }

    /* Filter Tags */
    .active-filter-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 8px 0;
    }

    .filter-tag {
        display: inline-flex;
        align-items: center;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 16px;
        padding: 4px 12px;
        font-size: 0.875rem;
        color: #495057;
        transition: all 0.2s ease;
    }

    .filter-tag:hover {
        background-color: #e9ecef;
    }

    .filter-tag .close-btn {
        background: none;
        border: none;
        color: #6c757d;
        font-size: 18px;
        line-height: 1;
        padding: 0 0 0 8px;
        cursor: pointer;
        transition: color 0.2s ease;
    }

    .filter-tag .close-btn:hover {
        color: #dc3545;
    }

    /* Advanced Filters */
    .advanced-filters-content {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .advanced-filters-content .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .advanced-filters-content .form-control,
    .advanced-filters-content .form-select {
        border: 2px solid #e9ecef;
        border-radius: 6px;
        padding: 0.5rem 1rem;
    }

    .advanced-filters-content .input-group-text {
        border: 2px solid #e9ecef;
        background-color: #f8f9fa;
    }

    /* Loading State */
    .filter-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        display: none;
    }

    .filter-loading.active {
        display: flex;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .quick-filters {
            flex-wrap: wrap;
        }

        .quick-filters .btn {
            flex: 1 1 auto;
            min-width: calc(33.333% - 8px);
            margin-bottom: 8px;
        }

        .quick-filters .ms-auto {
            width: 100%;
            margin-top: 0.5rem;
            display: flex;
            gap: 0.5rem;
        }
    }

    .toggle-icon {
        transition: transform 0.3s ease;
    }

    [aria-expanded="false"] .toggle-icon {
        transform: rotate(180deg);
    }

    #consolidationDetails {
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover .toggle-icon {
        color: #6c757d;
    }

    .fs-7 {
        font-size: 0.875rem;
    }

    .filter-note .card {
        box-shadow: none;
        background-color: rgba(255, 255, 255, 0.5) !important;
    }

    .filter-note .list-unstyled li {
        margin-bottom: 0.25rem;
    }

    .filter-note .list-unstyled li:last-child {
        margin-bottom: 0;
    }

    .filter-note .btn-link {
        text-decoration: none;
    }

    .filter-note .btn-link:hover {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
    }

    .filter-note .alert-light {
        background-color: rgba(255, 255, 255, 0.8);
    }

    .consolidation-section {
        background: #fff;
        border-radius: 12px;
        padding: 1.25rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    }

    .info-banner {
        padding: 0.75rem;
        background: rgba(13, 110, 253, 0.04);
        border-radius: 8px;
    }

    .content-card {
        background: #f8f9fa;
        border-radius: 10px;
        overflow: hidden;
        transition: transform 0.2s;
    }

    .content-card:hover {
        transform: translateY(-2px);
    }

    .card-header {
        padding: 0.75rem 1rem;
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-content {
        padding: 1rem;
    }

    .detail-item,
    .format-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border-radius: 6px;
        margin-bottom: 0.5rem;
        background: #fff;
    }

    .detail-item:last-child,
    .format-item:last-child {
        margin-bottom: 0;
    }

    .detail-item .label {
        color: #6c757d;
        min-width: 100px;
        font-size: 0.875rem;
    }

    .detail-item .value {
        font-family: 'Roboto Mono', monospace;
        font-size: 0.875rem;
    }

    .format-item {
        font-size: 0.875rem;
    }

    .format-item i {
        font-size: 1rem;
    }

    .badge.rounded-pill {
        padding: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-link:hover {
        opacity: 0.75;
    }

    @media (max-width: 768px) {
        .consolidation-section {
            padding: 1rem;
        }

        .detail-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .detail-item .label {
            min-width: auto;
        }
    }

    .middleware-disclaimer {
        display: flex;
        gap: 1rem;
        padding: 1rem;
        /* background: linear-gradient(to right, rgba(13, 110, 253, 0.03), rgba(13, 110, 253, 0.01)); */
        border-radius: 8px;
        border: 1px solid rgba(13, 110, 253, 0.1);
        position: relative;
    }

    .disclaimer-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        flex-shrink: 0;
    }

    .disclaimer-icon i {
        font-size: 1.25rem;
        color: #0d6efd;
    }

    .disclaimer-content {
        flex: 1;
    }

    .disclaimer-content h6 {
        color: #0d6efd;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .disclaimer-content ul {
        margin-top: 0.25rem;
    }

    .disclaimer-content ul li {
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .disclaimer-note {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: rgba(255, 193, 7, 0.1);
        border-radius: 4px;
        color: #856404;
        font-size: 0.75rem;
    }

    .disclaimer-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: rgba(255, 193, 7, 0.1);
        border-radius: 4px;
        color: #084298;
        font-size: 0.75rem;
    }

    .disclaimer-info i {
        font-size: 0.875rem;
    }

    .disclaimer-note i {
        font-size: 0.875rem;
    }

    @media (max-width: 768px) {
        .middleware-disclaimer {
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 1rem;
        }

        .disclaimer-content ul {
            text-align: left;
        }

        .disclaimer-note {
            text-align: left;
        }
    }

    /* Excel Upload Section Styles */
    .excel-upload-section {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 1rem;
    }

    .upload-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e9ecef;
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;
    }

    .upload-title {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #495057;
    }

    .upload-info-badge {
        background: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .upload-body {
        padding: 1.5rem;
    }

    .drop-zone {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .drop-zone:hover {
        border-color: #0d6efd;
        background: rgba(13, 110, 253, 0.02);
    }

    .drop-zone.dragover {
        border-color: #0d6efd;
        background: rgba(13, 110, 253, 0.05);
        transform: scale(1.02);
    }

    .drop-zone-content {
        pointer-events: none;
    }

    .drop-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .drop-zone h5 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .supported-formats {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .file-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }

    .file-details {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .file-details i {
        font-size: 2rem;
        color: #198754;
    }

    .file-text {
        flex: 1;
    }

    .file-name {
        font-weight: 600;
        color: #495057;
    }

    .file-size {
        font-size: 0.875rem;
    }

    .document-type-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .upload-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .upload-progress {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .progress-text {
        font-weight: 500;
        color: #495057;
    }

    .progress-percentage {
        font-weight: 600;
        color: #0d6efd;
    }

    .progress {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
    }

    .progress-bar {
        background: linear-gradient(90deg, #0d6efd, #0a58ca);
        transition: width 0.3s ease;
    }

    @media (max-width: 768px) {
        .upload-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .upload-actions .btn {
            width: 100%;
        }

        .drop-zone {
            padding: 1.5rem 1rem;
        }

        .drop-icon {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}

<div class="container-fluid px-2 px-md-2 px-lg-2">
    <!-- Welcome Card - Full width -->
    <div class="profile-welcome-section mb-4">
        <div class="profile-welcome-card">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="welcome-icon">
                        <i class="bi bi-box-arrow-right"></i>
                    </div>
                    <div class="welcome-content">
                        <h3 class="mb-1">Outbound</h3>
                        <p class="mb-0 cursor-pointer" >
                            View and manage your outbound invoices. To fetch invoices from BQE, please authorize the
                            connection.
                        </p>
                    </div>
                </div>
                <div class="d-flex align-items-start">
                    <!-- Add BQE Auth Button -->
                    <div class="me-4 mt-4 mb-2 position-relative">
                        <button id="bqeAuthBtn" class="btn auth-btn" type="button">
                            <i class="bi bi-shield-lock me-2"></i>
                            <span id="bqeAuthBtnText">Authorize BQE</span>
                        </button>
                        <div class="connection-status not-connected mt-2 text-center">
                            <i class="bi bi-x-circle"></i>
                            <span>Not Connected</span>
                        </div>
                        <div class="mt-2 text-center">
                            <small class="text-white-50">Click to establish BQE connection</small>
                        </div>
                    </div>
                    <!-- Existing datetime section -->
                    <div class="welcome-datetime text-end">
                        <div class="current-time">
                            <i class="bi bi-clock"></i>
                            <span id="currentTime">00:00:00 AM</span>
                        </div>
                        <div class="current-date">
                            <i class="bi bi-calendar3"></i>
                            <span id="currentDate">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Content Section - With margins -->
    <div class="content-section">
        <!-- Cards Section -->
        <div class="cards-container">
            <!-- Invoices Card -->
            <div class="info-card invoices-card">
                <div class="card-info">
                    <div class="card-icon">
                        <i class="bi bi-file-earmark-text"></i>
                    </div>
                    <div class="count-info">
                        <h6>0</h6>
                        <span class="text-muted">Total</span>
                    </div>
                </div>
                <span class="card-title-new">INVOICES</span>
            </div>

            <!-- Submitted Card -->
            <div class="info-card submitted-card">
                <div class="card-info">
                    <div class="card-icon">
                        <i class="bi bi-check2-circle"></i>
                    </div>
                    <div class="count-info">
                        <h6>0</h6>
                        <span class="text-muted">Total</span>
                    </div>
                </div>
                <span class="card-title-new">SUBMITTED</span>
            </div>

            <!-- Pending Card -->
            <div class="info-card pending-card">
                <div class="card-info">
                    <div class="card-icon">
                        <i class="bi bi-hourglass-split"></i>
                    </div>
                    <div class="count-info">
                        <h6>0</h6>
                        <span class="text-muted">Total</span>
                    </div>
                </div>
                <span class="card-title-new">PENDING</span>
            </div>

            <!-- Cancelled Card -->
            <div class="info-card cancelled-card">
                <div class="card-info">
                    <div class="card-icon">
                        <i class="bi bi-x-circle"></i>
                    </div>
                    <div class="count-info">
                        <h6>0</h6>
                        <span class="text-muted">Total</span>
                    </div>
                </div>
                <span class="card-title-new">CANCELLED</span>
            </div>
        </div>



        <!-- Table Section -->
        <div class="table-container-wrapper">
            <div class="table-card">
                <div class="table-header">
                    <div class="table-header-content">
                        <h2 class="table-title">Outbound Document List</h2>
                        <div class="table-actions">
                            <!-- Data Source Toggle -->
                            <div class="btn-group me-2" role="group" aria-label="Data source toggle">
                                <input type="radio" class="btn-check" name="dataSource" id="bqe" autocomplete="off" checked>
                                <label class="btn outbound-action-btn cancel btn-sm" for="bqe">
                                    <i class="bi bi-archive me-1"></i>BQE Submission Lists
                                </label>

                                <input type="radio" class="btn-check" name="dataSource" id="manual" autocomplete="off">
                                <label class="btn outbound-action-btn cancel btn-sm" for="manual">
                                    <i class="bi bi-archive me-1"></i>Manual Submission Lists
                                </label>
                            </div>

                            <!-- Refresh Button -->
                            <button id="refreshDataSource" class="btn outbound-action-btn submit btn-sm">
                                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                            </button>
                        </div>
                    </div>


                </div>
                <div class="table-body-container">
                    <div class="responsive-table-container">
                        <div class="table-section">
                            <div class="outbound-table-container">
                                <!-- Advanced Filters (Collapsible) -->
                                <div class="collapse" id="advancedFilters" >
                                    <div class="advanced-filters-content border-top pt-3">
                                        <div class="row g-3">
                                            <!-- Date Range -->
                                            <div class="col-md-6">
                                                <label class="form-label d-flex align-items-center">
                                                    <i class="bi bi-calendar-range me-2"></i>Date Range
                                                </label>
                                                <div class="input-group">
                                                    <input type="date" class="form-control" id="tableStartDate">
                                                    <span class="input-group-text bg-light">to</span>
                                                    <input type="date" class="form-control" id="tableEndDate">
                                                </div>
                                            </div>

                                            <!-- Amount Range -->
                                            <div class="col-md-6">
                                                <label class="form-label d-flex align-items-center">
                                                    <i class="bi bi-currency-dollar me-2"></i>Amount Range
                                                </label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="minAmount"
                                                        placeholder="Min">
                                                    <span class="input-group-text bg-light">to</span>
                                                    <input type="number" class="form-control" id="maxAmount"
                                                        placeholder="Max">
                                                </div>
                                            </div>

                                            <!-- Company Filter -->
                                            <div class="col-md-4">
                                                <label class="form-label d-flex align-items-center">
                                                    <i class="bi bi-building me-2"></i>Company
                                                </label>
                                                <input type="text" class="form-control" id="companyFilter"
                                                    placeholder="Filter by company name">
                                            </div>

                                            <!-- Document Type -->
                                            <div class="col-md-4">
                                                <label class="form-label d-flex align-items-center">
                                                    <i class="bi bi-file-text me-2"></i>Document Type
                                                </label>
                                                <select class="form-select" id="documentTypeFilter">
                                                    <option value="">All Types</option>
                                                    <option value="Invoice">Invoice</option>
                                                    <option value="Credit Note">Credit Note</option>
                                                    <option value="Debit Note">Debit Note</option>
                                                </select>
                                            </div>

                                            <!-- Source -->
                                            <div class="col-md-4">
                                                <label class="form-label d-flex align-items-center">
                                                    <i class="bi bi-diagram-2 me-2"></i>Source
                                                    <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip"
                                                        data-bs-html="true" title="<div class='tooltip-content'>
                                                                            <div class='tooltip-row'>
                                                                                <i class='bi bi-info-circle'></i>
                                                                                <span><b>LHDN Validation:</b></span>
                                                                            </div>
                                                                            <div class='tooltip-row'>
                                                                                <i class='bi bi-check-circle text-success'></i>
                                                                                <span>Valid: Document passed validation</span>
                                                                            </div>
                                                                            <div class='tooltip-row'>
                                                                                <i class='bi bi-x-circle text-danger'></i>
                                                                                <span>Invalid: Failed validation checks</span>
                                                                            </div>
                                                                            <div class='tooltip-row'>
                                                                                <i class='bi bi-clock-history text-warning'></i>
                                                                                <span>Pending: Awaiting for Submission to LHDN for validation</span>
                                                                            </div>
                                                                        </div>"></i>
                                                </label>
                                                <select class="form-select" id="sourceFilter">
                                                    <option value="">LHDN</option>

                                                </select>
                                                <small class="text-muted mt-1 d-block">
                                                    <i class="bi bi-info-circle-fill me-1"></i>
                                                    LHDN responses include validation status and timestamps
                                                </small>
                                            </div>
                                        </div>


                                        <!-- Filter Note -->
                                        <div class="filter-note alert alert-info mt-3" role="alert">
                                            <i class="bi bi-info-circle me-2"></i>
                                            <small>
                                                Use the filters above to narrow down your results. Active filters will
                                                appear below as tags that you can easily remove. Click the 'x' on any
                                                tag to remove that filter, or use 'Clear all filters' to reset.
                                            </small>
                                        </div>

                                        <!-- Active Filters Display -->
                                        <div class="active-filters mt-3 pt-3 border-top">
                                            <div class="d-flex align-items-center">
                                                <small class="text-muted me-2">Active Filters:</small>
                                                <div class="active-filter-tags" id="activeFilterTags">
                                                    <!-- Active filter tags will be added here dynamically -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Filters Section -->
                                <div class="filters-section d-none">
                                    <div class="filters-header">
                                        <div class="filter-title">
                                            <i class="bi bi-funnel me-2"></i>
                                            Filters
                                            <span class="api-limit-badge ms-2" data-bs-toggle="tooltip"
                                                title="BQE API has a limitation of 25 pages per request. Each page contains up to 100 records.">
                                                <i class="bi bi-info-circle"></i>
                                                Max 25 pages
                                            </span>
                                        </div>
                                    </div>


                                    <div class="filters-body">
                                        <!-- Main Filters -->
                                        <div class="main-filters">
                                            <div class="filter-group">
                                                <label>
                                                    Period
                                                    <i class="bi bi-question-circle" data-bs-toggle="tooltip"
                                                        title="Select a predefined time period or choose Custom for manual date range"></i>
                                                </label>
                                                <select class="form-select">
                                                    <optgroup label="Custom">
                                                        <option value="custom">Custom Range</option>
                                                    </optgroup>
                                                    <optgroup label="Daily">
                                                        <option value="today">Today</option>
                                                        <option value="yesterday">Yesterday</option>
                                                    </optgroup>
                                                    <optgroup label="Weekly">
                                                        <option value="this week">This Week</option>
                                                        <option value="last week">Last Week</option>
                                                    </optgroup>
                                                    <optgroup label="Monthly">
                                                        <option value="this month">This Month</option>
                                                        <option value="last month">Last Month</option>
                                                    </optgroup>
                                                    <optgroup label="Yearly">
                                                        <option value="this year">This Year</option>
                                                        <option value="last year">Last Year</option>
                                                    </optgroup>
                                                </select>
                                            </div>

                                            <div class="date-range">
                                                <div class="filter-group">
                                                    <label>
                                                        From
                                                        <i class="bi bi-question-circle" data-bs-toggle="tooltip"
                                                            title="Select start date for custom date range"></i>
                                                    </label>
                                                    <input type="date" class="form-control" id="fromDate">
                                                </div>
                                                <div class="filter-group">
                                                    <label>
                                                        To
                                                        <i class="bi bi-question-circle" data-bs-toggle="tooltip"
                                                            title="Select end date for custom date range"></i>
                                                    </label>
                                                    <input type="date" class="form-control" id="toDate">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Additional Filters -->
                                        <div class="additional-filters">


                                        </div>
                                        <!-- Middleware Disclaimer -->

                                    </div>

                                    <div class="filters-footer">
                                        <div class="records-info">
                                            <span class="records-count me-2">
                                                Total Invoices Fetched: 0
                                                <i class="bi bi-question-circle ms-1" data-bs-toggle="tooltip"
                                                    title="Total number of invoices found for selected date range"></i>
                                            </span>
                                            <span class="api-notice">
                                                <i class="bi bi-exclamation-triangle"></i>
                                                Limited to 2,500 records (25 pages × 100 records)
                                            </span>
                                        </div>
                                        <button class="outbound-action-btn submit" type="button" id="searchBqeBtn">
                                            <i class="bi bi-search me-1"></i>
                                            Search BQE Invoice
                                        </button>
                                    </div>
                                </div>
                                <!-- Primary Filters Row -->
                                <div class="row g-3 mb-3">
                                    <!-- Global Search -->
                                    <div class="col-md-4">
                                        <div class="search-wrapper position-relative">
                                            <i
                                                class="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                                            <input type="text" class="form-control form-control-lg ps-5"
                                                id="globalSearch" placeholder="Search documents...">
                                        </div>
                                    </div>

                                    <!-- Quick Filters -->
                                    <div class="col-md-8">
                                        <div class="quick-filters d-flex gap-2">
                                            <button class="btn outbound-action-btn submit active" data-filter="all">
                                                <i class="bi bi-grid-3x3-gap-fill me-1"></i>All
                                            </button>
                                            <button class="btn outbound-action-btn submit" data-filter="submitted">
                                                <i class="bi bi-check-circle me-1"></i>Submitted
                                            </button>
                                            <button class="btn outbound-action-btn submit" data-filter="pending">
                                                <i class="bi bi-hourglass-split me-1"></i>Pending
                                            </button>
                                            <button class="btn outbound-action-btn submit" data-filter="cancelled">
                                                <i class="bi bi-x-circle me-1"></i>Cancelled
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <table id="reportsTable" class="outbound-table-responsive">
                                    <thead class="outbound-table-header">
                                        <tr>
                                            <th class="md-4">#</th>
                                            <th>INVOICE NO.</th>
                                            <th>TYPE</th>
                                            <th>SUPPLIER NAME</th>
                                            <th>BUYER NAME</th>
                                            <th>FILE PATH</th>
                                            <th>UPLOADED DATE/TIME</th>
                                            <th>DATE SUBMITTED</th>
                                            <th>CANCELLATION TIME LEFT</th>
                                            <th>STATUS</th>
                                            <th>AMOUNT</th>
                                            <th>ACTION</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Overlay -->
                    <div id="tableLoadingOverlay" class="table-loading-overlay d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Fetching documents...</p>
                    </div>
                </div>



                <div class="table-footer">
                    <div class="padding: 10px">
                        <div class="disclaimer-note">
                            <i class="bi bi-exclamation-circle"></i>
                            <span>For a smooth submission process, verify all required fields are complete and accurate
                                on your ERP System Before Submitting to LHDN. </br>
                                <span>Pinnacle e-Invoice Portal performs initial validations for Mandatory fields before
                                    proceeding to LHDN submission</span>
                                <a href="/help" class="ms-1 text-decoration-none" target="_blank">
                                    <i class="bi bi-question-circle" data-bs-toggle="tooltip"
                                        title="Click for help documentation"></i>
                                </a>
                        </div>
                        <div class="disclaimer-note mt-2" style="background: rgba(220, 53, 69, 0.1); color: #842029;">
                            <i class="bi bi-exclamation-triangle"></i>
                            <span><strong>Important Disclaimer:</strong> <br>
                                Our platform provides validation services, but the ultimate responsibility for data
                                accuracy lies with the user.
                                <br>
                                We are not liable for any consequences, penalties, or issues arising from submission of
                                incomplete, inaccurate or non-compliant data to LHDN.</span>
                        </div>
                        <!-- Consolidation Guidelines -->
                        <div class="mt-3 mb-4">
                            <div class="middleware-disclaimer mb-3">
                                <div class="disclaimer-icon">
                                    <i class="bi bi-info-circle-fill text-primary"></i>
                                </div>
                                <div class="disclaimer-content">
                                    <h6 class="mb-0 text-primary fw-bold">Important Information About Consolidation</h6>
                                    <p class="mb-2">This section outlines critical requirements for LHDN e-Invoice
                                        consolidation. Please review carefully before proceeding.</p>
                                </div>
                            </div>

                            <div class="d-flex align-items-center justify-content-between mb-3 clickable-header"
                                style="cursor: pointer; transition: all 0.2s ease; background: #f8f9fa; padding: 1rem; border-radius: 8px; border: 1px solid #e9ecef;">
                                <div class="d-flex align-items-center">
                                    <span class="badge rounded-pill bg-primary me-2" style="padding: 8px;"
                                        data-bs-toggle="tooltip"
                                        title="Consolidation allows grouping multiple invoices into a single submission">
                                        <i class="bi bi-boxes"></i>
                                    </span>
                                    <h6 class="mb-0 text-primary fw-bold">LHDN e-Invoice Consolidation Requirements <i
                                            class="bi bi-info-circle-fill ms-1 small" data-bs-toggle="tooltip"
                                            data-bs-html="true" title="<div class='p-2'>
                                                    <strong>Before Submitting a Consolidation:</strong><br>
                                                    • Must Group multiple invoices together<br>
                                                    • Must meet LHDN requirements<br>
                                                    • Streamlines submission process</div>">
                                        </i>
                                    </h6>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <a href="https://www.hasil.gov.my/media/uwwehxwq/irbm-e-invoice-specific-guideline.pdf"
                                        target="_blank"
                                        class="btn btn-sm btn-link text-primary p-0 d-flex align-items-center hover-effect"
                                        data-bs-toggle="tooltip" data-bs-html="true" title="<div class='p-2'>
                                                       <strong>LHDN Official Guidelines:</strong><br>
                                                       • Review consolidation requirements<br>
                                                       • Ensure compliance with LHDN standards<br>
                                                       • Understand submission protocols
                                                     </div>">
                                        <i class="bi bi-file-pdf fs-5"></i>
                                        <span class="ms-1 small">LHDN Guidelines</span>
                                    </a>
                                    <button
                                        class="btn btn-sm btn-link text-primary p-0 d-flex align-items-center toggle-btn"
                                        type="button" data-bs-toggle="collapse" data-bs-target="#consolidationDetails"
                                        aria-expanded="false" aria-controls="consolidationDetails"
                                        data-bs-toggle="tooltip" data-bs-html="true" title="<div class='p-2'>
                                                            <strong>Consolidation Requirements:</strong><br>
                                                            • View detailed formatting guidelines<br>
                                                            • Check buyer information requirements<br>
                                                            • Review receipt number formatting
                                                          </div>">
                                        <i class="bi bi-chevron-down toggle-icon fs-5"></i>

                                    </button>
                                </div>
                            </div>

                            <div class="collapse" id="consolidationDetails">
                                <div class="consolidation-content">
                                    <div class="row g-3">
                                        <!-- Buyer's Details -->
                                        <div class="col-md-6">
                                            <div class="content-card h-100"
                                                style="border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                                <div class="card-header bg-light">
                                                    <i class="bi bi-person-badge text-primary"></i>
                                                    <span class="fw-bold">Buyer's Details</span>
                                                </div>
                                                <div class="card-content">
                                                    <div class="detail-item">
                                                        <span class="label">Name</span>
                                                        <span class="value">"General Public"</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="label">TIN</span>
                                                        <span class="value">"EI00000000010"</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="label">Registration/ID</span>
                                                        <span class="value">"NA"</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="label">Other Details</span>
                                                        <span class="value text-muted">Address/Contact/SST: "NA"</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Description Format -->
                                        <div class="col-md-6">
                                            <div class="content-card h-100"
                                                style="border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                                <div class="card-header bg-light">
                                                    <i class="bi bi-list-check text-primary"></i>
                                                    <span class="fw-bold">Description Format</span>
                                                </div>
                                                <div class="card-content">
                                                    <div class="format-item">
                                                        <i class="bi bi-check2 text-success"></i>
                                                        <span>Summary per receipt as line items (Use Classification
                                                            Code: 004)</span>
                                                    </div>
                                                    <div class="format-item">
                                                        <i class="bi bi-check2 text-success"></i>
                                                        <span>Continuous receipt number chain</span>
                                                    </div>
                                                    <div class="format-item">
                                                        <i class="bi bi-check2 text-success"></i>
                                                        <span>Branch consolidated submissions</span>
                                                    </div>
                                                    <div class="format-item text-danger">
                                                        <i class="bi bi-exclamation-circle"></i>
                                                        <span>Receipt reference numbers required</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                const toggleBtn = document.querySelector('[data-bs-toggle="collapse"]');
                                const clickableHeader = document.querySelector('.clickable-header');

                                // Add hover effect to the entire header
                                clickableHeader.addEventListener('mouseenter', function () {
                                    this.style.transform = 'translateY(-2px)';
                                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                                });

                                clickableHeader.addEventListener('mouseleave', function () {
                                    this.style.transform = 'translateY(0)';
                                    this.style.boxShadow = 'none';
                                });

                                // Make the entire header trigger the collapse
                                clickableHeader.addEventListener('click', function (e) {
                                    if (!e.target.closest('a')) { // Prevent triggering when clicking the PDF link
                                        toggleBtn.click();
                                    }
                                });

                                toggleBtn.addEventListener('click', function () {
                                    const isCollapsed = this.getAttribute('aria-expanded') === 'false';
                                    this.setAttribute('aria-expanded', isCollapsed);
                                    const icon = this.querySelector('.toggle-icon');
                                    icon.style.transition = 'transform 0.3s ease';
                                    icon.style.transform = isCollapsed ? 'rotate(180deg)' : 'rotate(0)';
                                });

                                // Add hover effect for PDF link
                                const pdfLink = document.querySelector('.hover-effect');
                                pdfLink.addEventListener('mouseenter', function () {
                                    this.style.transform = 'scale(1.1)';
                                    this.style.transition = 'transform 0.2s ease';
                                });

                                pdfLink.addEventListener('mouseleave', function () {
                                    this.style.transform = 'scale(1)';
                                });
                            });
                        </script>
                    </div>
                 </div>

                 </div>
            </div>
         </div>
    </div>


<div class="modal fade outbound-loading-modal" id="loadingModal" tabindex="-1" aria-hidden="true"
    data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-processing-header">
                <div class="processing-icon">
                    <div class="document-stack">
                        <div class="document doc1"></div>
                        <div class="document doc2"></div>
                        <div class="document doc3"></div>
                    </div>
                    <div class="processing-circle"></div>
                </div>
                <div class="processing-title">
                    <h5>Processing Invoice Data</h5>
                    <p>Please wait while we save your documents from BQE Core</p>
                </div>
            </div>

            <div class="invoice-processing-container">
                <div class="invoice-animation">
                    <div class="invoice-paper">
                        <div class="invoice-header">
                            <div class="invoice-line"></div>
                        </div>
                        <div class="invoice-details">
                            <div class="invoice-details-left">
                                <div class="invoice-details-line"></div>
                                <div class="invoice-details-line"></div>
                            </div>
                            <div class="invoice-details-right">
                                <div class="invoice-details-line"></div>
                                <div class="invoice-details-line"></div>
                            </div>
                        </div>
                        <div class="invoice-table">
                            <div class="invoice-table-row">
                                <div class="invoice-table-cell"></div>
                                <div class="invoice-table-cell"></div>
                                <div class="invoice-table-cell"></div>
                            </div>
                            <div class="invoice-table-row">
                                <div class="invoice-table-cell"></div>
                                <div class="invoice-table-cell"></div>
                                <div class="invoice-table-cell"></div>
                            </div>
                        </div>
                        <div class="invoice-stamp"></div>
                    </div>
                    <div class="processing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>

                <div class="processing-steps">
                    <div class="step-item" id="step1">
                        <i class="bi bi-file-text"></i>
                        <span>Fetching from BQE</span>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step-item" id="step2">
                        <i class="bi bi-check2-circle"></i>
                        <span>Checking Documents</span>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step-item" id="step3">
                        <i class="bi bi-cloud-upload"></i>
                        <span>Document Processing</span>
                    </div>
                </div>

                <div id="loadingMessage" class="processing-status">
                    <div class="status-icon">
                        <i class="bi bi-arrow-repeat spin"></i>
                    </div>
                    <span class="status-text">Initializing document processing...</span>
                </div>
            </div>

            <div class="progress-section">
                <div class="progress-header">
                    <div class="progress-info">
                        <span class="progress-label">Processing Progress</span>
                        <span class="progress-percentage" id="progressPercentage">0%</span>
                    </div>
                    <div class="document-count">
                        <i class="bi bi-files"></i>
                        <span id="processedCount">0/0</span> documents
                    </div>
                </div>
                <div class="progress">
                    <div id="loadingProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                        role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
            </div>

            <div class="processing-info">
                <div id="loadingFact" class="info-box">
                    <div class="info-icon">
                        <i class="bi bi-lightbulb"></i>
                    </div>
                    <div class="info-content">
                        <span class="info-label">Processing Tip</span>
                        <p class="info-message">Loading tip...</p>
                    </div>
                </div>
            </div>

            <div id="loadingErrorContainer" class="mt-3 d-none">
                <div class="error-content">
                    <div class="error-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="error-message" id="loadingErrorMessage">
                        Document processing encountered an error
                    </div>
                    <button id="loadingRetryButton" class="retry-button">
                        <i class="bi bi-arrow-repeat"></i>
                        Retry Processing
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Manual Submission Modal -->
<div class="modal fade" id="manualSubmissionModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content manual-submission-modal">
            <div class="manual-modal-header">
                <div class="manual-modal-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="manual-modal-title">Submitting Manual Document to LHDN</div>
                <div class="manual-modal-subtitle">Please wait while we process your request</div>
            </div>
            <div class="manual-modal-body">
                <div class="manual-steps-container">
                    <div class="manual-step-card" id="manualStep1">
                        <div class="manual-step-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="manual-step-content">
                            <div class="manual-step-title">Validating Document</div>
                            <div class="manual-step-status">Waiting...</div>
                        </div>
                    </div>
                    <div class="manual-step-card" id="manualStep2">
                        <div class="manual-step-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="manual-step-content">
                            <div class="manual-step-title">Submit to LHDN</div>
                            <div class="manual-step-status">Waiting...</div>
                        </div>
                    </div>
                    <div class="manual-step-card" id="manualStep3">
                        <div class="manual-step-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="manual-step-content">
                            <div class="manual-step-title">Processing</div>
                            <div class="manual-step-status">Waiting...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Details Modal -->
<div class="modal fade lhdn-inbound-modal" id="viewDetailsModal" tabindex="-1" role="dialog" aria-modal="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Loading Overlay -->
            <div id="modalLoadingOverlay" class="modal-loading-overlay d-none">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <div class="d-flex align-items-center">
                    <div>
                        <h5 class="modal-title mb-0">
                            <i class="bi bi-file-text"></i>
                            Document Details
                        </h5>
                        <!-- <small id="invoice-number" class="text-white-50"></small> -->
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge-status me-3"></span>

                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <!-- Left Sidebar - Details -->
                <div class="info-sidebar">
                    <div class="info-section">
                        <div class="info-content">
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalInvoiceNumber"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalInvoiceStatus"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalInvoiceDate"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalInvoiceVersion"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalInvoiceType"></div>
                            </div>
                        </div>
                    </div>
                    <div class="info-section">
                        <div class="section-header">
                            <i class="bi bi-building"></i>
                            Supplier Information
                        </div>
                        <div class="info-content">
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalSupplierName"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalSupplierTin"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalSupplierBrn"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalSupplierSst"></div>
                            </div>
                        </div>
                    </div>
                    <!-- Buyer Information -->
                    <div class="info-section">
                        <div class="section-header">
                            <i class="bi bi-person"></i>
                            Buyer Information
                        </div>
                        <div class="info-content">
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalBuyerName"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalBuyerTin"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalBuyerBrn"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalBuyerAddress"></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label"></div>
                                <div class="info-value" id="modalZipCode"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Table Viewer -->
                <div class="pdf-section">
                    <div class="pdf-viewer-container">
                        <div class="loading-container" id="loadingSpinner">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="loading-text">Loading BQE Invoice Details...</div>
                        </div>
                        <div class="section-header">
                            <i class="bi bi-list-check"></i>
                            Line Items
                        </div>
                        <div class="p-2 info-content">
                            <div class="padding: 10px">
                                <table class="table table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>Classification Code</th>
                                            <th>Description</th>
                                            <th>Qty.</th>
                                            <th>Unit Price</th>
                                            <th>Amount</th>
                                            <th>Tax Rate</th>
                                            <th>Tax Amount</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody id="lineItemsBody" class="p-2">
                                        <!-- Line items will be inserted here -->
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn outbound-action-btn cancel" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn outbound-action-btn submit" id="submitToLhdnBtn">
                    <i class="bi bi-send"></i>
                    Submit to LHDN
                </button>
                <button type="button" class="btn-lhdn btn-cancel-invoice d-none">
                    <i class="bi bi-x-circle"></i>
                    Cancel Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<div class="mt-2">
    <div id="bqeSessionInfo" class="d-none">
        <div class="alert alert-info border-info shadow-sm p-2" style="font-size: 0.85rem;">
            <div class="d-flex align-items-center">
                <i class="bi bi-info-circle me-2"></i>
                <div>
                    <div class="fw-semibold mb-1">BQE Session Info</div>
                    <div class="small">
                        <div class="d-flex align-items-center mb-1">
                            <i class="bi bi-clock-history me-1"></i>
                            Expires in: <span id="sessionExpiryTime" class="ms-1 fw-semibold"></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-shield-check me-1"></i>
                            Status: <span id="sessionStatus" class="ms-1 fw-semibold text-success">Active</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



{% endblock %}
{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', async function () {
        try {
            console.log('DOM loaded, initializing managers...');

            // Array of loading messages with progress percentages
            const loadingStates = [
                { message: 'Initializing connection...', progress: 10 },
                { message: 'Checking database records...', progress: 20 },
                { message: 'Validating document formats...', progress: 35 },
                { message: 'Processing invoice data...', progress: 45 },
                { message: 'Analyzing submission status...', progress: 60 },
                { message: 'Preparing document summary...', progress: 70 },
                { message: 'Synchronizing with BQE...', progress: 80 },
                { message: 'Verifying tax information...', progress: 85 },
                { message: 'Updating invoice status...', progress: 90 },
                { message: 'Checking LHDN rules...', progress: 95 },
                { message: 'Validating buyer details...', progress: 98 },
                { message: 'Almost done...', progress: 99 }
            ];

            // Array of fun facts
            const funFacts = [
                'Automating invoicing can reduce errors by up to 80%.',
                'E-invoicing can save up to 80% in processing costs.',
                'Digital invoices are processed 5x faster than paper.',
                'E-invoicing reduces carbon footprint by 36%.',
                'Companies save 60-80% switching to e-invoicing.',
                'Digital invoices cut processing time by 50%.',
                'E-invoicing improves cash flow by 25%.',
                'Malaysia aims for 80% e-invoice adoption by 2025.',
                'E-invoicing reduces payment delays by 61%.',
                'Digital transformation saves 150+ hours annually.'
            ];

            let currentStateIndex = 0;
            let factInterval;

            // Function to update fun facts
            function updateFunFact() {
                const funFactElement = document.getElementById('loadingFact');
                if (!funFactElement) return;

                const randomFact = funFacts[Math.floor(Math.random() * funFacts.length)];
                funFactElement.innerHTML = `
                    <div class="fact-content">
                        <i class="bi bi-lightbulb"></i>
                        <div class="info-content">
                            <span class="info-label">Processing Tip</span>
                            <p class="info-message">${randomFact}</p>
                        </div>
                    </div>`;
            }

            // Function to update loading state
            function updateLoadingState() {
                const loadingMessage = document.getElementById('loadingMessage');
                const progressBar = document.getElementById('loadingProgressBar');
                const progressPercentage = document.getElementById('progressPercentage');
                const processedCount = document.getElementById('processedCount');

                if (!loadingMessage || !progressBar || !progressPercentage || !processedCount) return;

                if (currentStateIndex < loadingStates.length) {
                    const currentState = loadingStates[currentStateIndex];

                    // Update message
                    loadingMessage.innerHTML = `
                        <div class="status-icon">
                            <i class="bi bi-arrow-repeat spin"></i>
                        </div>
                        <span class="status-text">${currentState.message}</span>`;

                    // Update progress
                    progressBar.style.transition = 'width 0.8s ease-in-out';
                    progressBar.style.width = `${currentState.progress}%`;
                    progressBar.setAttribute('aria-valuenow', currentState.progress);
                    progressPercentage.textContent = `${currentState.progress}%`;

                    // Update document count
                    processedCount.textContent = `${Math.floor(currentState.progress / 10)}/10`;

                    // Update active step
                    updateProcessingStep(currentState.progress);

                    currentStateIndex++;
                }
            }

            // Function to update processing step indicators
            function updateProcessingStep(progress) {
                const step1 = document.getElementById('step1');
                const step2 = document.getElementById('step2');
                const step3 = document.getElementById('step3');

                if (!step1 || !step2 || !step3) return;

                step1.classList.remove('active');
                step2.classList.remove('active');
                step3.classList.remove('active');

                if (progress < 33) {
                    step1.classList.add('active');
                } else if (progress < 66) {
                    step2.classList.add('active');
                } else {
                    step3.classList.add('active');
                }
            }

            // Function to start loading sequence
            function startLoadingSequence() {
                currentStateIndex = 0;
                let interval = 1000; // Start with 1 second interval

                function scheduleNextUpdate() {
                    if (currentStateIndex < loadingStates.length) {
                        updateLoadingState();
                        // Increase interval as we progress
                        interval += 200;
                        setTimeout(scheduleNextUpdate, interval);
                    }
                }

                scheduleNextUpdate();
            }

            // Initialize loading modal events
            const loadingModal = document.getElementById('loadingModal');
            if (loadingModal) {
                loadingModal.addEventListener('shown.bs.modal', function () {
                    // Start sequences when modal is shown
                    startLoadingSequence();
                    factInterval = setInterval(updateFunFact, 5000);
                    updateFunFact(); // Initial fun fact
                });

                loadingModal.addEventListener('hidden.bs.modal', function () {
                    // Clean up when modal is hidden
                    clearInterval(factInterval);
                    currentStateIndex = 0;
                });
            }

            // Add event listener for retry button
            const retryButton = document.getElementById('loadingRetryButton');
            if (retryButton) {
                retryButton.addEventListener('click', function () {
                    // Hide error container
                    const errorContainer = document.getElementById('loadingErrorContainer');
                    if (errorContainer) {
                        errorContainer.classList.add('d-none');
                    }

                    // Reset progress bar
                    const progressBar = document.getElementById('loadingProgressBar');
                    if (progressBar) {
                        progressBar.style.width = '0%';
                        progressBar.setAttribute('aria-valuenow', '0');
                    }

                    // Restart sequences
                    clearInterval(factInterval);
                    factInterval = setInterval(updateFunFact, 5000);
                    startLoadingSequence();
                    updateFunFact();
                });
            }

            // Initialize the invoice table manager if it exists and hasn't been initialized yet
            if (typeof InvoiceTableManager !== 'undefined' && !window.invoiceTable) {
                window.invoiceTable = new InvoiceTableManager();
                // Initialize table and event listeners
                window.invoiceTable.initializeTable();
                window.invoiceTable.initializeEventListeners();
            }

        } catch (error) {
            console.error('Error initializing managers:', error);
            Swal.fire({
                icon: 'error',
                title: 'Initialization Error',
                text: 'Failed to initialize the application. Please refresh the page.',
                confirmButtonText: 'Refresh',
                showCancelButton: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.reload();
                }
            });
        }
    });



    function updateDateTime() {
        const timeElement = document.getElementById('currentTime');
        const dateElement = document.getElementById('currentDate');

        function update() {
            const now = new Date();

            // Update time
            timeElement.textContent = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });

            // Update date
            dateElement.textContent = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Update immediately and then every second
        update();
        setInterval(update, 1000);
    }

    // Call when the document is ready
    document.addEventListener('DOMContentLoaded', updateDateTime);

    // Add this to your existing DOMContentLoaded event handler
    function updateStepDots(currentStep) {
        const dots = document.querySelectorAll('.step-dot');
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentStep % dots.length);
        });
    }


    // Update the welcome message script
    document.addEventListener('DOMContentLoaded', function () {
        const welcomeElement = document.getElementById('middlewareWelcome');
        const shouldShow = !localStorage.getItem('middlewareWelcomeDismissed');

        if (welcomeElement && shouldShow) {
            // Show with slight delay for better UX
            setTimeout(() => {
                welcomeElement.style.display = 'block';
                welcomeElement.style.animation = 'slideIn 0.5s ease-out forwards';
            }, 1000);
        }

        // Restore checkbox state if previously saved
        const dontShowCheckbox = document.getElementById('dontShowAgain');
        if (dontShowCheckbox) {
            dontShowCheckbox.checked = localStorage.getItem('middlewareWelcomeDontShow') === 'true';
        }
    });

    function dismissWelcome(permanent = false) {
        const welcome = document.getElementById('middlewareWelcome');
        const dontShowCheckbox = document.getElementById('dontShowAgain');

        if (welcome) {
            welcome.style.animation = 'slideOut 0.5s ease-in forwards';

            // If user clicked "Got it" or checked "Don't show again"
            if (permanent || (dontShowCheckbox && dontShowCheckbox.checked)) {
                localStorage.setItem('middlewareWelcomeDismissed', 'true');
                localStorage.setItem('middlewareWelcomeDontShow', 'true');
            }

            setTimeout(() => {
                welcome.remove();
            }, 500);
        }
    }

    // Function to reset the welcome message (can be called from console for testing)
    function resetWelcomeMessage() {
        localStorage.removeItem('middlewareWelcomeDismissed');
        localStorage.removeItem('middlewareWelcomeDontShow');
        location.reload();
    }

    // Add these styles to your existing styles
    document.head.insertAdjacentHTML('beforeend', `
    <style>
        #middlewareWelcome {
            display: none;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
            to {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
        }

        #middlewareWelcome .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        #middlewareWelcome .btn-outline-secondary:hover {
            background-color: #f8f9fa;
            color: #0d6efd;
        }
    </style>
`);
</script>


{% endblock %}