const { validateNetworkPath, SERVER_CONFIG } = require('../config/paths');
const fs = require('fs').promises;

/**
 * Verifies service environment and network connectivity
 * @returns {Promise<Object>} Verification results
 */
async function verifyServiceEnvironment() {
    console.log('\n=== Starting Service Environment Verification ===\n');
    
    const results = {
        network: false,
        directories: false,
        errors: []
    };

    try {
        // Step 1: Verify network connectivity
        console.log('Verifying network connectivity...');
        const networkResult = await validateNetworkPath();
        
        if (!networkResult.success) {
            results.errors.push(`Network validation failed: ${networkResult.error}`);
            return results;
        }
        
        results.network = true;

        // Step 2: Verify required directories exist
        console.log('\nVerifying required directories...');
        const requiredDirs = [
            `${SERVER_CONFIG.networkPath}/Manual`,
            `${SERVER_CONFIG.networkPath}/Schedule`
        ];

        for (const dir of requiredDirs) {
            try {
                await fs.access(dir);
                console.log(`✓ Directory exists: ${dir}`);
            } catch (error) {
                results.errors.push(`Directory not accessible: ${dir}`);
                console.error(`✗ Directory not accessible: ${dir}`);
                return results;
            }
        }

        results.directories = true;
        console.log('\nService environment verification completed successfully');

    } catch (error) {
        results.errors.push(`Verification failed: ${error.message}`);
        console.error('Service environment verification failed:', error);
    }

    return results;
}

module.exports = verifyServiceEnvironment;