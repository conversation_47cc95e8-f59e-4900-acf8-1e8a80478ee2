'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_INBOUND_STATUS', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      uuid: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true
      },
      submissionUid: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      issuerName: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      issuerTin: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      issuerBrn: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      issuerVatRegNo: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      recipientName: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      recipientTin: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      recipientBrn: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      recipientVatRegNo: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      invoiceNumber: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      invoiceDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      currency: {
        type: Sequelize.STRING(10),
        allowNull: true
      },
      totalAmount: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: true
      },
      vatAmount: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: true
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'PENDING'
      },
      statusDescription: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      statusDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WP_USER_REGISTRATION',
          key: 'ID'
        }
      }
    });

    // Add indexes for common search fields
    await queryInterface.addIndex('WP_INBOUND_STATUS', ['invoiceNumber']);
    await queryInterface.addIndex('WP_INBOUND_STATUS', ['status']);
    await queryInterface.addIndex('WP_INBOUND_STATUS', ['issuerTin']);
    await queryInterface.addIndex('WP_INBOUND_STATUS', ['recipientTin']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_INBOUND_STATUS');
  }
}; 