process.env.PUPPETEER_CACHE_DIR = 'C:\\Users\\<USER>\\.cache\\puppeteer';

const express = require('express');
const cors = require('cors');
const path = require('path');
const bodyParser = require('body-parser');
const session = require('express-session');
const axios = require('axios');
const routes = require('./utils/routes');
const apiRoutes = require('./routes/api');
const { sequelize } = require('./models');
const fs = require('fs');
const { WP_USER_REGISTRATION, WP_LOGS, WP_COMPANY_SETTINGS } = require('./models');
const QRCode = require('qrcode');

const db = require('./models');
const https = require('https');
const bcrypt = require('bcrypt');
require('dotenv').config();
const crypto = require('crypto');
const { WP_SUBMISSION_STATUS, WP_INBOUND_STATUS } = require('./models');
const moment = require('moment');


const { Op } = require('sequelize');
const cons = require('consolidate');
const swig = require('swig-templates');
const bqeRoutes = require('./routes/bqe/bqe');
const AuthManager = require('./business/AuthManager');
const JWTManager = require('./business/JWTManager');
const Result = require('./shared/Result');
const GeneralMethods = require('./shared/GeneralMethods');

const invoiceConfig = {
  projectTitle: "NA",
  paymentDetails: `MIB-HSS ENGINEERING SDN BHD
MAYBANK ISLAMIC BERHAD "Collection Account"
102-6, Jalan Midah Satu,
Taman Midah, Cheras,
56000 Kuala Lumpur.
Account No.: 5147 0751 0991
Swift Code: MBBEMYKL (where applicable)`,
  paymentTitle: "Please remit the payment to:",
  defaultAttention: "Y.Bhg Tan Sri Ir. Kuna Sittampalam"
};

const app = express();
const port = 3002;


const agent = new https.Agent({
  secureProtocol: 'TLSv1_2_method' // Use TLS 1.2
});


const jsreport = require('jsreport-core')();

// Middleware for parsing request bodies
app.use(bodyParser.urlencoded({ extended: true }));

app.use(bodyParser.json());

app.use(cors());
//app.use(express.json());
app.use(express.json({limit: '50mb'}));
app.use(express.urlencoded({limit: '50mb', extended: true}));
// Set up view engine configuration
app.engine('html', cons.swig);
app.set('view engine', 'html'); // Make .html the default
app.set('views', path.join(__dirname, 'views')); // Set views directory

// Disable swig's cache in development
swig.setDefaults({ cache: false });

// Update your static file serving
app.use(express.static(path.join(__dirname, 'public')));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

jsreport.use(require('jsreport-jsrender')());
jsreport.use(require('jsreport-chrome-pdf')({
  launchOptions: {
    args: ["--no-sandbox"],
  },
  timeout: 180000,  // Increase timeout to 180 seconds
  verbose: true  // Enable verbose logging
}));


(async () => {
  await jsreport.init();
})();

// Session configuration
app.use(session({
  secret: 'e4d5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0',
  resave: false,
  saveUninitialized: true,
  cookie: {
    secure: true,
    httpOnly: true,
    maxAge: 1000 * 60 * 15,
    sameSite: 'lax'
  },
  name: 'bqe_session'
}));

// Token expiry check function
const checkTokenExpiry = (req) => {
  const accessToken = req.session.accessToken;
  const tokenExpiryTime = req.session.tokenExpiryTime;

  if (!accessToken || !tokenExpiryTime) {
    return false;
  }

  return Date.now() <= tokenExpiryTime;
};

// Idle timeout check function with automatic logout logging
const checkIdleTimeout = async (req, res, next) => {
  const idleTimeout = 5 * 60 * 1000; // 5 minutes in milliseconds (adjustable)

  if (req.session.user) {
    const now = Date.now();

    // If the user has been idle for too long, log them out
    if (req.session.lastActivity && now - req.session.lastActivity > idleTimeout) {
      const username = req.session.user.username;
      await WP_LOGS.create({
        Description: 'Automatic logout due to inactivity.',
        CreateTS: new Date(),
        LoggedUser: username
      });

      // Destroy session and log out
      req.session.destroy(err => {
        if (err) {
          return next(err);
        }
        res.clearCookie('connect.sid'); // Clear session cookie
        return res.redirect('/auth/login');
      });
      return;
    }

    // Update the last activity time
    req.session.lastActivity = now;
  }

  next();
};

// Middleware to check token expiry and idle timeout
app.use(checkIdleTimeout);

app.use(async (req, res, next) => {
  if (req.session.user || req.path === '/login' || req.path === '/auth/register' || req.path.startsWith('/test-lhdn') || req.path.startsWith('/api')) {
    if (req.session.user) {
      req.username = req.session.user.username;
      req.admin = req.session.user.admin;

      // Check if the token has expired
      if (!checkTokenExpiry(req)) {
        await WP_LOGS.create({
          Description: 'Automatic logout due to expired token.',
          CreateTS: new Date(),
          LoggedUser: req.session.user.username
        });
        return res.redirect('/login');
      }
    }
    next();
  } else {
    res.redirect('/login');
  }
});

// Login route
const loginAttempts = {}; // Store for tracking failed attempts
const maxFailedAttempts = 3;
const blockDuration = 5 * 60 * 1000; // 5 minutes in milliseconds

app.post('/api/login', async (req, res) => {
  const { username, password } = req.body;
  const currentTime = Date.now();
  const clientIP = req.ip;
  const userAgent = req.headers['user-agent'];

  // Check if the user is currently blocked
  if (loginAttempts[username] && loginAttempts[username].blockedUntil > currentTime) {
    const remainingTime = Math.ceil((loginAttempts[username].blockedUntil - currentTime) / 60000);
    
    // Log blocked attempt
    await WP_LOGS.create({
      Description: `Login blocked - Account temporarily locked due to multiple failed attempts`,
      CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
      LoggedUser: username,
      Details: JSON.stringify({
        action: 'login_blocked',
        username: username,
        remainingLockTime: `${remainingTime} minutes`,
        userIP: clientIP,
        userAgent: userAgent,
        failedAttempts: loginAttempts[username].count
      })
    });

    res.status(403).json({ 
      success: false, 
      message: `Too many failed login attempts. Please try again in ${remainingTime} minute(s).` 
    });
    return;
  }

  try {
    const user = await WP_USER_REGISTRATION.findOne({
      where: { Username: username },
      attributes: ['Username', 'Password', 'Admin', 'TIN', 'IDType', 'IDValue']
    });

    if (user) {
      const isPasswordValid = await bcrypt.compare(password, user.Password);
      if (isPasswordValid) {
        if (user.Admin === 1 || user.Admin === 2) {
          req.session.user = {
            username: user.Username,
            admin: user.Admin,
            IDType: user.IDType,
            IDValue: user.IDValue,
            TIN: user.TIN
          };

          // Log successful login with enhanced details
          await WP_LOGS.create({
            Description: `Login successful - User ${user.Username} authenticated successfully`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: user.Username,
            Details: JSON.stringify({
              action: 'login_success',
              username: user.Username,
              adminLevel: user.Admin,
              userIP: clientIP,
              userAgent: userAgent,
              sessionId: req.sessionID,
              loginTime: moment().format('YYYY-MM-DD HH:mm:ss')
            })
          });

          const accessToken = await generateAccessToken(req);
          req.session.accessToken = accessToken;
          req.session.tokenExpiryTime = Date.now() + (24 * 60 * 60 * 1000);
          
          // Reset failed attempts on successful login
          loginAttempts[username] = { count: 0, blockedUntil: 0 };

          res.json({ 
            success: true, 
            message: 'Login successful',
            redirectUrl: '/dashboard'
          });
        } else {
          // Log unauthorized access attempt
          await WP_LOGS.create({
            Description: `Unauthorized access attempt - User ${username} does not have sufficient privileges`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: username,
            Details: JSON.stringify({
              action: 'login_unauthorized',
              username: username,
              adminLevel: user.Admin,
              userIP: clientIP,
              userAgent: userAgent,
              reason: 'Insufficient privileges'
            })
          });

          res.status(401).json({ success: false, message: 'Unauthorized access. Contact your administrator.' });
        }
      } else {
        await trackFailedLogin(username, currentTime, 'Invalid password', clientIP, userAgent);
        res.status(401).json({ success: false, message: 'Invalid credentials' });
      }
    } else {
      await trackFailedLogin(username, currentTime, 'User not found', clientIP, userAgent);
      res.status(401).json({ success: false, message: 'Invalid credentials' });
    }
  } catch (error) {
    // Log system error during login
    await WP_LOGS.create({
      Description: `System error during login attempt for user ${username}`,
      CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
      LoggedUser: username,
      Details: JSON.stringify({
        action: 'login_error',
        username: username,
        error: error.message,
        errorStack: error.stack,
        userIP: clientIP,
        userAgent: userAgent
      })
    });

    console.error('Login error:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }

  async function trackFailedLogin(username, currentTime, reason, clientIP, userAgent) {
    if (!loginAttempts[username]) {
      loginAttempts[username] = { count: 0, blockedUntil: 0 };
    }
    
    loginAttempts[username].count++;
    const attemptCount = loginAttempts[username].count;

    if (attemptCount >= maxFailedAttempts) {
      loginAttempts[username].blockedUntil = currentTime + blockDuration;
      
      // Log account lockout
      await WP_LOGS.create({
        Description: `Account locked - User ${username} account locked due to ${attemptCount} failed login attempts`,
        CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
        LoggedUser: username,
        Details: JSON.stringify({
          action: 'account_locked',
          username: username,
          failedAttempts: attemptCount,
          lockDuration: `${blockDuration/60000} minutes`,
          reason: reason,
          userIP: clientIP,
          userAgent: userAgent,
          lockTime: moment().format('YYYY-MM-DD HH:mm:ss')
        })
      });
    }

    // Log failed login attempt
    await WP_LOGS.create({
      Description: `Failed login attempt - ${reason}`,
      CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
      LoggedUser: username,
      Details: JSON.stringify({
        action: 'login_failed',
        username: username,
        attemptCount: attemptCount,
        reason: reason,
        userIP: clientIP,
        userAgent: userAgent,
        willLock: attemptCount >= maxFailedAttempts
      })
    });
  }
});



// app.post('/api/login', async (req, res) => {
//   const { username, password } = req.body;
//   try {
//     const user = await WP_USER_REGISTRATION.findOne({
//       where: { Username: username },
//       attributes: ['Username', 'Password', 'Admin', 'TIN', 'IDType', 'IDValue']
//     });

//     if (user) {
//       const isPasswordValid = await bcrypt.compare(password, user.Password);
//       if (isPasswordValid) {
//         if (user.Admin === 1 || user.Admin === 2) {
//           req.session.user = {
//             username: user.Username,
//             admin: user.Admin,
//             IDType: user.IDType,
//             IDValue: user.IDValue,
//             TIN: user.TIN
//           };
//           const loginTimestamp = new Date().toISOString();
//           await WP_LOGS.create({
//             Description: `Login successful for user ${user.Username}. Authentication confirmed and access granted at ${loginTimestamp}.`,
//             CreateTS: new Date(),
//             LoggedUser: user.Username
//           });

//           const accessToken = await generateAccessToken(req);
//           req.session.accessToken = accessToken;
//           req.session.tokenExpiryTime = Date.now() + (24 * 60 * 60 * 1000); // 24 hours expiry
          
//           res.json({ success: true, message: 'Login successful', accessToken });
//         } else {
//           res.status(401).json({ success: false, message: 'Unauthorized access. Contact your administrator.' });
//         }
//       } else {
//         await WP_LOGS.create({
//           Description: `Failed login attempt due to incorrect password.`,
//           CreateTS: new Date(),
//           LoggedUser: username
//         });
//         res.status(401).json({ success: false, message: 'Invalid credentials' });
//       }
//     } else {
//       await WP_LOGS.create({
//         Description: `Failed login attempt due to non-existent username.`,
//         CreateTS: new Date(),
//         LoggedUser: username
//       });
//       res.status(401).json({ success: false, message: 'Invalid credentials' });
//     }
//   } catch (error) {
//     console.error('Login error:', error);
//     res.status(500).json({ success: false, message: 'Internal server error' });
//   }
// });

// Logout route
app.get('/auth/logout', (req, res) => {
  const username = req.session.user ? req.session.user.username : 'Unknown';
  const clientIP = req.ip;
  const userAgent = req.headers['user-agent'];
  const sessionData = { ...req.session }; // Capture session data before destroying

  req.session.destroy(async err => {
    if (err) {
      // Log logout error
      await WP_LOGS.create({
        Description: `Logout error - Failed to destroy session for user ${username}`,
        CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
        LoggedUser: username,
        Details: JSON.stringify({
          action: 'logout_error',
          username: username,
          error: err.message,
          errorStack: err.stack,
          userIP: clientIP,
          userAgent: userAgent,
          sessionId: req.sessionID
        })
      });
      return res.redirect('/dashboard');
    }

    res.clearCookie('connect.sid'); // Clear session cookie

    // Log successful logout
    await WP_LOGS.create({
      Description: `Logout successful - User ${username} logged out successfully`,
      CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
      LoggedUser: username,
      Details: JSON.stringify({
        action: 'logout_success',
        username: username,
        userIP: clientIP,
        userAgent: userAgent,
        sessionId: sessionData.id,
        logoutTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        sessionDuration: sessionData.lastActivity ? 
          `${Math.round((Date.now() - sessionData.lastActivity)/1000)} seconds` : 
          'Unknown'
      })
    });

    res.redirect('/auth/login');
  });
});



// Authentication middleware
// app.use(async (req, res, next) => {
//   //console.log('Session User:', req.session.user); // Log the session user
//   if (req.session.user || req.path === '/login' || req.path === '/auth/register' || req.path.startsWith('/test-lhdn') || req.path.startsWith('/api')) {
//     if (req.session.user) {
//       req.username = req.session.user.username; 
//       req.admin = req.session.user.admin; 
//     }
//     next();
//   } else {
//     res.redirect('/login');
//   }
// });



// Submit
app.post('/api/send-lhdn', require('./services/sendXMLtoLHDNData'));

// Route for dashboard
app.get('/dashboard', (req, res) => {
  res.render('dashboard/index', {
    title: 'Dashboard',
    user: req.session.user || null
  });
});

// Add this route handler
app.get('/sftp-manager', (req, res) => {
  res.sendFile(path.join(__dirname, './src/pages/dashboard', 'sftp-manager.html'));
});

// Route for dashboard
app.get('/logs', (req, res) => {
  res.render('dashboard/audit-trail', {
    title: 'Audit Trail',
    user: req.session.user || null
  });
});

// Route for profile
app.get('/profile', (req, res) => {
  res.sendFile(path.join(__dirname, './src/pages/dashboard', 'profile.html'));
});

// Route for company settings
app.get('/company-settings', (req, res) => {
  res.render('dashboard/company-settings', {
    title: 'Company Settings',
    user: req.session.user || null
  });
});

app.get('/api/file-count', async (req, res) => {
  const { period } = req.query;
  let dateFilter = new Date(0); // Default to epoch time

  switch (period) {
    case 'today':
      dateFilter = new Date();
      dateFilter.setHours(0, 0, 0, 0);
      break;
    case 'this-month':
      dateFilter = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
      break;
    case 'this-year':
      dateFilter = new Date(new Date().getFullYear(), 0, 1);
      break;
  }

  try {
    const fileCount = await countFilesInDirectoryWithDateFilter(srcXmlPath, dateFilter);
    res.json({ fileCount });
  } catch (error) {
    console.error('Error counting files:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});


const generateAccessToken = async (req) => {
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');
  params.append('client_id', process.env.CLIENT_ID);
  params.append('client_secret', process.env.CLIENT_SECRET);
  params.append('scope', 'InvoicingAPI');

  try {
    const response = await axios.post(`${process.env.ID_SRV_BASE_URL}/connect/token`, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    const accessToken = response.data.access_token;
    const expiresIn = response.data.expires_in; 

    // Store the token and its expiry time in the session
    req.session.accessToken = accessToken;
    req.session.tokenExpiryTime = Date.now() + expiresIn * 1000; // current time + token lifespan

    console.log('Access token generated:', accessToken);
    console.log('Token expiry time:', req.session.tokenExpiryTime);

    return accessToken;
  } catch (error) {
    console.error('Error generating access token:', error.response ? error.response.data : error.message);
    throw new Error('Failed to generate access token');
  }
};

// Fetch document by UUID
app.get('/api/documents/:uuid/document', async (req, res) => {
  const { uuid } = req.params;
  try {
    const response = await axios.get(`https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/${uuid}/raw`, {
      headers: {
        'Authorization': `Bearer ${req.session.accessToken}`
      }
    });
    console.log("Document API response:", response.data); // Log the document data
    res.json({ success: true, data: response.data });
  } catch (error) {
    console.error('Error fetching document details:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch document details' });
  }
});


// Fetch document details by UUID
app.get('/api/documents/:uuid/details', async (req, res) => {
  const { uuid } = req.params;
  try {
    const response = await axios.get(`https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/${uuid}/details`, {
      headers: {
        'Authorization': `Bearer ${req.session.accessToken}`
      }
    });
    console.log("Document Details API response:", response.data); // Log the document details
    res.json({ success: true, data: response.data });
  } catch (error) {
    console.error('Error fetching document details:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch document details' });
  }
});

// Add display-details endpoint
app.get('/api/documents/:uuid/display-details', async (req, res) => {
  try {
    const { uuid } = req.params;
    
    // Get raw document data
    const rawResponse = await axios.get(
      `https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/${uuid}/raw`,
      {
        headers: { 'Authorization': `Bearer ${req.session.accessToken}` }
      }
    );

    if (!rawResponse.data) {
      throw new Error('Failed to fetch document data');
    }

    const rawData = rawResponse.data;
    const parsedDoc = JSON.parse(rawData.document);
    const invoice = parsedDoc.Invoice[0];

    // Extract supplier details
    const supplierParty = invoice.AccountingSupplierParty[0].Party[0];
    const customerParty = invoice.AccountingCustomerParty[0].Party[0];

    // Format the response to match the display-details API structure
    const displayData = {
      success: true,
      documentInfo: {
        status: rawData.status,
        internalId: rawData.internalId,
        invoiceType: rawData.typeName,
        invoiceDate: invoice.IssueDate[0]._ + 'T' + invoice.IssueTime[0]._
      },
      supplierInfo: {
        company: supplierParty.PartyLegalEntity[0].RegistrationName[0]._,
        tin: rawData.issuerTin,
        registrationNo: supplierParty.PartyIdentification.find(id => id.ID[0].schemeID === 'BRN')?.ID[0]._ || 'NA',
        taxRegNo: supplierParty.PartyIdentification.find(id => id.ID[0].schemeID === 'SST')?.ID[0]._ || 'NA',
        tourismTaxRegNo: supplierParty.PartyIdentification.find(id => id.ID[0].schemeID === 'TTX')?.ID[0]._ || 'NA',
        msicCode: supplierParty.IndustryClassificationCode?.[0]._ || 'NA',
        businessActivity: supplierParty.IndustryClassificationCode?.[0]?.name || 
                         supplierParty.IndustryClassificationCode?.[0]?.description || 
                         'NA',
        businessDesc: supplierParty.IndustryClassificationCode?.[0]?.name || 
                      supplierParty.IndustryClassificationCode?.[0]?.description || 
                      'NA',
        email: supplierParty.Contact?.[0]?.ElectronicMail?.[0]._ || 'NA',
        contact: supplierParty.Contact?.[0]?.Telephone?.[0]._ || 'NA',
        address: supplierParty.PostalAddress[0].AddressLine
          .map(line => line.Line[0]._)
          .filter(Boolean)
          .join(', ') || 'NA'
      },
      customerInfo: {
        company: customerParty.PartyLegalEntity[0].RegistrationName[0]._,
        tin: rawData.receiverId,
        registrationNo: customerParty.PartyIdentification.find(id => id.ID[0].schemeID === 'BRN')?.ID[0]._ || 'NA',
        taxRegNo: customerParty.PartyIdentification.find(id => id.ID[0].schemeID === 'SST')?.ID[0]._ || 'NA',
        address: customerParty.PostalAddress[0].AddressLine
          .map(line => line.Line[0]._)
          .filter(Boolean)
          .join(', ') || 'NA'
      },
      paymentInfo: {
        paymentMode: invoice.PaymentMeans?.[0]?.PaymentMeansCode?.[0]._ || 'NA',
        paymentTerms: invoice.PaymentTerms?.[0]?.Note?.[0]._ || 'NA',
        paymentDate: invoice.PaymentMeans?.[0]?.PaymentDueDate?.[0]._ || 'NA',
        paymentNote: invoice.PaymentMeans?.[0]?.PaymentNote?.[0]._ || 'NA',
        paymentRefNo: invoice.PaymentMeans?.[0]?.PayeeFinancialAccount?.[0]?.ID?.[0]._ || 'NA',
        totalIncludingTax: rawData.totalPayableAmount,
        totalExcludingTax: rawData.totalExcludingTax,
        validationDateTime: rawData.dateTimeValidated,
        irbmUniqueNo: rawData.uuid
      }
    };

    res.json(displayData);

  } catch (error) {
    console.error('Error generating display details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate display details',
      error: error.message,
      details: error.response?.data
    });
  }
});

// Add this function to check if PDF already exists
async function getPdfHash(documentData) {
  const dataToHash = JSON.stringify({
    internalId: documentData.internalId,
    dateTimeValidated: documentData.dateTimeValidated,
    totalPayableAmount: documentData.totalPayableAmount
  });
  return crypto.createHash('md5').update(dataToHash).digest('hex');
}

// Add a cleanup function to remove old files
function cleanupOldFiles() {
  const pdfDir = path.join(__dirname, 'pdf');
  if (!fs.existsSync(pdfDir)) return;

  fs.readdir(pdfDir, (err, files) => {
    if (err) {
      console.error('Error reading pdf directory:', err);
      return;
    }

    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    files.forEach(file => {
      const filePath = path.join(pdfDir, file);
      fs.stat(filePath, (err, stats) => {
        if (err) {
          console.error(`Error getting stats for file ${file}:`, err);
          return;
        }

        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlink(filePath, err => {
            if (err) {
              console.error(`Error deleting file ${file}:`, err);
            } else {
              console.log(`Deleted old file: ${file}`);
            }
          });
        }
      });
    });
  });
}

// Run cleanup every 6 hours
setInterval(cleanupOldFiles, 6 * 60 * 60 * 1000);
// Run cleanup on server start
cleanupOldFiles();

// Clean up any existing auth files on server start
try {
    const authFilePath = path.join(__dirname, 'AuthResponse.ini');
    if (fs.existsSync(authFilePath)) {
        fs.unlinkSync(authFilePath);
        console.log('Cleaned up existing auth file');
    }
} catch (error) {
    console.error('Error cleaning up auth file:', error);
}

// Initialize PDF cache
const PDFCache = new Map();

// Add cache cleanup function 
function cleanupPDFCache() {
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  const now = Date.now();
  
  for (const [key, value] of PDFCache.entries()) {
    if (value.timestamp && now - value.timestamp > maxAge) {
      PDFCache.delete(key);
    }
  }
}

// Add cache cleanup interval
setInterval(cleanupPDFCache, 60 * 60 * 1000); // Clean up every hour

// Modified generate-invoice-report endpoint with optimizations
app.post('/generate-invoice-report', async (req, res) => {
  const { uuid, longId, forceRegenerate } = req.body;

  if (!uuid || !longId) {
    return res.status(400).json({ 
      success: false, 
      message: 'Missing required parameters: uuid or longId' 
    });
  }

  try {
    // Ensure pdf directory exists
    const pdfDir = path.join(__dirname, 'pdf');
    if (!fs.existsSync(pdfDir)) {
      fs.mkdirSync(pdfDir, { recursive: true });
    }

    // Add debug logging
    console.log('Generating PDF for document:', { uuid, longId });

    // Get raw document data
    const rawResponse = await axios.get(
      `https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/${uuid}/raw`,
      {
        headers: { 
          'Authorization': `Bearer ${req.session.accessToken}`,
          // Add debug logging for token
          'X-Debug-Token': req.session.accessToken ? 'Token present' : 'Token missing'
        }
      }
    );

    console.log('Raw API response status:', rawResponse.status);

    if (!rawResponse.data) {
      throw new Error('Failed to fetch document data');
    }

    const rawData = rawResponse.data;
    const documentHash = await getPdfHash(rawData);
    const fileName = `${rawData.internalId}-${documentHash}-invoice.pdf`;
    const filePath = path.join(pdfDir, fileName);

    // Return existing file if available and not forcing regenerate
    if (fs.existsSync(filePath) && !forceRegenerate) {
      return res.json({ 
        success: true,
        url: `/pdf/${fileName}`,
        fileName: fileName,
        contentType: 'application/pdf'
      });
    }

    // Delete old file if exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    const templatePath = path.join(__dirname, 'src', 'reports', 'invoice-template.html');
    if (!fs.existsSync(templatePath)) {
      console.error('Template file not found at:', templatePath);
      throw new Error('Template file not found');
    }
    
    const templateContent = fs.readFileSync(templatePath, 'utf8');

    // Parse document data
    const parsedDoc = JSON.parse(rawData.document);
    const invoice = parsedDoc.Invoice[0];
    const supplierParty = invoice.AccountingSupplierParty[0].Party[0];
    const customerParty = invoice.AccountingCustomerParty[0].Party[0];

    // Generate QR code
    const qrUrl = `https://preprod.myinvois.hasil.gov.my/${uuid}/share/${longId}`;
    const qrCodeData = await QRCode.toDataURL(qrUrl);

    // Prepare minimal data for PDF
    const summaryData = {
      // Essential supplier info
      SupplierName: rawData.issuerName || '',
      SupplierAddress: supplierParty.PostalAddress[0].AddressLine
        .map(line => line.Line[0]._)
        .filter(Boolean)
        .join('\n'),
      SupplierTIN: rawData.issuerTin || '',

      // Essential buyer info  
      BuyerName: rawData.receiverName || 'NA',
      BuyerAddress: customerParty.PostalAddress[0].AddressLine
        .map(line => line.Line[0]._)
        .filter(Boolean)
        .join('\n'),
      BuyerTIN: rawData.receiverId || 'NA',

      // Essential invoice details
      InvoiceNumber: rawData.internalId || '',
      InvoiceDate: new Date(rawData.dateTimeIssued).toLocaleDateString('en-MY') || '',
      InvoiceCurrency: invoice.DocumentCurrencyCode?.[0]._ || 'MYR',

      // Line items with minimal data
      items: invoice.InvoiceLine.map(line => ({
        Description: line.Item[0].Description[0]._ || 'NA',
        UnitPrice: formatNumber(parseFloat(line.Price[0].PriceAmount[0]._) || 0),
        Amount: formatNumber(parseFloat(line.LineExtensionAmount[0]._) || 0)
      })),

      // Essential totals
      TotalExcludingTax: formatNumber(invoice.LegalMonetaryTotal[0].TaxExclusiveAmount[0]._ || 0),
      TaxAmount: formatNumber(invoice.TaxTotal[0].TaxAmount[0]._ || 0),
      TotalIncludingTax: formatNumber(invoice.LegalMonetaryTotal[0].TaxInclusiveAmount[0]._ || 0),

      qrCodeDataUrl: qrCodeData
    };

    // Generate PDF with optimized options
    const result = await jsreport.render({
      template: {
        content: templateContent,
        engine: 'jsrender',
        recipe: 'chrome-pdf',
        chrome: {
          timeout: 60000, // Reduced timeout
          launchOptions: { 
            args: ['--no-sandbox', '--disable-gpu', '--disable-dev-shm-usage']
          },
          pdfOptions: {
            format: 'A4',
            landscape: true,
            margin: { top: '10mm', right: '15mm', bottom: '10mm', left: '15mm' },
            printBackground: true,
            scale: 0.8 // Reduced scale for better performance
          }
        }
      },
      data: summaryData
    });

    // Add more detailed error handling
    if (!result || !result.content) {
      throw new Error('PDF generation failed - no content received');
    }

    // Save PDF and update cache
    fs.writeFileSync(filePath, result.content);
    
    console.log('PDF generated successfully:', fileName);

    return res.json({
      success: true,
      url: `/pdf/${fileName}`,
      fileName: fileName,
      contentType: 'application/pdf'
    });

  } catch (error) {
    console.error('Detailed error in PDF generation:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'PDF generation failed',
      error: error.message,
      details: error.response?.data || 'No additional details available'
    });
  }
});

// Helper function to format numbers
function formatNumber(value) {
  if (!value || isNaN(value)) return '0.00';
  
  // Convert to float and fix to 2 decimal places
  const num = parseFloat(value);
  return num.toLocaleString('en-MY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

app.get('/generate-invoice-report', async (req, res) => {
  try {

    const reportResponse = await axios.post('/generate-invoice-report', data);

    const pdfUrl = reportResponse.data.url;
    res.redirect(pdfUrl);
  } catch (error) {
    console.error(error);
    res.status(500).send('Error generating report');
  }
});

// Add PDF preview endpoint
app.get('/api/documents/:uuid/preview', async (req, res) => {
  try {
    const { uuid } = req.params;
    
    // First try to get the raw document data
    const rawResponse = await axios.get(
      `https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/${uuid}/raw`,
      {
        headers: { 'Authorization': `Bearer ${req.session.accessToken}` }
      }
    );

    if (!rawResponse.data) {
      throw new Error('Failed to fetch document data');
    }

    const rawData = rawResponse.data;
    console.log('Raw document data:', rawData);

    // Parse the document JSON
    const parsedDoc = JSON.parse(rawData.document);
    const invoice = parsedDoc.Invoice[0];

    // Extract supplier details
    const supplierParty = invoice.AccountingSupplierParty[0].Party[0];
    const customerParty = invoice.AccountingCustomerParty[0].Party[0];

    // Format the data for the PDF
    const data = {
      // Company Info
      companyName: supplierParty.PartyLegalEntity[0].RegistrationName[0]._,
      companyAddress: supplierParty.PostalAddress[0].AddressLine
        .map(line => line.Line[0]._)
        .filter(Boolean)
        .join(', '),
      companyPhone: supplierParty.Contact[0].Telephone[0]._ || 'N/A',
      companyEmail: supplierParty.Contact[0].ElectronicMail[0]._ || 'N/A',
      
      // Document Info
      validationDateTime: new Date(rawData.dateTimeValidated).toLocaleString('en-CA'),
      InvoiceType: rawData.typeName,
      InvoiceVersion: invoice.InvoiceTypeCode[0].listVersionID || '1.0',
      InvoiceCode: rawData.internalId,
      DocumentCode: rawData.internalId,
      UniqueIdentifier: rawData.uuid,
      
      // Supplier Info
      SupplierTIN: rawData.issuerTin,
      SupplierRegistrationNumber: supplierParty.PartyIdentification
        .find(id => id.ID[0].schemeID === 'BRN')?.ID[0]._ || 'N/A',
      SupplierSSTID: supplierParty.PartyIdentification
        .find(id => id.ID[0].schemeID === 'SST')?.ID[0]._ || 'N/A',
      SupplierMSICCode: supplierParty.IndustryClassificationCode?.[0]._ || 'N/A',
      SupplierBusinessDesc: supplierParty.IndustryClassificationCode?.[0]._ || 'N/A',
      SupplierCity: supplierParty.PostalAddress[0].CityName?.[0]._ || 'N/A',
      SupplierPostalCode: supplierParty.PostalAddress[0].PostalZone?.[0]._ || 'N/A',
      SupplierCountryCode: supplierParty.PostalAddress[0].Country?.[0].IdentificationCode?.[0]._ || 'N/A',
      
      // Buyer Info
      BuyerTIN: rawData.receiverId,
      BuyerName: customerParty.PartyLegalEntity[0].RegistrationName[0]._,
      BuyerRegistrationNumber: customerParty.PartyIdentification
        .find(id => id.ID[0].schemeID === 'BRN')?.ID[0]._ || 'N/A',
      BuyerSSTID: customerParty.PartyIdentification
        .find(id => id.ID[0].schemeID === 'SST')?.ID[0]._ || 'N/A',
      BuyerAddress: customerParty.PostalAddress[0].AddressLine
        .map(line => line.Line[0]._)
        .filter(Boolean)
        .join(', '),
      BuyerCity: customerParty.PostalAddress[0].CityName?.[0]._ || 'N/A',
      BuyerPostalCode: customerParty.PostalAddress[0].PostalZone?.[0]._ || 'N/A',
      BuyerCountryCode: customerParty.PostalAddress[0].Country?.[0].IdentificationCode?.[0]._ || 'N/A',
      BuyerPhone: customerParty.Contact?.[0]?.Telephone?.[0]._ || 'N/A',
      BuyerEmail: customerParty.Contact?.[0]?.ElectronicMail?.[0]._ || 'N/A',
      
      // Dates
      InvoiceDate: new Date(invoice.IssueDate[0]._).toLocaleDateString('en-CA'),
      InvoiceTime: new Date(invoice.IssueTime[0]._).toLocaleTimeString('en-CA'),
      
      // Payment Info
      PaymentMode: invoice.PaymentMeans?.[0]?.PaymentMeansCode?.[0]._ || 'N/A',
      PaymentTerms: invoice.PaymentTerms?.[0]?.Note?.[0]._ || 'N/A',
      PaymentAccountId: invoice.PaymentMeans?.[0]?.PayeeFinancialAccount?.[0]?.ID?.[0]._ || 'N/A',
      
      // Totals
      TotalIncTax: formatNumber(rawData.totalPayableAmount),
      TotalExclTax: formatNumber(rawData.totalExcludingTax), 
      TaxAmount: formatNumber(rawData.totalPayableAmount - rawData.totalExcludingTax),
      TotalDiscount: formatNumber(rawData.totalDiscount || 0),
      
      // Line Items
      items: invoice.InvoiceLine.map((line, index) => {
        const item = line.Item[0];
        const classification = item.CommodityClassification?.[0]?.ItemClassificationCode?.[0]._ || 'N/A';
        const unitPrice = parseFloat(line.Price[0].PriceAmount[0]._);
        const quantity = parseFloat(line.InvoicedQuantity[0]._);
        const amount = parseFloat(line.LineExtensionAmount[0]._);
        const discount = line.AllowanceCharge?.find(ac => ac.ChargeIndicator[0]._ === 'false')?.Amount?.[0]._ || 0;
        const taxAmount = parseFloat(line.TaxTotal[0].TaxAmount[0]._);
        const totalWithTax = amount + taxAmount;

        return {
          No: index + 1,
          Cls: classification,
          Description: item.Description[0]._,
          Quantity: `${quantity} ${line.InvoicedQuantity[0].unitCode}`,
          UnitPrice: formatNumber(unitPrice),
          Amount: formatNumber(amount),
          Disc: formatNumber(discount),
          TaxAmount: formatNumber(taxAmount),
          TotalExclTax: formatNumber(totalWithTax)
        };
      }),

      // Status
      Status: rawData.status
    };

    res.json({
      success: true,
      data: data
    });

  } catch (error) {
    console.error('Error generating preview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate preview',
      error: error.message,
      details: error.response?.data
    });
  }
});




app.get('/list-invoices', (req, res) => {
    const pdfDir = path.join(__dirname, 'pdf');
  
    fs.readdir(pdfDir, (err, files) => {
      if (err) {
        console.error(`Error reading directory: ${err.message}`);
        return res.status(500).send('Error reading directory');
      }
  
      const pdfFiles = files.filter(file => file.endsWith('.pdf'));
      res.json(pdfFiles);
    });
  });

app.get('/view-invoice/:fileName', (req, res) => {
    const fileName = req.params.fileName;
    const filePath = path.join(__dirname, 'pdf', fileName);

    if (fs.existsSync(filePath)){
        res.setHeader('Content-Type', 'application/pdf');
        fs.createReadStream(filePath).pipe(res);
    }else{
        res.status(400).send('Invoice not found');
    }
});
  

// Serve the generated PDF files
// Add proper MIME type handling
app.use('/pdf', (req, res, next) => {
  res.set('Content-Type', 'application/pdf');
  next();
}, express.static(path.join(__dirname, 'pdf')));

app.use('/', routes);
app.use('/api', apiRoutes);


sequelize.sync().then(() => {
  // Create HTTPS server
  const httpsOptions = {
    key: fs.readFileSync(path.join(__dirname, 'ssl', 'client-key.pem')),
    cert: fs.readFileSync(path.join(__dirname, 'ssl', 'client-cert.pem')),
    requestCert: false,
    rejectUnauthorized: false
  };

  const server = https.createServer(httpsOptions, app);

  // Add middleware to redirect HTTP to HTTPS
  app.use((req, res, next) => {
    if (!req.secure) {
      return res.redirect(['https://', req.get('Host'), req.url].join(''));
    }
    next();
  });

  server.listen(port, () => {
    console.log(`HTTPS Server started on https://localhost:${port}`);
    console.log('Open https://localhost:3002/authorize to start the OAuth flow');
  });
}).catch(err => {
  console.error('Unable to connect to the database:', err);
});

// Add this near your other routes
app.get('/api/health-check', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Add this near your other API routes, before app.use('/', routes)
app.post('/api/cancel-invoice', async (req, res) => {
  const loggedUser = req.session.user?.username;
  const { uuid, id } = req.body;

  console.log('Cancel request received:', { uuid, id, loggedUser });

  if (!uuid || !id) {
    return res.status(400).json({ 
      success: false, 
      message: 'Missing required parameters: uuid or id' 
    });
  }

  try {
    // First check if the document exists
    const documentResponse = await axios.get(
      `${process.env.API_BASE_URL}/api/v1.0/documents/${uuid}/details`,
      {
        headers: {
          'Authorization': `Bearer ${req.session.accessToken}`
        }
      }
    );

    if (!documentResponse.data) {
      throw new Error('Document not found');
    }

    // Proceed with cancellation
    const cancelResponse = await axios.put(
      `${process.env.API_BASE_URL}/api/v1.0/documents/${uuid}/cancel`,
      {
        reason: 'Cancelled via portal',
        additionalInformation: `Cancelled by user ${loggedUser}`
      },
      {
        headers: {
          'Authorization': `Bearer ${req.session.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('Cancel API Response:', cancelResponse.data);

    if (cancelResponse.status === 200 || cancelResponse.status === 202) {
      // Update local database statuses
      await Promise.all([
        WP_SUBMISSION_STATUS.update(
          {
            SubmissionStatus: 'Cancelled',
            DateTimeUpdated: moment().format('YYYY-MM-DD HH:mm:ss')
          },
          { 
            where: { 
              DocNum: id,
              UUID: uuid 
            } 
          }
        ),
        WP_INBOUND_STATUS.update(
          {
            status: 'Cancelled',
            dateTimeReceived: moment().format('YYYY-MM-DD HH:mm:ss')
          },
          { 
            where: { uuid } 
          }
        )
      ]);

      return res.json({
        success: true,
        message: 'Invoice cancelled successfully'
      });
    }

    throw new Error('Unexpected response from cancellation API');

  } catch (error) {
    console.error('Error cancelling invoice:', error);

    // Handle specific error cases
    if (error.response) {
      const errorData = error.response.data;
      
      // Check if document is already cancelled
      if (errorData?.error?.code === 'ValidationError' && 
          errorData?.error?.details?.some(d => d.message?.includes('already cancelled'))) {
        
        // Update local status
        await Promise.all([
          WP_SUBMISSION_STATUS.update(
            { SubmissionStatus: 'Cancelled' },
            { where: { DocNum: id, UUID: uuid } }
          ),
          WP_INBOUND_STATUS.update(
            { status: 'Cancelled' },
            { where: { uuid } }
          )
        ]);

        return res.json({
          success: true,
          message: 'Document was already cancelled'
        });
      }

      // Handle other API errors
      return res.status(error.response.status).json({
        success: false,
        message: 'Failed to cancel invoice',
        error: errorData?.error?.message || error.message
      });
    }

    // Log the error
    await WP_LOGS.create({
      Description: `Failed to cancel invoice ${id}: ${error.message}`,
      CreateTS: new Date(),
      LoggedUser: loggedUser || 'System'
    });

    return res.status(500).json({
      success: false,
      message: 'Failed to cancel invoice',
      error: error.message
    });
  }
});

// Add these new API endpoints

// Get outbound count
app.get('/api/outbound-files/count', async (req, res) => {
  try {
    const { period } = req.query;
    let whereClause = {};
    
    if (period) {
      const now = new Date();
      switch (period) {
        case 'today':
          whereClause.CreateTS = {
            [Op.gte]: new Date(now.setHours(0, 0, 0, 0))
          };
          break;
        case 'this-month':
          whereClause.CreateTS = {
            [Op.gte]: new Date(now.getFullYear(), now.getMonth(), 1)
          };
          break;
        case 'this-year':
          whereClause.CreateTS = {
            [Op.gte]: new Date(now.getFullYear(), 0, 1)
          };
          break;
      }
    }

    const count = await WP_SUBMISSION_STATUS.count({ where: whereClause });
    res.json({ count });
  } catch (error) {
    console.error('Error getting outbound count:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get inbound count
app.get('/api/inbound-status/count', async (req, res) => {
  try {
    const { period } = req.query;
    let whereClause = {};
    
    if (period) {
      const now = new Date();
      switch (period) {
        case 'today':
          whereClause.dateTimeReceived = {
            [Op.gte]: new Date(now.setHours(0, 0, 0, 0))
          };
          break;
        case 'this-month':
          whereClause.dateTimeReceived = {
            [Op.gte]: new Date(now.getFullYear(), now.getMonth(), 1)
          };
          break;
        case 'this-year':
          whereClause.dateTimeReceived = {
            [Op.gte]: new Date(now.getFullYear(), 0, 1)
          };
          break;
      }
    }

    const count = await WP_INBOUND_STATUS.count({ where: whereClause });
    res.json({ count });
  } catch (error) {
    console.error('Error getting inbound count:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/dashboard/graph-data', async (req, res) => {
  try {
    const { type, period } = req.query;
    
    // Initialize empty data structure that matches exactly what your frontend expects
    const defaultData = {
      sentToLHDN: new Array(7).fill(0),
      valid: new Array(7).fill(0),
      invalid: new Array(7).fill(0),
      rejected: new Array(7).fill(0),
      cancelled: new Array(7).fill(0),
      failedToSend: new Array(7).fill(0)
    };

    // Calculate date range for this week
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    try {
      // Query using Sequelize model
      const results = await WP_SUBMISSION_STATUS.findAll({
        attributes: [
          [db.sequelize.literal('DATEPART(WEEKDAY, DateTimeSent) - 1'), 'dayOfWeek'],
          'SubmissionStatus',
          [db.sequelize.fn('COUNT', '*'), 'count']
        ],
        where: {
          DateTimeSent: {
            [Op.gte]: startOfWeek,
            [Op.lte]: now
          }
        },
        group: [
          db.sequelize.literal('DATEPART(WEEKDAY, DateTimeSent)'),
          'SubmissionStatus'
        ],
        raw: true
      });

      // Map the results to your data structure
      results.forEach(row => {
        const dayIndex = row.dayOfWeek;
        const status = (row.SubmissionStatus || '').toLowerCase();
        const count = parseInt(row.count) || 0;

        switch(status) {
          case 'sent to lhdn':
          case 'Submitted':
            defaultData.sentToLHDN[dayIndex] = count;
            break;
          case 'Valid':
            defaultData.valid[dayIndex] = count;
            break;
          case 'Invalid':
            defaultData.invalid[dayIndex] = count;
            break;
          case 'Rejected':
            defaultData.rejected[dayIndex] = count;
            break;
          case 'Cancelled':
            defaultData.cancelled[dayIndex] = count;
            break;
          case 'failed':
          case 'failed to send':
            defaultData.failedToSend[dayIndex] = count;
            break;
        }
      });

      // Return the data
      res.json(defaultData);

    } catch (dbError) {
      console.error('Database error:', dbError);
      // On database error, return empty data structure
      res.json(defaultData);
    }

  } catch (error) {
    console.error('Error in graph data endpoint:', error);
    // Return empty data structure on error
    res.json({
      sentToLHDN: new Array(7).fill(0),
      valid: new Array(7).fill(0),
      invalid: new Array(7).fill(0),
      rejected: new Array(7).fill(0),
      cancelled: new Array(7).fill(0),
      failedToSend: new Array(7).fill(0)
    });
  }
});

// Update your routes to use render
app.get('/audit-trail', (req, res) => {
  res.render('dashboard/audit-trail', {
    title: 'Audit Trail',
    user: req.session.user || null
  });
});

app.get('/company-settings', (req, res) => {
  res.render('dashboard/company-settings', {
    title: 'Company Settings',
    user: req.session.user || null
  });
});

// Add similar routes for other pages

// Add this route
app.get('/outbound-bqe', (req, res) => {
  res.render('dashboard/outbound-bqe', {
    title: 'Outbound Invoices',
    user: req.session.user || null
  });
});

// Add the BQE routes
app.use('/bqe', bqeRoutes);

// CALLBACK
app.get('/callback', async (req, res) => {
  try {  
        console.log('Callback received:', {
            code: req.query.code ? 'present' : 'missing',
            state: req.query.state,
            error: req.query.error
        });

        if (!req.query.code) {
            return res.redirect('/outbound?auth=error&message=no_code');
        }

        const authManager = new AuthManager();
        const config = GeneralMethods.GetConfig();
        
        // Verify state parameter
        if (!authManager.IsValidState(req, res, req.query.state)) {
            return res.redirect('/outbound?auth=error&message=invalid_state');
        }

        // Exchange code for tokens
        authManager.Authorize(req.query.code, (status, response) => {
            if (status === Result.Success && response) {
                try {
                    // Save auth response
                    authManager.SaveAuthResponse(response);
                    
                    // Create JWT manager with config
                    const jwtManager = new JWTManager(config, response.id_token);
                    const decodedJWT = jwtManager.DecodeJWT();
                    
                    jwtManager.ValidateJWT(decodedJWT, (isValid) => {
                        if (isValid) {
                            res.redirect('/outbound?auth=success');
          } else {
                            console.error('Invalid JWT token');
                            res.redirect('/outbound?auth=error&message=invalid_token');
                        }
                    });
                } catch (error) {
                    console.error('Error processing auth response:', error);
                    res.redirect('/outbound?auth=error&message=processing_error');
          }
      } else {
                console.error('Authorization failed:', response?.body);
                res.redirect('/outbound?auth=error&message=auth_failed');
      }
        });
  } catch (error) {        
        console.error('Callback Error:', error);
        res.redirect('/outbound?auth=error&message=' + encodeURIComponent(error.message));
  }    
});


// Add this middleware to handle session debugging (optional)
app.use((req, res, next) => {
    if (process.env.NODE_ENV !== 'production') {
        console.log('Session data:', {
            state: req.session.state,
            sessionID: req.sessionID
        });
    }
    next();
});

// Add error handling middleware for BQE routes
app.use((err, req, res, next) => {
    if (err.name === 'BQEAuthError') {
        console.error('BQE Authentication Error:', err);
        return res.redirect('/outbound-bqe?auth=error');
    }
    next(err);
});

// Add these new endpoints to update the configuration
app.put('/api/invoice-config/project-title', async (req, res) => {
  try {
    const { projectTitle } = req.body;
    if (!projectTitle) {
      return res.status(400).json({ success: false, message: 'Project title is required' });
    }
    invoiceConfig.projectTitle = projectTitle;
    res.json({ success: true, message: 'Project title updated' });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.put('/api/invoice-config/payment-details', async (req, res) => {
  try {
    const { paymentTitle, paymentDetails, defaultAttention } = req.body;
    if (paymentTitle) invoiceConfig.paymentTitle = paymentTitle;
    if (paymentDetails) invoiceConfig.paymentDetails = paymentDetails;
    if (defaultAttention) invoiceConfig.defaultAttention = defaultAttention;
    res.json({ success: true, message: 'Settings updated successfully' });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get current configuration
app.get('/api/invoice-config', (req, res) => {
  res.json({ success: true, data: invoiceConfig });
});

// Add this function to read and convert image to base64
const getBase64Image = (imagePath) => {
  try {
    const imageBuffer = fs.readFileSync(path.join(__dirname, 'public', imagePath));
    return imageBuffer.toString('base64');
  } catch (error) {
    console.error('Error reading logo file:', error);
    return null;
  }
};

const multer = require('multer');
const storage = multer.memoryStorage(); // Use memory storage instead of disk

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Not an image file'));
    }
  }
});

// Update logo upload endpoint with optimization
app.post('/api/invoice-config/logo', upload.single('logo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Validate file type
    if (!req.file.mimetype.startsWith('image/')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid file type. Please upload an image.'
      });
    }

    // Generate unique filename with original extension
    const fileExt = req.file.mimetype.includes('png') ? '.png' : '.jpg';
    const filename = `company-logo-${Date.now()}${fileExt}`;
    const uploadDir = path.join(__dirname, 'public', 'uploads', 'logos');
    const filePath = path.join(uploadDir, filename);

    // Ensure upload directory exists with error handling
    try {
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
    } catch (fsError) {
      console.error('Directory creation error:', fsError);
      return res.status(500).json({
        success: false,
        message: 'Failed to create upload directory',
        error: fsError.message
      });
    }

    // Save optimized image with error handling
    try {
      fs.writeFileSync(filePath, optimizedImageBuffer);
    } catch (writeError) {
      console.error('File write error:', writeError);
      return res.status(500).json({
        success: false,
        message: 'Failed to save image',
        error: writeError.message
      });
    }

    // Update config
    invoiceConfig.logoUrl = `/uploads/logos/${filename}`;
    invoiceConfig.logoBase64 = optimizedImageBuffer.toString('base64');

    // Return success with optimized image info
    res.json({
      success: true,
      message: 'Logo uploaded and optimized successfully',
      logoUrl: invoiceConfig.logoUrl,
      size: optimizedImageBuffer.length,
      dimensions: {
        width: 150,
        height: 50
      }
    });

  } catch (error) {
    console.error('Logo upload error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'An unexpected error occurred',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});
