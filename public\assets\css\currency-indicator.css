/* Currency Indicator Styles */
:root {
  --currency-primary: #0d6efd;
  --currency-secondary: #6c757d;
  --currency-success: #198754;
  --currency-danger: #dc3545;
  --currency-warning: #ffc107;
  --currency-info: #0dcaf0;
  --currency-light: #f8f9fa;
  --currency-dark: #212529;
  --currency-white: #fff;
  --currency-shadow: rgba(0, 0, 0, 0.15);
}

/* Currency Badge */
.currency-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  color: var(--currency-white);
  background-color: var(--currency-primary);
  border-radius: 0.25rem;
  margin-left: 0.5rem;
  box-shadow: 0 2px 4px var(--currency-shadow);
}

.currency-badge-secondary {
  background-color: var(--currency-secondary);
}

.currency-badge-success {
  background-color: var(--currency-success);
}

.currency-badge-danger {
  background-color: var(--currency-danger);
}

.currency-badge-warning {
  background-color: var(--currency-warning);
  color: var(--currency-dark);
}

.currency-badge-info {
  background-color: var(--currency-info);
  color: var(--currency-dark);
}

/* Currency Value Display */
.currency-value {
  position: relative;
  display: inline-block;
}

.currency-value-foreign {
  color: var(--currency-primary);
  font-weight: 600;
}

.currency-value-myr {
  display: block;
  font-size: 0.85em;
  color: var(--currency-secondary);
  margin-top: 0.25rem;
}

.currency-value-myr::before {
  content: "MYR: ";
  font-weight: 600;
}

/* Currency Exchange Rate Indicator */
.exchange-rate-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  background-color: var(--currency-light);
  border: 1px solid var(--currency-secondary);
  border-radius: 0.25rem;
  margin-left: 0.5rem;
}

.exchange-rate-indicator i {
  margin-right: 0.25rem;
  color: var(--currency-primary);
}

/* Currency Tooltip */
.currency-tooltip {
  position: relative;
  display: inline-block;
  cursor: help;
}

.currency-tooltip .currency-tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--currency-dark);
  color: var(--currency-white);
  text-align: center;
  border-radius: 6px;
  padding: 0.5rem;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  box-shadow: 0 4px 8px var(--currency-shadow);
}

.currency-tooltip .currency-tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--currency-dark) transparent transparent transparent;
}

.currency-tooltip:hover .currency-tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Currency Table Cell */
.currency-cell {
  position: relative;
}

.currency-cell-foreign {
  color: var(--currency-primary);
  font-weight: 600;
}

.currency-cell-myr {
  display: block;
  font-size: 0.85em;
  color: var(--currency-secondary);
}

/* Currency Header */
.currency-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--currency-light);
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

.currency-header-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.currency-header-info {
  display: flex;
  align-items: center;
}

/* Enhanced Error Modal Styles */
.enhanced-error-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.enhanced-error-modal.show {
  opacity: 1;
  visibility: visible;
}

.enhanced-error-dialog {
  width: 100%;
  max-width: 500px;
  background-color: var(--currency-white);
  border-radius: 12px;
  box-shadow: 0 10px 25px var(--currency-shadow);
  overflow: hidden;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.enhanced-error-modal.show .enhanced-error-dialog {
  transform: translateY(0);
}

.enhanced-error-header {
  padding: 1.5rem;
  background-color: #f8d7da;
  text-align: center;
  position: relative;
}

.enhanced-error-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background-color: var(--currency-danger);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.enhanced-error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #721c24;
  margin: 0;
}

.enhanced-error-subtitle {
  font-size: 1rem;
  color: var(--currency-secondary);
  margin: 0.5rem 0 0;
}

.enhanced-error-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: var(--currency-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.enhanced-error-body {
  padding: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.enhanced-error-message {
  background-color: var(--currency-light);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid var(--currency-danger);
}

.enhanced-error-code {
  font-family: monospace;
  background-color: #f1f1f1;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  overflow-x: auto;
  font-size: 0.9rem;
}

.enhanced-error-footer {
  padding: 1rem 1.5rem;
  background-color: var(--currency-light);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
  display: flex;
  justify-content: flex-end;
}

.enhanced-error-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.enhanced-error-btn-primary {
  background-color: var(--currency-primary);
  color: white;
}

.enhanced-error-btn-primary:hover {
  background-color: #0b5ed7;
}
