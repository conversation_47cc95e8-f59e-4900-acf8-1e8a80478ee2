<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
      body {
            background: #f8f9fa;
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            -webkit-font-smoothing: antialiased;
            color: #444444;
            margin: 0;
            padding: 0.10rem;
            background-color: #ffffff;
            font-size: 12px;
            line-height: 1.2;
        }
        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 5px 0;
            background-color: #ffffff;
            position: relative;
            margin-bottom: -15px;
        }

        /* Modified Company Info styles */
        .company-info {
            width: 100%;
            margin-bottom: 15px;
            /* Reduced from 30px */
        }

        .company-info p {
            margin: 1px 0;
        }

        .company-info img {
            max-width: 150px;
            margin-bottom: 5px;
            height: auto;
        }

        .company-name {
            font-weight: bold;
        }

        .email-link {
            color: #000;
            text-decoration: underline;
        }

        /* Modified Invoice Header styles */
        .invoice-header {
            position: relative;
            text-align: right;
            margin-top: 0;
            /* Remove top margin */
        }

        .invoice-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
        }

        .invoice-details {
            text-align: right;

        }

        .invoice-details p {
            margin: 1px 0;
        }

        /* UUID Section */
        .uuid-section {
            position: absolute;
            top: 10px;
            right: 10px;
            text-align: right;
            font-size: 10px;
        }

        .main-content {
            clear: both;
            margin-top: 20px;
        }

        .info-row {
            margin: 1px 0;
        }

        /* Buyer section specific styling */
        .buyer-section {
            margin-top: 15px;
        }

        .buyer-section .info-row {
            margin: 1px 0;
        }

        .two-column {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            /* Reduced from 30px */
        }

        .column {
            width: 45%;
        }

        .info-row {
            margin: 1px 0;
        }

        .info-label {
            font-weight: bold;
        }
        p {
            white-space: normal;
        }

        /* Dashed border */
        hr.dashed {
            border-top: 3px dashed #bbb;
        }

        /* Dotted border */
        hr.dotted {
            border-top: 3px dotted #bbb;
        }

        /* Solid border */
        hr.solid {
            border-top: 1px solid #444444;
        }

        /* Rounded border */
        hr.rounded {
            border-top: 8px solid #bbb;
            border-radius: 5px;
        }

         /* Table Styles */
         .project-table {
            width: 100%;
            border-collapse: collapse;
            margin: 5px 0;
            font-size: 10px;
        }

        .project-table th,
        .project-table td {
            border: 1px solid #444444;
            padding: 4px;
            text-align: left;
        }

        /* Table Styles */
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10px;
        }

        .details-table th,
        .details-table td {
            border: 1px solid #444444;
            padding: 4px;
            text-align: left;
        }

        .details-table th {
            background-color: #444444;
            color: #fff;
            font-weight: bold;
            font-size: 10px;
            white-space: no-wrap;
            word-wrap: break-word;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        /* Tax Summary Table */
        .tax-summary-table {
            width: auto;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10px;
        }

        .tax-summary-table th,
        .tax-summary-table td {
            border: 1px solid #444444;
            padding: 4px 8px;
            text-align: left;
            min-width: 80px;
        }

        .tax-summary-table th {
            background-color: #444444;
            color: #fff;
            font-weight: bold;
        }

        /* Currency Section */
        .currency-section {
            margin: 15px 0;
            width: 250px;
        }

        .currency-section table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }

        .currency-section td {
            padding: 4px;
            border: 1px solid #444444;
        }

           /* Currency Section */
           .project-section {
            margin: 15px 0;
            width: 250px;
        }

        .project-section table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }

        .project-section td {

            padding: 4px;
            border: 1px solid #444444;
        }
        .project-section th {
            background-color: #444444;
            color: #fff;
            font-weight: bold;
            font-size: 10px;
            white-space: nowrap;
        }

        /* Currency and Project Title Container */
        .currency-project-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 15px 0;
            width: 100%;
        }

        .currency-project-container .currency-section,
        .currency-project-container .project-section {
            margin: 0;
            width: 48%;
        }

        /* Payment and Totals Container */
        .payment-totals-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 15px 0;
            width: 100%;
        }

        .payment-totals-container .payment-info {
            width: 48%;
            margin: 0;
        }

        .payment-totals-container .totals-table {
            width: 48%;
            margin: 0;
        }

        /* Totals Section */
        .totals-table {
            width: 250px;
            margin-left: auto;
            border-collapse: collapse;
            font-size: 10px;
        }

        .totals-table tr td {
            padding: 4px 8px;
            border: 1px solid #000;
        }

        .totals-table tr:last-child {
            font-weight: bold;
        }

        .total-label {
            text-align: right;
            white-space: nowrap;
            font-weight: bold;
        }

        .total-amount {
            text-align: right;
            width: 100px;
            background-color: #f9f9f9;
        }

        .total-amount.final {
            background-color: #e9e9e9;
        }

        /* Footer */
        .footer {
            border-top: 1px solid #000;
            padding-top: 5px;
            position: relative;
            min-height: 100px;
            font-size: 10px;
            margin-bottom: 5px;
        }

        .signature-block {
            width: calc(100% - 120px);
        }

        .signature-block p {
            margin: 1px 0;
        }

        .signature-title {
            font-weight: bold;
        }

        .qr-code {
            position: relative;
            /* Changed from fixed to relative */
            float: right;
            /* Float right to align with right column */
            width: 100px;
            height: 100px;
        }

        .qr-container {
            clear: right;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .line-items-note {
            font-size: 9px;
            margin-top: 5px;
            font-style: italic;
        }

        .url-footer {
            font-size: 8px;
            text-align: center;
            color: #666;

            margin-top: 5px;
        }

          /* Payment Info */
          .payment-info {
            font-size: 10px;
            width: 100%;
            page-break-inside: avoid;
        }

        .payment-info p {
            margin: 5px 0;
        }

        .invoice-details-eInvoice{
            margin-top: 10px;
            text-align: right;
            font-size: 12px;
            gap: 10px;
        }
        .invoice-details-eInvoice p{
            margin: 1px 0;
        }

    </style>
</head>

<body>
    <div class="container">
        <div class="two-column">
            <!-- Left Column: Company Info -->
            <div class="column">
                <div class="company-info">
                    {{if CompanyLogo}}
                    <img src="{{:CompanyLogo}}" alt="HSS Engineering Sdn Bhd Logo" style="max-width: 100px; height: auto;">
                    {{/if}}
                    <p class="company-name">{{:companyName}}</p>
                    <p>{{:SupplierTIN}}</p>
                    <p>{{:SupplierIdType}}/{{:SupplierIdNumber}}</p>
                    <p>{{:SupplierSSTID}}</p>
                    <p>{{:companyAddress}}</p>
                    <p>{{:companyPhone}}</p>
                    <p><a href="mailto:{{:companyEmail}}" class="email-link">{{:companyEmail}}</a></p>
                    <p>{{:SupplierMSICCode}}</p>
                    <p>{{:SupplierBusinessActivity}}</p>
                </div>

                <!-- Buyer Section moved inside left column -->
                <div class="buyer-section">
                    <div class="info-row"><span class="info-label">TO:</span></div>
                    <div class="info-row"><span class="info-label">Name:</span> {{:BuyerName}}</div>
                    <div class="info-row"><span class="info-label">Tax Identification Number (TIN):</span> {{:BuyerTIN}}
                    </div>
                    <div class="info-row"><span class="info-label">ID Type/Number:</span>
                        {{:BuyerIdType}}/{{:BuyerIdNumber}}</div>
                    <div class="info-row"><span class="info-label">SST Registration Number:</span> {{:BuyerSSTID}}
                    </div>
                    <div class="info-row"><span class="info-label">Address:</span> {{:BuyerAddress}}</div>
                    <div class="info-row"><span class="info-label">Telephone Number:</span> {{:BuyerPhone}}</div>
                    <div class="info-row"><span class="info-label">Email:</span> <a href="mailto:{{:companyEmail}}"
                            class="email-link">{{:BuyerEmail}}</a></div>
                    <div class="info-row">
                        <span class="info-label">Attention:</span>
                        {{if BuyerAttention && BuyerAttention !== 'NA'}}
                            {{:BuyerAttention}}
                        {{else}}
                            {{:BuyerName}}
                        {{/if}}
                    </div>
                    <!-- <div class="info-row"><span class="info-label">MSIC Code:</span> {{:BuyerMSICCode}}</div>
                    <div class="info-row"><span class="info-label">Business Activity:</span> {{:BuyerBusinessActivity}} -->

                </div>
            </div>

            <!-- Right Column: Invoice Header -->
            <div class="column">
                <div class="invoice-header">
                    <!-- <div class="invoice-title">{{:InvoiceTypeName}} {{:InvoiceVersionCode}}</div> -->
                    <div class="invoice-title">
                        E-INVOICE
                        {{if isForeignCurrency}}
                        <span style="font-size: 12px; color: #666; margin-left: 5px;">[{{:currencyIndicator}}]</span>
                        {{else}}
                        <span style="font-size: 12px; color: #666; margin-left: 5px;">[MYR]</span>
                        {{/if}}
                    </div>
                    <div class="qr-container">
                        {{if qrCode}}
                        <img src="{{:qrCode}}" alt="QR Code" class="qr-code">
                        {{/if}}

                    </div>
                    <div class="invoice-details">
                        <p><span class="info-label">UUID:</span> {{:UniqueIdentifier}}</p>
                        <!-- <p><span class="info-label">LONG ID:</span> {{:LHDNlongId}}
                        </p> -->
                    </div>

                    <div class="invoice-details-eInvoice">
                        <p><span class="info-label">e-Invoice No:</span> {{:internalId}}</p>
                        <p><span class="info-label">e-Invoice Type:</span> {{:InvoiceTypeCode}}</p>
                        <p><span class="info-label">e-Invoice Version:</span> {{:InvoiceVersionCode}}</p>
                        <p><span class="info-label">e-Invoice Issue Date:</span> {{:issueDate}}</p>
                        <p><span class="info-label">e-Invoice Issue Time:</span> {{:issueTime}}</p>
                    </div>
                    <div class="invoice-details" style="margin-top: 10px;">
                         <p><span class="info-label">Original Invoice Ref. No.:</span> {{:OriginalInvoiceRef}}</p>
                         <p><span class="info-label">Original Invoice Date:</span> {{:OriginalInvoiceStartDate}}</p>
                         <!-- <p><span class="info-label">Original Invoice End Date No:</span> {{:OriginalInvoiceEndDate}}</p> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Table -->
        <hr class="solid">
        <table class="details-table">
            <tr>
                <th>No.</th>
                <th>Code</th>
                <th>Description</th>
                <th>Quantity</th>
                <th>UOM</th>
                <th>Unit Price ({{:documentCurrency}})</th>
                <th>Subtotal ({{:documentCurrency}})</th>
                <th>(A) Discount</th>
                <th>(B) Fee / Charges</th>
                <th>Tax Rate</th>
                <th style="text-align: center;">Tax Amount ({{:documentCurrency}})</th>
                <th style="text-align: center;">Amount Exempted from Tax ({{:documentCurrency}})</th>
                <th style="text-align: center;">Amount ({{:documentCurrency}})</th>
            </tr>
            {{for items}}
            <tr>
                <td class="text-center">{{:No}}</td>
                <td class="text-center">{{:Cls}}</td>
                <td>{{:Description}}</td>
                <td class="text-right">{{:Quantity}}</td>
                <td class="text-right">{{:UOM}}</td>
                <td class="text-right">{{:UnitPrice}}</td>
                <td class="text-right">{{:QtyAmount}}</td>
                <td class="text-right">{{if Disc === '0.00'}}{{:Disc}}{{else}} {{:Disc}}{{/if}}</td>
                <td class="text-right">{{:Charges}}</td>
                <td class="text-center">{{if TaxType === "Not Applicable"}}0%{{else}}{{:LineTaxPercent}}%{{/if}}</td>
                <td class="text-right">{{if TaxType === "Not Applicable"}}0.00{{else}}{{:LineTaxAmount}}{{/if}}</td>
                <td class="text-right">{{if TaxType === "Tax exemption"}}{{:HypotheticalTax}}*{{else}}0.00{{/if}}</td>
                <td class="text-right">{{:Total}}</td>
            </tr>
            {{/for}}
        </table>
        <div class="line-items-note">
            Note: Classification Code, UOM - Unit of Measurement, ( ) - Additional data present, * Exempted
            from Tax shows the tax amount that would have been charged at {{:TaxExchangeRate}}% if the item was not tax exempt.
            {{if isForeignCurrency}}
            <p style="color: #666; font-weight: bold;">FOREIGN CURRENCY INVOICE: All amounts are in {{:currencyIndicator}}. Exchange Rate: 1 {{:currencyIndicator}} = {{:TaxExchangeRate}} MYR. Tax amounts are calculated in MYR as per LHDN requirements.</p>
            {{/if}}
        </div>

        <!-- Currency and Project Title Section -->
        {{if projectTitle && projectTitle !== 'NA'}}
        <div class="currency-project-container">
            <!-- Currency Section -->
            <div class="currency-section">
                <table>
                    <tr>
                        <td>Currency Code</td>
                        <td>{{:documentCurrency}}</td>
                    </tr>
                    <tr>
                        <td>Exchange Rate</td>
                        <td>{{:TaxExchangeRate}}</td>
                    </tr>
                </table>
            </div>

            <!-- Project Title Table -->
            <div class="project-section">
                <table>
                    <tr>
                        <th style="text-align: center;">Project Title</th>
                    </tr>
                    <tr>
                        <td style="text-align: center;">{{:projectTitle}}</td>
                    </tr>
                </table>
            </div>
        </div>
        {{else}}
        <!-- Currency Section Only (when project title is NA) -->
        <div class="currency-section" style="width: 250px; margin: 15px 0;">
            <table>
                <tr>
                    <td>Currency Code</td>
                    <td>{{:documentCurrency}}</td>
                </tr>
                <tr>
                    <td>Exchange Rate</td>
                    <td>{{:TaxExchangeRate}}</td>
                </tr>
            </table>
        </div>
        {{/if}}

        <!-- Tax Summary Table -->
        <table class="details-table" style="margin-top: 20px;">
            <tr>
                <th style="text-align: center;">Tax Type</th>
                <th style="text-align: center;">Tax Rate</th>
                <th style="text-align: right;">Total Amount ({{:documentCurrency}})</th>
                <th style="text-align: right;">Total Tax Amount Per Tax Type ({{:documentCurrency}})</th>
                <th style="text-align: center;">Total Amount Exempted from Tax ({{:documentCurrency}})</th>
                <th style="text-align: center;">Details of Tax Exemption</th>
            </tr>
            {{for taxSummary}}
            <tr>
                <td style="text-align: center;">{{:taxType}}</td>
                <td style="text-align: center;">{{if taxType === "06" || taxType === "Not Applicable"}}0%{{else}}{{:taxRate}}%{{/if}}</td>
                <td style="text-align: right;">{{if taxType === "06" || taxType === "Not Applicable"}}0.00{{else}}{{:totalAmount}}{{/if}}</td>
                <td style="text-align: right;">{{if taxType === "06" || taxType === "Not Applicable"}}0.00{{else}}{{:totalTaxAmount}}{{/if}}</td>
                <td style="text-align: right;">{{if taxType === "Tax exemption"}}{{:hypotheticalTaxAmount}}*{{else}}0.00{{/if}}</td>
                <td style="text-align: center;">{{:LHDNtaxExemptionReason}}</td>
            </tr>
            {{/for}}
        </table>

        <!-- Check if we have any valid payment information -->
        {{if paymentAccount !== 'NA' || paymentDetails !== 'NA' || (paymentInfo && paymentInfo.length > 0)}}
        <!-- Payment and Totals Container (when payment info exists) -->
        <div class="payment-totals-container">
            <!-- Payment Info -->
            <div class="payment-info">
                <div>
                    <p><strong>Please remit the payment to:</strong></p>
                    {{if paymentAccount && paymentAccount !== 'NA'}}
                        <p>{{:paymentAccount}}</p>
                    {{/if}}
                    {{if paymentDetails && paymentDetails !== 'NA'}}
                        <p>{{:paymentDetails}}</p>
                    {{/if}}
                    {{if paymentInfo && paymentInfo.length > 0}}
                        {{for paymentInfo}}
                            <p>{{:}}</p>
                        {{/for}}
                    {{/if}}
                </div>
            </div>

            <!-- Totals Table -->
            <table class="totals-table">
                <tr>
                    <td class="total-label">Total Tax Amount ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalTaxAmount}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total Net Amount ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalNetAmount}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total excluding Tax ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalExcludingTax}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total including Tax ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalIncludingTax}}</td>
                </tr>
                <tr>
                    <td class="total-label">Prepayment Amount ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:Prepayment}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total Payable Amount ({{:documentCurrency}})</td>
                    <td class="total-amount final">{{:TotalPayableAmount}}</td>
                </tr>
            </table>
        </div>
        {{else}}
        <!-- Totals Only (when all payment info is NA) -->
        <div style="display: flex; justify-content: flex-end; margin: 15px 0;">
            <table class="totals-table">
                <tr>
                    <td class="total-label">Total Tax Amount ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalTaxAmount}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total Net Amount ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalNetAmount}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total excluding Tax ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalExcludingTax}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total including Tax ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:TotalIncludingTax}}</td>
                </tr>
                <tr>
                    <td class="total-label">Prepayment Amount ({{:documentCurrency}})</td>
                    <td class="total-amount">{{:Prepayment}}</td>
                </tr>
                <tr>
                    <td class="total-label">Total Payable Amount ({{:documentCurrency}})</td>
                    <td class="total-amount final">{{:TotalPayableAmount}}</td>
                </tr>
            </table>
        </div>
        {{/if}}

        <!-- Footer -->
        <div class="footer">
            <div class="signature-block">
                <p><span class="signature-title">Signed by:</span> {{:signedBy}}</p>
                <p><span class="signature-title">Digital Signature:</span> {{:DigitalSignature}}</p>
                <p><span class="signature-title">Date and Time of Validation:</span> {{:validationDateTime}}</p>
            </div>
            <div class="url-footer">
                This document is a visual presentation of the e-Invoice.
                This document was automatically generated by Pinnacle e-Invoice Portal from Lembaga Hasil Dalam Negeri
                Malaysia (LHDNM).
                <br>
                {{:lhdnLink}}
            </div>
        </div>
    </div>
</body>

</html>