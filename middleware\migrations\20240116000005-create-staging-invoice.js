'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('staging_invoices', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      bqe_invoice_id: {
        type: Sequelize.STRING(255),
        unique: true,
        allowNull: false
      },
      invoice_number: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'Pending'
      },
      date_submitted: {
        type: Sequelize.DATE,
        allowNull: true
      },
      date_sync: {
        type: Sequelize.DATE,
        allowNull: true
      },
      date_cancelled: {
        type: Sequelize.DATE,
        allowNull: true
      },
      cancelled_by: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      cancellation_reason: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('staging_invoices');
  }
}; 