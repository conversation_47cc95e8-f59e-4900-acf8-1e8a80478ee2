'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_COMPANY_SETTINGS', {
      ID: { 
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      CompanyImage: Sequelize.STRING,
      CompanyName: Sequelize.STRING,
      Industry: Sequelize.STRING,
      Country: Sequelize.STRING,
      TIN: Sequelize.STRING,
      BRN: Sequelize.STRING,
      About: Sequelize.TEXT,
      Address: Sequelize.STRING,
      Phone: Sequelize.STRING,
      Email: Sequelize.STRING,
      ValidStatus: Sequelize.STRING,
      UserID: Sequelize.STRING
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_COMPANY_SETTINGS');
  }
}; 