'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_LOGS', {
      ID: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      Description: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      CreateTS: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      LoggedUser: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      IPAddress: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      LogType: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'INFO'
      },
      Module: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      Action: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      Status: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      UserID: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'WP_USER_REGISTRATION',
          key: 'ID'
        }
      }
    });

    // Add indexes for common search fields
    await queryInterface.addIndex('WP_LOGS', ['CreateTS']);
    await queryInterface.addIndex('WP_LOGS', ['LogType']);
    await queryInterface.addIndex('WP_LOGS', ['Module']);
    await queryInterface.addIndex('WP_LOGS', ['Status']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_LOGS');
  }
}; 