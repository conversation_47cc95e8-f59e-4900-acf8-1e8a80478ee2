'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('staging_invoices', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      bqe_invoice_id: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      invoice_number: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      invoice_date: {
        type: Sequelize.DATE,
        allowNull: false
      },
      due_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      customer_name: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      customer_tin: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      customer_brn: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      customer_vat: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      customer_address: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      total_amount: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: false
      },
      vat_amount: {
        type: Sequelize.DECIMAL(18, 2),
        allowNull: true,
        defaultValue: 0.00
      },
      currency: {
        type: Sequelize.STRING(10),
        allowNull: false,
        defaultValue: 'MUR'
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'PENDING'
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      submission_id: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      submission_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WP_USER_REGISTRATION',
          key: 'ID'
        }
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });

    // Add indexes for common search fields
    await queryInterface.addIndex('staging_invoices', ['invoice_number']);
    await queryInterface.addIndex('staging_invoices', ['bqe_invoice_id']);
    await queryInterface.addIndex('staging_invoices', ['status']);
    await queryInterface.addIndex('staging_invoices', ['customer_tin']);
    await queryInterface.addIndex('staging_invoices', ['submission_id']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('staging_invoices');
  }
}; 