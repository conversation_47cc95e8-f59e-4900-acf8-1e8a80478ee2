module.exports = (sequelize, DataTypes) => {
  const EInvoiceSettings = sequelize.define('EInvoiceSettings', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'WP_USER_REGISTRATION',
        key: 'ID'
      }
    },
    apiEndpoint: {
      type: DataTypes.STRING,
      allowNull: true
    },
    apiKey: {
      type: DataTypes.STRING,
      allowNull: true
    },
    apiVersion: {
      type: DataTypes.STRING(20),
      defaultValue: 'v1'
    },
    defaultTemplate: {
      type: DataTypes.STRING(50),
      defaultValue: 'standard'
    },
    logoPosition: {
      type: DataTypes.STRING(20),
      defaultValue: 'top-left'
    },
    showQRCode: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    invoiceFormat: {
      type: DataTypes.STRING(100),
      defaultValue: 'INV-{YYYY}-{MM}-{0000}'
    },
    startingNumber: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    resetMonthly: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    defaultTaxRate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00
    },
    taxRegNumber: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    includeTax: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    tableName: 'einvoice_settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return EInvoiceSettings;
}; 