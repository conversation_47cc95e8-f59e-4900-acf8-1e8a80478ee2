document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM fully loaded and parsed');
  document.getElementById('loginForm').addEventListener('submit', async function(event) {
    event.preventDefault(); // Prevent the form from submitting the default way
    console.log('Form submission intercepted');

    const form = event.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    console.log('Form data:', data);

    try {
      const response = await fetch(form.action, {
        method: form.method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();
      console.log('Server response:', result);

      if (result.success) {
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: result.message
        }).then(() => {
          window.location.href = '/dashboard'; // Redirect to dashboard after alert is closed
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: result.message
        });
      }
    } catch (error) {
      console.error('Error:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'An error occurred while logging in the user.'
      });
    }
  });
});
