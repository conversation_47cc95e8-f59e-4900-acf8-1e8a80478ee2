const axios = require('axios');
const db = require('../models');
const WP_CONFIGURATION = db.WP_CONFIGURATION;

// DEPRECATED: Use BQE service getTokenAsTaxPayer instead
const generateAccessToken = async (req, userId = null) => {
  console.warn('[DEPRECATED] token.service.generateAccessToken is deprecated. Use BQE service getTokenAsTaxPayer instead.');

  if (!req) {
    throw new Error('Request context is required for token generation. Please use BQE service getTokenAsTaxPayer(req) instead.');
  }

  // Redirect to BQE service for consistent token generation
  const { getTokenAsTaxPayer } = require('./bqe/service');
  const tokenResponse = await getTokenAsTaxPayer(req);

  if (!tokenResponse?.access_token) {
    throw new Error('Failed to get authentication token from BQE service');
  }

  return tokenResponse.access_token;
};

// Helper function to generate token with specific credentials
const generateTokenWithCredentials = async (settings) => {
  try {
    const params = new URLSearchParams();
    params.append('grant_type', 'client_credentials');
    params.append('client_id', settings.clientId);
    params.append('client_secret', settings.clientSecret);
    params.append('scope', 'InvoicingAPI');

    // Ensure proper URL construction
    let baseUrl = settings.middlewareUrl || process.env.ID_SRV_BASE_URL;

    // Remove any trailing slashes
    baseUrl = baseUrl.replace(/\/+$/, '');

    // Ensure the URL starts with https://
    if (!baseUrl.startsWith('https://')) {
      baseUrl = 'https://' + baseUrl;
    }

    console.log('Using base URL:', baseUrl); // Debug log

    const response = await axios.post(`${baseUrl}/connect/token`, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    console.log('Token response:', response.data); // Debug log
    return response.data.access_token;
  } catch (error) {
    console.error('Token generation error details:', {
      error: error.message,
      response: error.response?.data,
      config: error.config
    });
    throw error;
  }
};

// Helper function to generate token with default credentials
const generateDefaultToken = async () => {
  try {
    const params = new URLSearchParams();
    params.append('grant_type', 'client_credentials');
    params.append('client_id', process.env.CLIENT_ID);
    params.append('client_secret', process.env.CLIENT_SECRET);
    params.append('scope', 'InvoicingAPI');

    // Ensure proper URL construction for default URL
    let baseUrl = process.env.ID_SRV_BASE_URL;

    // Remove any trailing slashes
    baseUrl = baseUrl.replace(/\/+$/, '');

    // Ensure the URL starts with https://
    if (!baseUrl.startsWith('https://')) {
      baseUrl = 'https://' + baseUrl;
    }

    console.log('Using default base URL:', baseUrl); // Debug log

    const response = await axios.post(`${baseUrl}/connect/token`, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    console.log('Default token response:', response.data); // Debug log
    return response.data.access_token;
  } catch (error) {
    console.error('Default token generation error details:', {
      error: error.message,
      response: error.response?.data,
      config: error.config
    });
    throw error;
  }
};

const checkTokenExpiry = (req) => {
  const accessToken = req.session.accessToken;
  const tokenExpiryTime = req.session.tokenExpiryTime;

  if (!accessToken || !tokenExpiryTime) {
    return false;
  }

  return Date.now() <= tokenExpiryTime;
};

// DEPRECATED: Use BQE service getTokenAsTaxPayer instead
const getAccessToken = async (req) => {
  console.warn('[DEPRECATED] token.service.getAccessToken is deprecated. Use BQE service getTokenAsTaxPayer instead.');

  if (!req) {
    throw new Error('Request context is required for token generation. Please use BQE service getTokenAsTaxPayer(req) instead.');
  }

  // Redirect to BQE service for consistent token generation
  const { getTokenAsTaxPayer } = require('./bqe/service');
  const tokenResponse = await getTokenAsTaxPayer(req);

  if (!tokenResponse?.access_token) {
    throw new Error('Failed to get authentication token from BQE service');
  }

  return tokenResponse.access_token;
};

// Function to test LHDN connection by generating a token
const generateToken = async (settings) => {
  try {
    const params = new URLSearchParams();
    params.append('grant_type', 'client_credentials');
    params.append('client_id', settings.clientId);
    params.append('client_secret', settings.clientSecret);
    params.append('scope', 'InvoicingAPI');

    // Ensure proper URL construction
    let baseUrl = settings.middlewareUrl || process.env.ID_SRV_BASE_URL;

    // Remove any trailing slashes
    baseUrl = baseUrl.replace(/\/+$/, '');

    // Ensure the URL starts with https://
    if (!baseUrl.startsWith('https://')) {
      baseUrl = 'https://' + baseUrl;
    }

    console.log('Testing connection with base URL:', baseUrl);

    const response = await axios.post(`${baseUrl}/connect/token`, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    return {
      success: true,
      accessToken: response.data.access_token,
      expiresIn: response.data.expires_in / 60 // Convert seconds to minutes
    };
  } catch (error) {
    console.error('Token generation error details:', {
      error: error.message,
      response: error.response?.data,
      config: error.config
    });
    return {
      success: false,
      error: error.response?.data?.error_description || error.message
    };
  }
};

// Function to validate LHDN credentials without generating a token
const validateCredentials = async (settings) => {
  try {
    const params = new URLSearchParams();
    params.append('grant_type', 'client_credentials');
    params.append('client_id', settings.clientId);
    params.append('client_secret', settings.clientSecret);
    params.append('scope', 'InvoicingAPI');

    // Ensure proper URL construction
    let baseUrl = settings.middlewareUrl || process.env.ID_SRV_BASE_URL;
    baseUrl = baseUrl.replace(/\/+$/, '');
    if (!baseUrl.startsWith('https://')) {
      baseUrl = 'https://' + baseUrl;
    }

    console.log('Validating credentials with base URL:', baseUrl);

    // Only validate credentials without storing the token
    const response = await axios.post(`${baseUrl}/connect/token`, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      validateStatus: status => status === 200 // Only consider 200 as success
    });

    return {
      success: true,
      expiresIn: response.data.expires_in / 60 // Convert seconds to minutes
    };
  } catch (error) {
    console.error('Credential validation error:', {
      error: error.message,
      response: error.response?.data,
      config: error.config
    });
    return {
      success: false,
      error: error.response?.data?.error_description || error.message
    };
  }
};

module.exports = {
  generateAccessToken,
  checkTokenExpiry,
  getAccessToken,
  generateToken,
  validateCredentials
};