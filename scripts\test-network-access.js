require('dotenv').config();
const { validateNetworkPath } = require('../config/paths');
const path = require('path');

async function testAccess() {
  console.log('Environment variables:');
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('API_BASE_URL:', process.env.API_BASE_URL);
  console.log('NETWORK_SHARE:', process.env.NETWORK_SHARE);
  console.log('PUPPETEER_CACHE_DIR:', process.env.PUPPETEER_CACHE_DIR);
  
  const networkPath = process.env.NETWORK_SHARE;
  console.log('\nTesting network access to:', networkPath);
  
  try {
    const isAccessible = await validateNetworkPath(networkPath);
    console.log('Network access test result:', isAccessible ? 'SUCCESS' : 'FAILED');
    
    if (!isAccessible) {
      console.error('Please check:');
      console.error('1. Network share path is correct in .env file');
      console.error('2. Network share is accessible');
      console.error('3. Current user has permissions to access the share');
    }
  } catch (error) {
    console.error('Network access test error:', error);
  }
}

testAccess().catch(console.error); 