'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('einvoice_settings', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WP_USER_REGISTRATION',
          key: 'ID'
        },
        unique: true
      },
      apiEndpoint: {
        type: Sequelize.STRING,
        allowNull: true
      },
      apiKey: {
        type: Sequelize.STRING,
        allowNull: true
      },
      apiVersion: {
        type: Sequelize.STRING(20),
        defaultValue: 'v1'
      },
      defaultTemplate: {
        type: Sequelize.STRING(50),
        defaultValue: 'standard'
      },
      logoPosition: {
        type: Sequelize.STRING(20),
        defaultValue: 'top-left'
      },
      showQRCode: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      invoiceFormat: {
        type: Sequelize.STRING(100),
        defaultValue: 'INV-{YYYY}-{MM}-{0000}'
      },
      startingNumber: {
        type: Sequelize.INTEGER,
        defaultValue: 1
      },
      resetMonthly: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      defaultTaxRate: {
        type: Sequelize.DECIMAL(5, 2),
        defaultValue: 0.00
      },
      taxRegNumber: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      includeTax: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('einvoice_settings');
  }
}; 