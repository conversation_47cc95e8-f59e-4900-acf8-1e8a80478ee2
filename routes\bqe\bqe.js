const express = require('express');
const router = express.Router();
const axios = require('axios');
const JWTManager = require('../../business/JWTManager');
const AuthManager = require('../../business/AuthManager');
const GeneralMethods = require('../../shared/GeneralMethods');
const Result = require('../../shared/Result');
const { StagingInvoice, sequelize } = require('../../models');
const Sequelize = require('sequelize');
const moment = require('moment');
const fs = require('fs');
const path = require('path');

const SubmitToBQE = require('../../services/bqe/submitter');

const { WP_LOGS } = require('../../models');

const { LoggingService, LOG_TYPES, MODULES, ACTIONS, STATUS } = require('../../services/logging.service');

const BQE_API_LIMITS = {
    MAX_PAGES: 25,
    RECORDS_PER_PAGE: 100,
    MAX_TOTAL_RECORDS: 2500 // 25 pages × 100 records
};
// BQE Authorization route
router.get('/auth', async (req, res) => {
    try {
        const authManager = new AuthManager();
        const config = GeneralMethods.GetConfig();

        // Generate state parameter
        const state = Math.random().toString(36).substring(7);
        req.session.state = state;

        // Construct auth URL with proper encoding
        const authUrl = new URL(config.CoreIdentityBaseUrl + '/connect/authorize');
        authUrl.searchParams.append('response_type', 'code');
        authUrl.searchParams.append('client_id', config.ClientID);
        authUrl.searchParams.append('redirect_uri', config.RedirectURI);
        authUrl.searchParams.append('scope', config.Scopes);
        authUrl.searchParams.append('state', state);

        // Create log first with formatted date
        await WP_LOGS.create({
            Description: `BQE Authorization initiated - User ${req.session.user?.username || 'system'} requesting authorization. State: ${state}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session.user?.username || 'system',
            // Add more context but mask sensitive data
            Details: JSON.stringify({
                clientId: '[REDACTED]',
                redirectUri: '[REDACTED]',
                state: state,
                scopes: config.Scopes.split(' ').length + ' scopes requested', // Only log scope count
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        // Then send response
        return res.json({
            success: true,
            redirectUrl: authUrl.toString()
        });

    } catch (error) {
        console.error('BQE Auth Error:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to initialize BQE authorization',
            details: error.message
        });
    }
});

// BQE Callback route
router.get('/callback', async (req, res) => {
    try {
        // Add initial callback log with formatted date
        await WP_LOGS.create({
            Description: `BQE callback received with state: ${req.query.state}. Processing authorization response.`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                hasCode: !!req.query.code,
                state: req.query.state,
                error: req.query.error,
                error_description: req.query.error_description,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        console.log('Callback received:', {
            code: req.query.code ? 'present' : 'missing',
            state: req.query.state,
            error: req.query.error,
            error_description: req.query.error_description
        });

        if (req.query.error) {
            // Add error log with formatted date
            await WP_LOGS.create({
                Description: `BQE OAuth error encountered: ${req.query.error_description || req.query.error}. Authorization failed.`,
                CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
                LoggedUser: req.session?.user?.username || 'system',
                Details: JSON.stringify({
                    error: req.query.error,
                    errorDescription: req.query.error_description,
                    state: req.query.state,
                    userIP: req.ip,
                    requestHeaders: req.headers
                })
            });
            console.error('OAuth error:', {
                error: req.query.error,
                description: req.query.error_description
            });
            return res.redirect('/outbound?auth=error&message=' + encodeURIComponent(req.query.error_description || req.query.error));
        }

        if (!req.query.code) {
            console.error('No authorization code received');
            return res.redirect('/outbound?auth=error&message=no_code');
        }

        const authManager = new AuthManager();

        // Verify state parameter
        if (!authManager.IsValidState(req, res, req.query.state)) {
            console.error('Invalid state parameter');
            return res.redirect('/outbound?auth=error&message=invalid_state');
        }

        // Exchange code for tokens
        authManager.Authorize(req.query.code, (status, response) => {
            if (status === Result.Success && response) {
                try {
                    // Save auth response
                    authManager.SaveAuthResponse(response);

                    // Validate JWT token
                    const jwtManager = new JWTManager(GeneralMethods.GetConfig(), response.id_token);
                    const decodedJWT = jwtManager.DecodeJWT();

                    jwtManager.ValidateJWT(decodedJWT, async (isValid) => {
                        if (isValid) {
                            // Add success log
                            await WP_LOGS.create({
                                Description: `BQE authentication successful for user ${req.session?.user?.username || 'system'}. JWT validated successfully.`,
                                CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
                                LoggedUser: req.session?.user?.username || 'system',
                                Details: JSON.stringify({
                                    tokenType: response.token_type,
                                    scope: response.scope,
                                    jwtValidated: true,
                                    userIP: req.ip,
                                    userAgent: req.headers['user-agent']
                                })
                            });
                            res.redirect('/outbound?auth=success');
                        } else {
                            await WP_LOGS.create({
                                Description: `BQE JWT validation failed for user ${req.session?.user?.username || 'system'}. Token validation error.`,
                                CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
                                LoggedUser: req.session?.user?.username || 'system',
                                Details: JSON.stringify({
                                    error: 'JWT validation failed',
                                    userIP: req.ip,
                                    userAgent: req.headers['user-agent']
                                })
                            });
                            console.error('Invalid JWT token');
                            res.redirect('/outbound?auth=error&message=invalid_token');
                        }
                    });
                } catch (error) {
                    console.error('Error processing auth response:', error);
                    res.redirect('/outbound?auth=error&message=processing_error');
                }
            } else {
                console.error('Authorization failed:', response?.body);
                res.redirect('/outbound?auth=error&message=auth_failed');
            }
        });
    } catch (error) {
        // Add error log with formatted date
        await WP_LOGS.create({
            Description: `BQE callback error: ${error.message}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system'
        });
        console.error('BQE Callback Error:', error);
        res.redirect('/outbound?auth=error&message=' + encodeURIComponent(error.message));
    }
});

// Disconnect from BQE
router.post('/disconnect', async (req, res) => {
    try {
        const authManager = new AuthManager();
        // Convert callback to async/await using Promise
        const disconnectResult = await new Promise((resolve) => {
            authManager.DisconnectFromCore((status, response) => {
                resolve({ status, response });
            });
        });

        if (disconnectResult.status === Result.Success) {
            try {
                authManager.SaveAuthResponse(null);

                await WP_LOGS.create({
                    Description: `User ${req.session.user.username} initiated disconnect from BQE. Session terminated.`,
                    CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
                    LoggedUser: req.session.user.username,
                    Details: JSON.stringify({
                        action: 'disconnect',
                        status: 'success',
                        userIP: req.ip,
                        userAgent: req.headers['user-agent'],
                        sessionData: req.session
                    })
                });

                res.json({
                    success: true,
                    message: 'Successfully disconnected from BQE'
                });
            } catch (error) {
                console.error('Error clearing auth file:', error);
                res.status(500).json({
                    success: false,
                    error: 'Failed to clear authentication data',
                    details: error.message
                });
            }
        } else {
            await WP_LOGS.create({
                Description: `Failed to disconnect from BQE for user ${req.session.user.username}. Error: ${disconnectResult.response?.body || 'Unknown error'}`,
                CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
                LoggedUser: req.session.user.username,
                Details: JSON.stringify({
                    action: 'disconnect',
                    status: 'failed',
                    error: disconnectResult.response?.body,
                    userIP: req.ip,
                    userAgent: req.headers['user-agent']
                })
            });
            console.error('Disconnect failed:', disconnectResult.response?.body);
            res.status(500).json({
                success: false,
                error: 'Failed to disconnect from BQE',
                details: disconnectResult.response?.body || 'Unknown error'
            });
        }
    } catch (error) {
        console.error('BQE Disconnect Error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to disconnect from BQE',
            details: error.message
        });
    }
});

// Add this new route to check authorization status
router.get('/check-auth', async (req, res) => {
    try {
        const authManager = new AuthManager();
        const authResponse = authManager.GetAuthResponse();

        if (!authResponse || !authResponse.access_token) {
            return res.json({
                isAuthorized: false,
                details: null,
                authResponse: null
            });
        }

        // Calculate expiry time
        const tokenCreationTime = new Date(authResponse.created_at || Date.now());
        const expiryTime = new Date(tokenCreationTime.getTime() + (authResponse.expires_in * 1000));
        const now = new Date();
        const remainingTime = Math.floor((expiryTime - now) / 1000);

        // Check if token is expired or about to expire
        if (remainingTime <= 0) {
            return res.json({
                isAuthorized: false,
                details: {
                    error: 'Token expired'
                },
                authResponse: null
            });
        }

        res.json({
            isAuthorized: true,
            details: {
                expiresIn: remainingTime,
                tokenType: authResponse.token_type,
                scope: authResponse.scope,
                created: tokenCreationTime,
                expires: expiryTime
            },
            authResponse: authResponse
        });

    } catch (error) {
        console.error('Auth check error:', error);
        res.status(500).json({
            isAuthorized: false,
            error: error.message
        });
    }
});


router.get('/company', async (req, res) => {
    try {
        const authManager = new AuthManager();
        const authResponse = authManager.GetAuthResponse();

        // Check if we have a valid auth response
        if (!authResponse || !authResponse.access_token) {
            return res.status(401).json({ error: 'Please log in again', redirect: '/' });
        }

        // Fetch company details with ALL custom fields
        const companyResponse = await axios({
            method: 'get',
            url: `${authResponse.endpoint}/company`,
            headers: {
                'Authorization': `Bearer ${authResponse.access_token}`,
                'Accept': 'application/json'
            },
            params: {
                expand: [
                    'customFields($select=*)',  // Get all custom fields
                    'address',
                    'contact',
                    'settings',
                    'taxGroup1',
                    'taxGroup2',
                    'taxGroup3'
                ].join(','),
                $select: '*'  // Get all fields
            }
        });

        // Ensure custom fields are properly structured
        const companyData = {
            ...companyResponse.data,
            customFields: companyResponse.data.customFields?.map(field => ({
                ...field,
                label: field.label?.trim(),  // Ensure labels are trimmed
                value: field.value?.trim()   // Ensure values are trimmed
            })) || []
        };

        // Log the custom fields for debugging
        console.log('Company custom fields:', companyData.customFields.map(f => ({
            label: f.label,
            value: f.value
        })));

        res.json(companyData);
    } catch (error) {
        console.error('Error fetching company:', error.response?.data || error.message);
        if (error.response?.status === 401) {
            return res.status(401).json({ error: 'Authentication failed', redirect: '/' });
        }
        res.status(500).json({ error: error.response?.data?.message || error.message });
    }
});

router.get('/client/:clientId', async (req, res) => {
    try {
        const { clientId } = req.params;

        // Get BQE auth manager instance
        const authManager = new AuthManager();
        const bqeAuth = authManager.GetAuthResponse();

        if (!bqeAuth || !bqeAuth.access_token || !bqeAuth.endpoint) {
            throw new Error('No valid BQE authentication found');
        }

        // Get client details from BQE
        const clientResponse = await axios({
            method: 'get',
            url: `${bqeAuth.endpoint}/client/${clientId}`,
            headers: {
                'Authorization': `Bearer ${bqeAuth.access_token}`,
                'Accept': 'application/json'
            },
            params: {
                expand: [
                    'customFields',
                    'address',
                    'contact',
                    'communications',
                    'address.communications'
                ].join(',')
            }
        });

        if (!clientResponse.data) {
            throw new Error('Failed to fetch client details from BQE');
        }

        // Map client details with proper structure
        const clientDetails = {
            ...clientResponse.data,
            company: clientResponse.data.company || clientResponse.data.formattedName,
            taxId: clientResponse.data.taxId,
            registrationNumber: clientResponse.data.customFields?.find(f =>
                f.label === "Buyer's Registration No" ||
                f.label === "Registration Number"
            )?.value,
            address: {
                ...clientResponse.data.address,
                communications: clientResponse.data.address?.communications || []
            },
            communications: [
                ...(clientResponse.data.communications || []),
                ...(clientResponse.data.address?.communications || [])
            ].filter(comm => comm.value && comm.typeName)
        };

        res.json(clientDetails);

    } catch (error) {
        console.error('Error fetching client details:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch client details',
            details: error.message
        });
    }
});


router.get('/project/:projectId', async (req, res) => {
    try {
        const { projectId } = req.params;

        // Get BQE auth manager instance
        const authManager = new AuthManager();
        const bqeAuth = authManager.GetAuthResponse();

        if (!bqeAuth || !bqeAuth.access_token || !bqeAuth.endpoint) {
            throw new Error('No valid BQE authentication found');
        }

        // Get client details from BQE
        const projectResponse = await axios({
            method: 'get',
            url: `${bqeAuth.endpoint}/project/${projectId}`,
            headers: {
                'Authorization': `Bearer ${bqeAuth.access_token}`,
                'Accept': 'application/json'
            },
            params: {
                expand: [
                    'customFields',
                    'address',
                    'assignedGroups',
                    'rules',
                ].join(',')
            }
        });

        if (!projectResponse.data) {
            throw new Error('Failed to fetch client details from BQE');
        }

        // Map client details with proper structure
        const projectDetails = {
            ...projectResponse.data,
        };

        res.json(projectDetails);

    } catch (error) {
        console.error('Error fetching client details:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch client details',
            details: error.message
        });
    }
});

// Update the invoice fetch route to handle pagination properly
router.get('/invoice/:id', async (req, res) => {
    try {
        const { id } = req.params;

        // Log invoice fetch attempt
        await WP_LOGS.create({
            Description: `BQE invoice fetch initiated for ID: ${id}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_invoice_initiated',
                invoiceId: id,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        const authManager = new AuthManager();
        const bqeAuth = authManager.GetAuthResponse();

        if (!bqeAuth || !bqeAuth.access_token || !bqeAuth.endpoint) {
            throw new Error('No valid BQE authentication found');
        }

        // Get invoice details from BQE with payments included
        let invoiceResponse;
        try {
            invoiceResponse = await axios({
                method: 'get',
                url: `${bqeAuth.endpoint}/invoice/${id}`,
                headers: {
                    'Authorization': `Bearer ${bqeAuth.access_token}`,
                    'Accept': 'application/json'
                },
                params: {
                    expand: [
                        'customFields',
                        'workflow',
                        'lineItems',
                        'accountSplits',
                        'extendedAccountSplit',
                        'invoiceDetails',
                        'project',
                        'payments'  // Add payments to expand parameter
                    ].join(',')
                }
            });
        } catch (axiosError) {
            if (axiosError.response?.status === 404) {
                throw new Error(`Invoice with ID ${id} not found`);
            }
            if (axiosError.response?.status === 401) {
                throw new Error('Authentication token expired or invalid');
            }
            throw new Error(`BQE API Error: ${axiosError.response?.data?.message || axiosError.message}`);
        }

        if (!invoiceResponse?.data) {
            throw new Error('No invoice data received from BQE');
        }

        // Get UUID information from staging_invoices and WP_SUBMISSION_STATUS
        let uuid = null;
        let submission_uuid = null;

        // Get invoice number from BQE response
        const invoiceNumber = invoiceResponse.data.invoiceNumber;

        try {
            // First check staging_invoices table
            const stagingResult = await sequelize.query(
                `SELECT TOP 1 id, bqe_invoice_id, invoice_number, uuid, submission_uuid, status
                 FROM staging_invoices
                 WHERE (bqe_invoice_id = :id OR invoice_number = :invoiceNumber)
                 ORDER BY date_sync DESC`,
                {
                    replacements: {
                        id,
                        invoiceNumber
                    },
                    type: sequelize.QueryTypes.SELECT
                }
            );

            if (stagingResult && stagingResult.length > 0 && stagingResult[0].uuid) {
                console.log('Found UUID information in staging_invoices:', stagingResult[0]);
                uuid = stagingResult[0].uuid;
                submission_uuid = stagingResult[0].submission_uuid;
            } else {
                // Check WP_SUBMISSION_STATUS as fallback
                const submissionResult = await sequelize.query(
                    `SELECT TOP 1 ID, DOC_ID, DOC_UUID, SUBMISSION_UUID, INVOICE_NO
                     FROM WP_SUBMISSION_STATUS
                     WHERE (DOC_ID = :id OR INVOICE_NO = :invoiceNumber)
                     ORDER BY CREATE_TS DESC`,
                    {
                        replacements: {
                            id,
                            invoiceNumber
                        },
                        type: sequelize.QueryTypes.SELECT
                    }
                );

                if (submissionResult && submissionResult.length > 0 && submissionResult[0].DOC_UUID) {
                    console.log('Found UUID information in WP_SUBMISSION_STATUS:', submissionResult[0]);
                    uuid = submissionResult[0].DOC_UUID;
                    submission_uuid = submissionResult[0].SUBMISSION_UUID;
                }
            }
        } catch (uuidError) {
            console.error('Error fetching UUID information:', uuidError);
            // Continue execution even if UUID fetch fails
        }

        const projectId = invoiceResponse.data.invoiceDetails?.[0]?.projectId;
        console.log(projectId);
        let projectDetails = null;

        if (projectId) {
            try {
                const projectResponse = await axios({
                    method: 'get',
                    url: `${bqeAuth.endpoint}/project/${projectId}`,
                    headers: {
                        'Authorization': `Bearer ${bqeAuth.access_token}`,
                        'Accept': 'application/json'
                    },
                    params: {
                        expand: [
                            'customFields',
                            'client',
                            'manager',
                            'address',
                            'assignedGroups',
                            'rules',
                            'parent',
                            'rootProject',
                            'budgetDetails',
                            'workflow',
                            'communications',
                            'cultureCode',
                            'currencyMultiplierId',
                            'currencyName',
                            'currencyMultiplier'
                        ].join(',')
                    }
                });

                if (projectResponse.data) {
                    // Map project details with proper structure
                    projectDetails = {
                        ...projectResponse.data,
                        customFields: projectResponse.data.customFields || [],
                        address: {
                            ...projectResponse.data.address,
                            communications: projectResponse.data.address?.communications || []
                        },
                        // Combine all communications
                        communications: [
                            ...(projectResponse.data.communications || []),
                            ...(projectResponse.data.address?.communications || [])
                        ].filter(comm => comm.value && comm.typeName)
                    };
                }
            } catch (projectError) {
                console.error('Error fetching project details:', projectError);
                // Continue execution even if project fetch fails
            }
        }

        // Log successful invoice fetch
        await WP_LOGS.create({
            Description: `BQE invoice details retrieved successfully for ID: ${id}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_invoice_success',
                invoiceId: id,
                invoiceNumber: invoiceResponse.data?.invoiceNumber || '[REDACTED]',
                uuid: uuid || 'not_found',
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });


        // Get client details
        const clientId = invoiceResponse.data.invoiceDetails?.[0]?.clientId;
        let clientDetails = null;

        if (clientId) {
            try {
                const clientResponse = await axios({
                    method: 'get',
                    url: `${bqeAuth.endpoint}/client/${clientId}`,
                    headers: {
                        'Authorization': `Bearer ${bqeAuth.access_token}`,
                        'Accept': 'application/json'
                    },
                    params: {
                        expand: [
                            'customFields',
                            'address',
                            'contact',
                            'communications',
                            'address.communications'
                        ].join(',')
                    }
                });

                if (clientResponse.data) {
                    // Map client details with proper structure
                    clientDetails = {
                        ...clientResponse.data,
                        taxId: clientResponse.data.taxId,
                        registrationNumber: clientResponse.data.customFields?.find(f => f.label === 'Registration Number')?.value,
                        address: {
                            ...clientResponse.data.address,
                            communications: clientResponse.data.address?.communications || []
                        },
                        // Combine all communications
                        communications: [
                            ...(clientResponse.data.communications || []),
                            ...(clientResponse.data.address?.communications || [])
                        ].filter(comm => comm.value && comm.typeName)
                    };
                }
            } catch (clientError) {
                console.error('Error fetching client details:', clientError);
                // Continue execution even if client fetch fails
            }
        }

        // Get company details
        let companyData = null;
        try {
            const companyResponse = await axios.get(`${bqeAuth.endpoint}/company`, {
                headers: {
                    'Authorization': `Bearer ${bqeAuth.access_token}`
                },
                params: {
                    expand: [
                        'customFields($select=*)',  // Get all custom fields
                        'address',
                        'contact',
                        'settings',
                        'taxGroup1',
                        'taxGroup2',
                        'taxGroup3'
                    ].join(','),
                    $select: '*'  // Get all fields
                }
            });

            // Ensure custom fields are properly structured
            companyData = {
                ...companyResponse.data,
                customFields: companyResponse.data.customFields?.map(field => ({
                    ...field,
                    label: field.label?.trim(),  // Ensure labels are trimmed
                    value: field.value?.trim()   // Ensure values are trimmed
                })) || []
            };

            // Log company custom fields for debugging
            console.log('Company custom fields for invoice:', companyData.customFields.map(f => ({
                label: f.label,
                value: f.value
            })));
        } catch (companyError) {
            console.error('Error fetching company details:', companyError);
            // Continue execution even if company fetch fails
        }

        // Get payment details if invoice has payments
let paymentDetails = [];
if (invoiceResponse.data?.payments?.length > 0) {
    try {
        // Fetch detailed payment information for each payment
        paymentDetails = await Promise.all(
            invoiceResponse.data.payments.map(async payment => {
                const paymentResponse = await axios({
                    method: 'get',
                    url: `${bqeAuth.endpoint}/payment/${payment.id}`,
                    headers: {
                        'Authorization': `Bearer ${bqeAuth.access_token}`,
                        'Accept': 'application/json'
                    },
                    params: {
                        expand: [
                            'customFields',
                            'paymentMethod',
                            'invoicePayments',
                            'account'
                        ].join(',')
                    }
                });

                // Add console log for payment response
                console.log('Payment details fetched:', {
                    paymentId: payment.id,
                    details: paymentResponse.data
                });

                return {
                    ...paymentResponse.data,
                    paymentDate: paymentResponse.data.paymentDate,
                    amount: paymentResponse.data.amount,
                    referenceNumber: paymentResponse.data.referenceNumber,
                    paymentMethod: paymentResponse.data.paymentMethod,
                    status: paymentResponse.data.status
                };
            })
        );

        // Add console log for all payments
        console.log('All payment details:', paymentDetails);

    } catch (paymentError) {
        console.error('Error fetching payment details:', paymentError);
    }
}



        // Prepare complete invoice data with payments
        const invoiceData = {
            ...invoiceResponse.data,
            _clientDetails: clientDetails,
            company: companyData,
            project: projectDetails,
            payments: paymentDetails,
            paymentStatus: {
                totalPaid: paymentDetails.reduce((sum, payment) => sum + (payment.amount || 0), 0),
                hasPayments: paymentDetails.length > 0,
                lastPaymentDate: paymentDetails.length > 0
                    ? Math.max(...paymentDetails.map(p => new Date(p.paymentDate)))
                    : null
            },
            id: id,
            invoiceNumber: invoiceResponse.data.invoiceNumber,
            uuid: uuid,
            submission_uuid: submission_uuid,
        };

        // Log successful fetch with payment information
        await WP_LOGS.create({
            Description: `BQE invoice details with payments retrieved for ID: ${id}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_invoice_success',
                invoiceId: id,
                invoiceNumber: invoiceResponse.data?.invoiceNumber || '[REDACTED]',
                paymentCount: paymentDetails.length,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        res.json(invoiceData);

    } catch (error) {
        // Enhanced error logging
        console.error('Error fetching invoice details:', {
            error: error.message,
            stack: error.stack,
            responseData: error.response?.data,
            statusCode: error.response?.status
        });

        // Log fetch error with more details
        await WP_LOGS.create({
            Description: `BQE invoice fetch error: ${error.message}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_invoice_error',
                invoiceId: req.params.id,
                error: error.message,
                errorStack: error.stack,
                statusCode: error.response?.status,
                responseData: error.response?.data,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        // Send more informative error response
        res.status(error.response?.status || 500).json({
            success: false,
            error: error.message || 'Failed to fetch invoice details',
            details: error.response?.data || error.stack,
            code: error.response?.status || 500
        });
    }
});


router.get('/invoices', async (req, res) => {
    try {
        const { page = 0, pageSize = BQE_API_LIMITS.RECORDS_PER_PAGE } = req.query;
        const skip = parseInt(page) * parseInt(pageSize);

        // Log invoices fetch attempt
        await WP_LOGS.create({
            Description: `BQE invoices list fetch initiated`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_invoices_initiated',
                page: page,
                pageSize: pageSize,
                skip: skip,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        // Check if we're exceeding API limits
        if (skip >= BQE_API_LIMITS.MAX_TOTAL_RECORDS) {
            await WP_LOGS.create({
                Description: `BQE invoices fetch - API limit exceeded`,
                CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
                LoggedUser: req.session?.user?.username || 'system',
                Details: JSON.stringify({
                    action: 'fetch_invoices_limit_exceeded',
                    skip: skip,
                    maxRecords: BQE_API_LIMITS.MAX_TOTAL_RECORDS,
                    userIP: req.ip,
                    userAgent: req.headers['user-agent']
                })
            });

            return res.status(400).json({
                error: 'API Limit Exceeded',
                message: `BQE API only allows fetching up to ${BQE_API_LIMITS.MAX_TOTAL_RECORDS} records`,
                limits: BQE_API_LIMITS
            });
        }

        const authManager = new AuthManager();
        const authResponse = authManager.GetAuthResponse();

        if (!authResponse?.access_token) {
            return res.status(401).json({ error: 'Authorization required' });
        }

        // Make the API request with proper pagination
        const response = await axios({
            method: 'get',
            url: `${authResponse.endpoint}/invoice`,
            headers: {
                'Authorization': `Bearer ${authResponse.access_token}`,
                'Accept': 'application/json'
            },
            params: {
                '$top': pageSize,
                '$skip': skip,
                '$count': true
            }
        });

        // Log successful invoices fetch
        await WP_LOGS.create({
            Description: `BQE invoices list retrieved successfully`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_invoices_success',
                page: page,
                pageSize: pageSize,
                totalRecords: response.data['@odata.count'],
                recordsFetched: response.data.value.length,
                rateLimit: {
                    limit: response.headers['x-ratelimit-limit'],
                    remaining: response.headers['x-ratelimit-remaining'],
                    reset: response.headers['x-ratelimit-reset']
                },
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        // Return paginated response with metadata
        res.json({
            data: response.data.value,
            pagination: {
                page: parseInt(page),
                pageSize: parseInt(pageSize),
                totalRecords: response.data['@odata.count'] || 0,
                hasMore: (skip + response.data.value.length) < Math.min(
                    response.data['@odata.count'],
                    BQE_API_LIMITS.MAX_TOTAL_RECORDS
                )
            },
            limits: {
                ...BQE_API_LIMITS,
                rateLimit: {
                    limit: response.headers['x-ratelimit-limit'],
                    remaining: response.headers['x-ratelimit-remaining'],
                    reset: response.headers['x-ratelimit-reset']
                }
            }
        });

    } catch (error) {
        // Log fetch error
        await WP_LOGS.create({
            Description: `BQE invoices list fetch error: ${error.message}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_invoices_error',
                error: error.message,
                errorStack: error.stack,
                statusCode: error.response?.status,
                page: req.query.page,
                pageSize: req.query.pageSize,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        handleApiError(error, res);
    }
});

// Add error handling utility
function handleApiError(error, res) {
    console.error('API Error:', error);

    if (error.response?.status === 429) {
        return res.status(429).json({
            error: 'Rate limit exceeded',
            message: 'Too many requests. Please try again later.',
            retryAfter: error.response.headers['retry-after']
        });
    }

    if (error.response?.status === 401) {
        return res.status(401).json({
            error: 'Authentication failed',
            redirect: '/'
        });
    }

    res.status(error.response?.status || 500).json({
        error: 'API request failed',
        message: error.message,
        details: error.response?.data
    });
}

// Add rate limit middleware
const rateLimitMiddleware = async (req, res, next) => {
    try {
        const authManager = new AuthManager();
        const authResponse = authManager.GetAuthResponse();

        if (!authResponse?.access_token) {
            return next();
        }

        // Check current rate limits
        const response = await axios.head(`${authResponse.endpoint}/invoice`, {
            headers: {
                'Authorization': `Bearer ${authResponse.access_token}`
            }
        });

        const remaining = parseInt(response.headers['x-ratelimit-remaining']);
        if (remaining < 10) { // Buffer for safety
            return res.status(429).json({
                error: 'Rate limit nearly exceeded',
                message: 'Please wait before making more requests',
                retryAfter: response.headers['x-ratelimit-reset']
            });
        }

        next();
    } catch (error) {
        next(error);
    }
};

// Apply rate limit middleware to relevant routes
router.use(['/invoice', '/invoices'], rateLimitMiddleware);

// Add this new route
router.post('/check-staging', async (req, res) => {
    try {
        const { fromDate, toDate } = req.body;
        console.log('Checking staging with date range:', { fromDate, toDate });

        // Get all invoices without date filtering for now
        const stagedInvoices = await StagingInvoice.findAll({
            attributes: [
                'id',
                'bqe_invoice_id',
                'invoice_number',
                'status',
                'date_submitted',
                'date_sync',
                'uuid',
                'submission_uuid',
                'submitted_by',
                'date_cancelled',
                'cancelled_by',
                'cancellation_reason'
            ],
            order: [['id', 'DESC']]
        });

        console.log(`Found ${stagedInvoices.length} invoices in staging`);

        if (stagedInvoices && stagedInvoices.length > 0) {
            const transformedInvoices = stagedInvoices.map(invoice => ({
                id: invoice.bqe_invoice_id,
                invoice_number: invoice.invoice_number,
                status: invoice.status || 'Pending',
                date_submitted: invoice.date_submitted,
                date_sync: invoice.date_sync,
                uuid: invoice.uuid,
                submission_uuid: invoice.submission_uuid,
                submitted_by: invoice.submitted_by,
                date_cancelled: invoice.date_cancelled,
                cancelled_by: invoice.cancelled_by,
                cancellation_reason: invoice.cancellation_reason
            }));

            console.log('Invoice UUIDs in staging:', transformedInvoices.map(inv => ({
                invoice_number: inv.invoice_number,
                uuid: inv.uuid,
                submission_uuid: inv.submission_uuid
            })));

            res.json({
                hasData: true,
                invoices: transformedInvoices
            });
        } else {
            res.json({
                hasData: false,
                invoices: []
            });
        }

    } catch (error) {
        console.error('Error checking staging data:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to check staging data',
            details: error.message
        });
    }
});

// Update the save-invoices route
router.post('/save-invoices', async (req, res) => {
    try {
        const invoices = req.body.invoices;
        const username = req.session?.user?.username || 'system';

        console.log('Saving invoices to staging:', invoices);

        // Process invoices
        const results = await Promise.all(invoices.map(async (invoice) => {
            try {
                // Check if invoice already exists by invoice_number only
                const existingInvoice = await StagingInvoice.findOne({
                    where: { invoice_number: invoice.invoice_number }
                });

                if (existingInvoice) {
                    // If bqe_invoice_id is missing or different, update it
                    if ((!existingInvoice.bqe_invoice_id && invoice.id) || (invoice.id && existingInvoice.bqe_invoice_id !== invoice.id)) {
                        await sequelize.query(
                            `UPDATE staging_invoices
                             SET bqe_invoice_id = :bqe_invoice_id, updated_at = GETDATE()
                             WHERE invoice_number = :invoice_number`,
                            {
                                replacements: {
                                    bqe_invoice_id: invoice.id,
                                    invoice_number: invoice.invoice_number
                                },
                                type: sequelize.QueryTypes.UPDATE
                            }
                        );
                        return {
                            id: invoice.id,
                            status: 'updated',
                            fields: ['bqe_invoice_id']
                        };
                    }
                    // If it exists but UUID is missing, update it
                    if (!existingInvoice.uuid && invoice.uuid) {
                        await sequelize.query(
                            `UPDATE staging_invoices
                             SET uuid = :uuid, updated_at = GETDATE()
                             WHERE invoice_number = :invoice_number`,
                            {
                                replacements: {
                                    uuid: invoice.uuid,
                                    invoice_number: invoice.invoice_number
                                },
                                type: sequelize.QueryTypes.UPDATE
                            }
                        );
                        return {
                            id: invoice.id,
                            status: 'updated',
                            fields: ['uuid']
                        };
                    }
                    return {
                        id: invoice.id,
                        status: 'preserved'
                    };
                }

                // Use raw query to ensure GETDATE() is used for all date fields and include uuid
                await sequelize.query(
                    `INSERT INTO staging_invoices
                     (bqe_invoice_id, invoice_number, status, uuid, submitted_by, date_sync, created_at, updated_at)
                     VALUES
                     (:bqe_invoice_id, :invoice_number, :status, :uuid, :submitted_by, GETDATE(), GETDATE(), GETDATE())`,
                    {
                        replacements: {
                            bqe_invoice_id: invoice.id,
                            invoice_number: invoice.invoice_number,
                            status: 'Pending',
                            uuid: invoice.uuid || null,
                            submitted_by: username
                        },
                        type: sequelize.QueryTypes.INSERT
                    }
                );

                return {
                    id: invoice.id,
                    status: 'created'
                };

            } catch (error) {
                console.error(`Error processing invoice ${invoice.id}:`, error);
                return {
                    id: invoice.id,
                    status: 'error',
                    error: error.message
                };
            }
        }));

        res.json({ success: true, results });

    } catch (error) {
        console.error('Error in save-invoices:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

router.post('/submit-to-lhdn', async (req, res) => {
    const { invoiceId, version, invoiceData } = req.body;

    try {
        // Initial submission log
        await LoggingService.log({
            description: `Invoice submission initiated for ${invoiceId}`,
            username: req.session?.user?.username,
            userId: req.session?.user?.id,
            ipAddress: req.ip,
            logType: LOG_TYPES.INFO,
            module: MODULES.INVOICE,
            action: ACTIONS.SUBMIT,
            status: STATUS.PENDING,
            details: {
                invoiceId,
                version,
                hasInvoiceData: !!invoiceData
            }
        });

        // Validate required data
        if (!invoiceData) {
            await LoggingService.log({
                description: `Invoice submission failed - No invoice data provided`,
                username: req.session?.user?.username,
                userId: req.session?.user?.id,
                ipAddress: req.ip,
                logType: LOG_TYPES.ERROR,
                module: MODULES.INVOICE,
                action: ACTIONS.VALIDATE,
                status: STATUS.FAILED,
                details: { invoiceId }
            });
            throw new Error('No invoice data provided');
        }

        // Create BQE submitter instance and submit
        const bqeSubmitter = new SubmitToBQE(req);
        const submissionResult = await bqeSubmitter.submitInvoice(invoiceData, version);

        console.log('===== LHDN SUBMISSION RESULT =====');
        console.log('Raw submission result:', JSON.stringify(submissionResult, null, 2));

        // Extract UUIDs from the result - these come from LHDN
        // IMPORTANT: These are DIFFERENT values from LHDN
        const submissionUid = submissionResult.submissionUid || '';
        const uuid = submissionResult.uuid || '';

        console.log('===== UUID INFORMATION FROM LHDN =====');
        console.log('submissionUid (will be saved as submission_uuid):', submissionUid);
        console.log('uuid (will be saved as uuid):', uuid);
        console.log('invoice_number:', invoiceId);

        // Save UUIDs to different tables for redundancy
        try {
            // 1. First save to staging_invoices
            if (!uuid) {
                console.warn('Warning: Missing UUID information from LHDN response');
                console.warn('This will cause cancellation to fail later');
            } else {
                console.log('Saving UUIDs to staging_invoices table...');
                // First try to update by invoice_number
                const updateByInvoiceNumberResult = await sequelize.query(
                    `UPDATE staging_invoices
                     SET status = 'Submitted',
                         date_submitted = GETDATE(),
                         submitted_by = :submitted_by,
                         uuid = :uuid,
                         submission_uuid = :submission_uuid,
                         updated_at = GETDATE()
                     WHERE invoice_number = :invoice_number`,
                    {
                        replacements: {
                            submitted_by: req.session?.user?.username || 'system',
                            uuid: uuid,
                            submission_uuid: submissionUid,
                            invoice_number: invoiceId
                        },
                        type: sequelize.QueryTypes.UPDATE
                    }
                );

                // Also try to update by bqe_invoice_id to ensure all records are updated
                if (invoiceData && invoiceData.id) {
                    console.log('Also updating by BQE invoice ID:', invoiceData.id);
                    await sequelize.query(
                        `UPDATE staging_invoices
                         SET status = 'Submitted',
                             date_submitted = GETDATE(),
                             submitted_by = :submitted_by,
                             uuid = :uuid,
                             submission_uuid = :submission_uuid,
                             updated_at = GETDATE()
                         WHERE bqe_invoice_id = :bqe_invoice_id`,
                        {
                            replacements: {
                                submitted_by: req.session?.user?.username || 'system',
                                uuid: uuid,
                                submission_uuid: submissionUid,
                                bqe_invoice_id: invoiceData.id
                            },
                            type: sequelize.QueryTypes.UPDATE
                        }
                    );
                }

                // 2. Also save to WP_SUBMISSION_STATUS as a backup
                console.log('Saving UUIDs to WP_SUBMISSION_STATUS table...');
                await sequelize.query(
                    `INSERT INTO WP_SUBMISSION_STATUS
                     (DocNum, UUID, SubmissionUID, SubmissionStatus, DateTimeSent, DateTimeUpdated)
                     VALUES
                     (:doc_num, :uuid, :submission_uid, 'Submitted', GETDATE(), GETDATE())`,
                    {
                        replacements: {
                            doc_num: invoiceData?.id || submissionResult?.data?.acceptedDocuments?.[0]?.id || 'submit-' + Date.now(),
                            uuid: uuid,
                            submission_uid: submissionUid
                        },
                        type: sequelize.QueryTypes.INSERT
                    }
                );

                // Verify the update was successful by fetching the invoice by both methods
                const verifyInvoiceByNumber = await sequelize.query(
                    `SELECT id, bqe_invoice_id, invoice_number, uuid, submission_uuid, status FROM staging_invoices
                     WHERE invoice_number = :invoice_number`,
                    {
                        replacements: {
                            invoice_number: invoiceId
                        },
                        type: sequelize.QueryTypes.SELECT
                    }
                );

                console.log('===== DATABASE VERIFICATION AFTER UPDATE BY INVOICE NUMBER =====');
                console.log('Database record after update:', JSON.stringify(verifyInvoiceByNumber, null, 2));

                // Also verify by BQE invoice ID if available
                if (invoiceData && invoiceData.id) {
                    const verifyInvoiceById = await sequelize.query(
                        `SELECT id, bqe_invoice_id, invoice_number, uuid, submission_uuid, status FROM staging_invoices
                         WHERE bqe_invoice_id = :bqe_invoice_id`,
                        {
                            replacements: {
                                bqe_invoice_id: invoiceData.id
                            },
                            type: sequelize.QueryTypes.SELECT
                        }
                    );

                    console.log('===== DATABASE VERIFICATION AFTER UPDATE BY BQE ID =====');
                    console.log('Database record after update:', JSON.stringify(verifyInvoiceById, null, 2));
                }
            }
        } catch (dbError) {
            console.error('Error saving UUIDs to database:', dbError);
            // Continue execution even if DB save fails - we'll still return the UUIDs to the client
        }

        // Success log
        await LoggingService.log({
            description: `Successfully submitted invoice ${invoiceId} to LHDN`,
            username: req.session?.user?.username,
            userId: req.session?.user?.id,
            ipAddress: req.ip,
            logType: LOG_TYPES.INFO,
            module: MODULES.INVOICE,
            action: ACTIONS.SUBMIT,
            status: STATUS.SUCCESS,
            details: {
                invoiceId,
                version,
                submissionResult,
                uuid,
                submissionUid,
                submissionTime: moment().format('YYYY-MM-DD HH:mm:ss')
            }
        });

        return res.json({
            success: true,
            message: 'Invoice submitted successfully',
            submissionTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            submissionDetails: {
                ...submissionResult,
                uuid: uuid // Explicitly include the UUID in the response
            }
        });

    } catch (error) {
        // Error log
        await LoggingService.log({
            description: `Invoice submission failed: ${error.message}`,
            username: req.session?.user?.username,
            userId: req.session?.user?.id,
            ipAddress: req.ip,
            logType: LOG_TYPES.ERROR,
            module: MODULES.INVOICE,
            action: ACTIONS.SUBMIT,
            status: STATUS.FAILED,
            details: {
                invoiceId,
                error: error.message,
                stack: error.stack
            }
        });

        console.error('Error in /submit-to-lhdn:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Failed to submit invoice',
            details: error.details || []
        });
    }
});

router.post('/cancel-invoice', async (req, res) => {
    try {
        const { invoiceId, uuid, reason, invoice_number } = req.body;

        console.log('===== CANCEL INVOICE REQUEST RECEIVED =====');
        console.log('Request body:', {
            invoiceId,
            uuid,
            reason,
            invoice_number
        });

        if (!invoiceId || !uuid || !reason) {
            console.error('Missing required parameters for cancellation');
            console.error('- invoiceId:', invoiceId);
            console.error('- uuid:', uuid);
            console.error('- reason:', reason ? 'provided' : 'missing');

            await LoggingService.log({
                description: 'Invoice cancellation failed - Missing required parameters',
                username: req.session?.user?.username,
                userId: req.session?.user?.id,
                ipAddress: req.ip,
                logType: LOG_TYPES.ERROR,
                module: MODULES.INVOICE,
                action: ACTIONS.UPDATE,
                status: STATUS.FAILED
            });

            return res.status(400).json({
                success: false,
                error: 'Invoice ID, UUID and reason are required'
            });
        }

        // Initial cancellation log
        await LoggingService.log({
            description: `Invoice cancellation initiated for ${invoiceId}`,
            username: req.session?.user?.username,
            userId: req.session?.user?.id,
            ipAddress: req.ip,
            logType: LOG_TYPES.INFO,
            module: MODULES.INVOICE,
            action: ACTIONS.UPDATE,
            status: STATUS.PENDING,
            details: { invoiceId, uuid, invoice_number }
        });

        // Find the invoice first - search by both invoiceId and invoice_number
        let whereClause = {};
        if (invoiceId) {
            whereClause.bqe_invoice_id = invoiceId;
        } else if (invoice_number) {
            whereClause.invoice_number = invoice_number;
        }

        const invoice = await StagingInvoice.findOne({
            where: whereClause
        });

        if (!invoice) {
            console.error(`Invoice not found in staging database: ${invoiceId || invoice_number}`);

            await LoggingService.log({
                description: `Invoice cancellation failed - Invoice ${invoiceId || invoice_number} not found`,
                username: req.session?.user?.username,
                userId: req.session?.user?.id,
                ipAddress: req.ip,
                logType: LOG_TYPES.ERROR,
                module: MODULES.INVOICE,
                action: ACTIONS.UPDATE,
                status: STATUS.FAILED,
                details: { invoiceId, invoice_number }
            });

            return res.status(404).json({
                success: false,
                error: 'Invoice not found'
            });
        }

        console.log('===== INVOICE FOUND IN DATABASE =====');
        console.log('Database invoice record:', {
            id: invoice.id,
            bqe_invoice_id: invoice.bqe_invoice_id,
            invoice_number: invoice.invoice_number,
            status: invoice.status,
            uuid: invoice.uuid,
            submission_uuid: invoice.submission_uuid
        });

        // For the cancellation API, we need the right UUID
        // IMPORTANT: We need to use the UUID from the request, NOT from the database
        console.log('===== CHECKING UUID FOR CANCELLATION =====');
        console.log('UUID from request:', uuid);
        console.log('UUID in database:', invoice.uuid);

        // If database doesn't have UUID but request does, update the database
        if (!invoice.uuid && uuid) {
            console.log('Updating database with UUID from request');
            await invoice.update({
                uuid: uuid,
                submission_uuid: req.body.submission_uuid || uuid
            });
            console.log('Database updated with UUID');
        }

        // If these don't match, it could indicate a problem
        if (uuid !== invoice.uuid && invoice.uuid) {
            console.warn('Warning: UUID mismatch between request and database!');
            console.warn('Proceeding with requested UUID:', uuid);
        }

        // Get auth token for LHDN API
        console.log('===== GETTING LHDN AUTH TOKEN =====');
        const { getTokenAsTaxPayer, cancelValidDocumentBySupplier } = require('../../services/bqe/service');
        const tokenResponse = await getTokenAsTaxPayer(req);

        if (!tokenResponse?.access_token) {
            console.error('Failed to get LHDN access token');
            throw new Error('Failed to get LHDN access token');
        }

        // Cancel at LHDN first
        console.log('===== PROCEEDING WITH LHDN CANCELLATION =====');
        try {
            const lhdnCancelResult = await cancelValidDocumentBySupplier(
                uuid,
                reason,
                tokenResponse.access_token
            );

            if (!lhdnCancelResult || lhdnCancelResult.status !== 'success') {
                console.error('Failed to cancel document at LHDN:', lhdnCancelResult);
                throw new Error('Failed to cancel document at LHDN');
            }

            console.log('===== LHDN CANCELLATION SUCCEEDED - UPDATING DATABASE =====');

            // Format date as YYYY-MM-DD HH:mm:ss
            const formattedDate = moment().format('YYYY-MM-DD HH:mm:ss');

            // Update staging database
            await sequelize.query(
                `UPDATE staging_invoices
                 SET status = :status,
                     date_cancelled = :date_cancelled,
                     cancellation_reason = :reason,
                     cancelled_by = :cancelled_by,
                     updated_at = :updated_at
                 WHERE bqe_invoice_id = :invoiceId OR invoice_number = :invoice_number`,
                {
                    replacements: {
                        status: 'Cancelled',
                        date_cancelled: formattedDate,
                        reason: reason,
                        cancelled_by: req.session?.user?.username || 'system',
                        updated_at: formattedDate,
                        invoiceId: invoiceId || '',
                        invoice_number: invoice_number || ''
                    },
                    type: sequelize.QueryTypes.UPDATE
                }
            );

            // Verify the update
            const verifyUpdate = await sequelize.query(
                `SELECT id, bqe_invoice_id, invoice_number, status, date_cancelled, cancelled_by, cancellation_reason
                 FROM staging_invoices
                 WHERE bqe_invoice_id = :invoiceId OR invoice_number = :invoice_number`,
                {
                    replacements: {
                        invoiceId: invoiceId || '',
                        invoice_number: invoice_number || ''
                    },
                    type: sequelize.QueryTypes.SELECT
                }
            );

            console.log('===== DATABASE UPDATE VERIFICATION =====');
            console.log('Updated invoice record:', JSON.stringify(verifyUpdate, null, 2));

            // Success log
            await LoggingService.log({
                description: `Invoice ${invoiceId || invoice_number} successfully cancelled at both LHDN and staging`,
                username: req.session?.user?.username,
                userId: req.session?.user?.id,
                ipAddress: req.ip,
                logType: LOG_TYPES.INFO,
                module: MODULES.INVOICE,
                action: ACTIONS.UPDATE,
                status: STATUS.SUCCESS,
                details: {
                    invoiceId,
                    uuid,
                    invoiceNumber: invoice.invoice_number,
                    cancellationReason: reason,
                    cancelledBy: req.session?.user?.username,
                    cancellationDate: formattedDate,
                    lhdnResult: lhdnCancelResult
                }
            });

            console.log('===== CANCELLATION PROCESS COMPLETED SUCCESSFULLY =====');

            res.json({
                success: true,
                message: 'Invoice cancelled successfully at both LHDN and staging',
                invoice: {
                    id: invoice.bqe_invoice_id,
                    invoice_number: invoice.invoice_number,
                    status: 'Cancelled',
                    date_cancelled: formattedDate,
                    cancelled_by: req.session?.user?.username,
                    cancellation_reason: reason
                }
            });
        } catch (lhdnError) {
            console.error('===== LHDN CANCELLATION FAILED =====');
            console.error('Error details:', lhdnError);

            // Add specific error handling for 404 errors
            if (lhdnError.response?.status === 404 || lhdnError.message?.includes('404')) {
                return res.status(400).json({
                    success: false,
                    error: 'Failed to cancel invoice - UUID not found in LHDN system',
                    details: 'The UUID provided does not exist in the LHDN system or is incorrectly formatted. Please verify that the UUID is correct.',
                    code: 404
                });
            }

            throw lhdnError;
        }

    } catch (error) {
        console.error('===== CANCELLATION PROCESS FAILED =====');
        console.error('Error cancelling invoice:', error);

        // Error log
        await LoggingService.log({
            description: `Invoice cancellation failed: ${error.message}`,
            username: req.session?.user?.username,
            userId: req.session?.user?.id,
            ipAddress: req.ip,
            logType: LOG_TYPES.ERROR,
            module: MODULES.INVOICE,
            action: ACTIONS.UPDATE,
            status: STATUS.FAILED,
            details: {
                invoiceId: req.body.invoiceId,
                uuid: req.body.uuid,
                error: error.message,
                stack: error.stack
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to cancel invoice',
            details: error.message
        });
    }
});

router.get('/currency', async (req, res) => {
    try {
        const authManager = new AuthManager();
        const authResponse = authManager.GetAuthResponse();

        if (!authResponse || !authResponse.access_token) {
            throw new Error('No valid BQE authentication found');
        }

        // Get currency details from BQE with proper OData parameters
        const currencyResponse = await axios({
            method: 'get',
            url: `${authResponse.endpoint}/currency`,
            headers: {
                'Authorization': `Bearer ${authResponse.access_token}`,
                'Accept': 'application/json'
            },
            params: {
                expand: [
                    'customFields',
                    'multiplier'
                ].join(','),
                // Add OData parameters for better filtering and sorting
                '$orderby': 'code',
                '$count': true,
                '$select': 'id,code,name,symbol,isBaseCurrency,isActive,multiplier'
            }
        });

        // Log successful currency fetch
        await WP_LOGS.create({
            Description: `BQE currency list retrieved successfully`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_currency_success',
                recordsFetched: currencyResponse.data.value?.length,
                totalCount: currencyResponse.data['@odata.count'],
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        res.json({
            success: true,
            data: currencyResponse.data.value,
            metadata: {
                totalCount: currencyResponse.data['@odata.count'],
                baseCurrency: currencyResponse.data.value?.find(c => c.isBaseCurrency) || null
            }
        });

    } catch (error) {
        // Log fetch error
        await WP_LOGS.create({
            Description: `BQE currency fetch error: ${error.message}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_currency_error',
                error: error.message,
                errorStack: error.stack,
                statusCode: error.response?.status,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        handleApiError(error, res);
    }
});

// Add endpoint to get specific currency by ID
router.get('/currency/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const authManager = new AuthManager();
        const authResponse = authManager.GetAuthResponse();

        if (!authResponse || !authResponse.access_token) {
            throw new Error('No valid BQE authentication found');
        }

        // Get specific currency details from BQE
        const currencyResponse = await axios({
            method: 'get',
            url: `${authResponse.endpoint}/currency/${id}`,
            headers: {
                'Authorization': `Bearer ${authResponse.access_token}`,
                'Accept': 'application/json'
            },
            params: {
                expand: [
                    'customFields',
                    'multiplier'
                ].join(','),
                '$select': [
                    'id',
                    'code',
                    'name',
                    'symbol',
                    'isBaseCurrency',
                    'isActive',
                    'multiplier',
                    'customFields'
                ].join(',')
            }
        });

        // Log successful currency fetch
        await WP_LOGS.create({
            Description: `BQE currency details retrieved for ID: ${id}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_currency_detail_success',
                currencyId: id,
                currencyCode: currencyResponse.data.code,
                isBaseCurrency: currencyResponse.data.isBaseCurrency,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        res.json({
            success: true,
            data: currencyResponse.data
        });

    } catch (error) {
        // Log fetch error
        await WP_LOGS.create({
            Description: `BQE currency detail fetch error: ${error.message}`,
            CreateTS: moment().format('YYYY-MM-DD HH:mm:ss'),
            LoggedUser: req.session?.user?.username || 'system',
            Details: JSON.stringify({
                action: 'fetch_currency_detail_error',
                currencyId: req.params.id,
                error: error.message,
                errorStack: error.stack,
                statusCode: error.response?.status,
                userIP: req.ip,
                userAgent: req.headers['user-agent']
            })
        });

        handleApiError(error, res);
    }
});

// Add this endpoint to specifically look up UUIDs for an invoice
router.post('/get-invoice-uuids', async (req, res) => {
    try {
        const { invoice_number, invoice_id, bqe_invoice_id } = req.body;

        console.log('===== DIRECT UUID LOOKUP REQUEST =====');
        console.log('Looking up UUIDs for:', {
            invoice_number,
            invoice_id,
            bqe_invoice_id
        });

        if (!invoice_number && !invoice_id && !bqe_invoice_id) {
            return res.status(400).json({
                success: false,
                error: 'Either invoice_number or invoice_id or bqe_invoice_id is required'
            });
        }

        // Collection of all found invoices
        let foundInvoices = [];

        // 1. Try staging_invoices table first
        try {
            // Build the WHERE clause dynamically
            let whereConditions = [];
            let replacements = {};

            if (invoice_number) {
                whereConditions.push('invoice_number = :invoice_number');
                replacements.invoice_number = invoice_number;
            }

            if (invoice_id) {
                // Check if invoice_id is a UUID (contains hyphens) or an integer
                const isUuid = typeof invoice_id === 'string' && invoice_id.includes('-');
                if (isUuid) {
                    // If it's a UUID, only compare with UUID columns
                    whereConditions.push('bqe_invoice_id = :invoice_id');
                } else {
                    // If it's an integer, compare with both id and bqe_invoice_id
                    whereConditions.push('(id = :invoice_id OR bqe_invoice_id = :invoice_id)');
                }
                replacements.invoice_id = invoice_id;
            }

            if (bqe_invoice_id) {
                whereConditions.push('bqe_invoice_id = :bqe_invoice_id');
                replacements.bqe_invoice_id = bqe_invoice_id;
            }

            const whereClause = whereConditions.join(' OR ');

            // Query the database
            const stagingInvoices = await sequelize.query(
                `SELECT TOP 5 id, bqe_invoice_id, invoice_number, status, uuid, submission_uuid,
                        date_submitted, date_cancelled, date_sync
                 FROM staging_invoices
                 WHERE ${whereClause}
                 ORDER BY date_sync DESC, id DESC`, // Get the most recent records first
                {
                    replacements,
                    type: sequelize.QueryTypes.SELECT
                }
            );

            console.log(`Found ${stagingInvoices.length} invoices in staging_invoices table`);

            if (stagingInvoices && stagingInvoices.length > 0) {
                // Filter for invoices with UUIDs
                const invoicesWithUuids = stagingInvoices.filter(inv => inv.uuid);

                if (invoicesWithUuids.length > 0) {
                    console.log('Found invoices with UUIDs in staging_invoices:',
                        invoicesWithUuids.map(inv => ({
                            id: inv.id,
                            bqe_invoice_id: inv.bqe_invoice_id,
                            invoice_number: inv.invoice_number,
                            uuid: inv.uuid
                        }))
                    );

                    // Add to found invoices
                    foundInvoices = [...foundInvoices, ...invoicesWithUuids];
                } else {
                    console.log('Found invoices in staging_invoices but none have UUIDs');
                    // Add them anyway to maintain information
                    foundInvoices = [...foundInvoices, ...stagingInvoices];
                }
            }
        } catch (error) {
            console.error('Error querying staging_invoices:', error);
        }

        // 2. Try WP_SUBMISSION_STATUS table as a fallback
        try {
            // Build WHERE clause similarly
            let whereConditions = [];
            let replacements = {};

            if (invoice_number) {
                whereConditions.push('INVOICE_NO = :invoice_number');
                replacements.invoice_number = invoice_number;
            }

            if (invoice_id) {
                // Check if invoice_id is a UUID (contains hyphens) or an integer
                const isUuid = typeof invoice_id === 'string' && invoice_id.includes('-');
                if (isUuid) {
                    // If it's a UUID, only compare with UUID columns
                    whereConditions.push('DOC_ID = :invoice_id');
                } else {
                    // If it's an integer, compare with both ID and DOC_ID
                    whereConditions.push('(ID = :invoice_id OR DOC_ID = :invoice_id)');
                }
                replacements.invoice_id = invoice_id;
            }

            if (bqe_invoice_id) {
                whereConditions.push('DOC_ID = :bqe_invoice_id');
                replacements.bqe_invoice_id = bqe_invoice_id;
            }

            const whereClause = whereConditions.join(' OR ');

            // Query the WP_SUBMISSION_STATUS table
            const submissionStatusInvoices = await sequelize.query(
                `SELECT TOP 5 ID, DOC_ID, DOC_UUID, SUBMISSION_UUID, INVOICE_NO,
                        INVOICE_DATE, CREATE_TS, STATUS
                 FROM WP_SUBMISSION_STATUS
                 WHERE ${whereClause}
                 ORDER BY CREATE_TS DESC, ID DESC`,
                {
                    replacements,
                    type: sequelize.QueryTypes.SELECT
                }
            );

            console.log(`Found ${submissionStatusInvoices.length} invoices in WP_SUBMISSION_STATUS table`);

            if (submissionStatusInvoices && submissionStatusInvoices.length > 0) {
                // Filter for invoices with UUIDs
                const invoicesWithUuids = submissionStatusInvoices.filter(inv => inv.DOC_UUID);

                if (invoicesWithUuids.length > 0) {
                    console.log('Found invoices with UUIDs in WP_SUBMISSION_STATUS:',
                        invoicesWithUuids.map(inv => ({
                            id: inv.ID,
                            doc_id: inv.DOC_ID,
                            invoice_number: inv.INVOICE_NO,
                            uuid: inv.DOC_UUID
                        }))
                    );

                    // Map the submission status invoices to the same format as staging invoices
                    const mappedInvoices = invoicesWithUuids.map(inv => ({
                        id: inv.ID,
                        bqe_invoice_id: inv.DOC_ID,
                        invoice_number: inv.INVOICE_NO,
                        status: inv.STATUS,
                        uuid: inv.DOC_UUID,
                        submission_uuid: inv.SUBMISSION_UUID,
                        date_submitted: inv.CREATE_TS,
                        date_cancelled: null
                    }));

                    // Add to found invoices
                    foundInvoices = [...foundInvoices, ...mappedInvoices];
                }
            }
        } catch (error) {
            console.error('Error querying WP_SUBMISSION_STATUS:', error);
        }

        // Process and return results
        console.log(`Total invoices found across all tables: ${foundInvoices.length}`);

        if (foundInvoices.length === 0) {
            return res.json({
                success: false,
                message: 'No invoice found',
                invoice: null
            });
        }

        // Filter for invoices with UUIDs first
        const invoicesWithUuids = foundInvoices.filter(inv => inv.uuid);

        // Return the first invoice with a UUID if available, or just the first invoice
        const bestInvoice = invoicesWithUuids.length > 0 ? invoicesWithUuids[0] : foundInvoices[0];

        console.log('Returning best matched invoice:', bestInvoice);

        return res.json({
            success: true,
            message: 'Invoice found',
            invoice: {
                id: bestInvoice.id,
                bqe_invoice_id: bestInvoice.bqe_invoice_id,
                invoice_number: bestInvoice.invoice_number,
                status: bestInvoice.status,
                uuid: bestInvoice.uuid,
                submission_uuid: bestInvoice.submission_uuid,
                date_submitted: bestInvoice.date_submitted,
                date_cancelled: bestInvoice.date_cancelled
            }
        });

    } catch (error) {
        console.error('Error in direct UUID lookup:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to look up invoice UUIDs',
            details: error.message
        });
    }
});

// Add a debugging endpoint to manually set UUIDs
router.post('/debug-set-uuids', async (req, res) => {
    try {
        const { invoice_number, uuid, submission_uuid } = req.body;

        if (!invoice_number || !uuid) {
            return res.status(400).json({
                success: false,
                error: 'invoice_number and uuid are required'
            });
        }

        console.log('===== DEBUG: MANUALLY SETTING UUIDs =====');
        console.log('Values to set:', {
            invoice_number,
            uuid,
            submission_uuid: submission_uuid || uuid
        });

        // Update staging_invoices table
        const updateResult = await sequelize.query(
            `UPDATE staging_invoices
             SET uuid = :uuid,
                 submission_uuid = :submission_uuid,
                 updated_at = GETDATE()
             WHERE invoice_number = :invoice_number`,
            {
                replacements: {
                    uuid: uuid,
                    submission_uuid: submission_uuid || uuid,
                    invoice_number: invoice_number
                },
                type: sequelize.QueryTypes.UPDATE
            }
        );

        // Also add to WP_SUBMISSION_STATUS for redundancy
        await sequelize.query(
            `INSERT INTO WP_SUBMISSION_STATUS
             (DocNum, UUID, SubmissionUID, SubmissionStatus, DateTimeSent, DateTimeUpdated)
             VALUES
             (:doc_num, :uuid, :submission_uid, 'Submitted', GETDATE(), GETDATE())`,
            {
                replacements: {
                    doc_num: req.body.doc_id || 'manual-' + Date.now(),
                    uuid: uuid,
                    submission_uid: submission_uuid || uuid
                },
                type: sequelize.QueryTypes.INSERT
            }
        );

        // Verify the update
        const verifyResult = await sequelize.query(
            `SELECT id, bqe_invoice_id, invoice_number, status, uuid, submission_uuid, date_submitted
             FROM staging_invoices
             WHERE invoice_number = :invoice_number`,
            {
                replacements: {
                    invoice_number: invoice_number
                },
                type: sequelize.QueryTypes.SELECT
            }
        );

        console.log('===== DEBUG: UPDATE VERIFICATION =====');
        console.log('Updated records in staging_invoices:', JSON.stringify(verifyResult, null, 2));

        return res.json({
            success: true,
            message: 'UUIDs manually set for debugging',
            updateResult: verifyResult
        });

    } catch (error) {
        console.error('Error in debug-set-uuids:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to set UUIDs',
            details: error.message
        });
    }
});

module.exports = router;