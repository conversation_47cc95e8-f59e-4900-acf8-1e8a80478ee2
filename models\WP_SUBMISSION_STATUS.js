module.exports = (sequelize, DataTypes) => {
  const WP_SUBMISSION_STATUS = sequelize.define('WP_SUBMISSION_STATUS', {
    DocNum: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    UUID: {
      type: DataTypes.STRING
    },
    SubmissionUID: {
      type: DataTypes.STRING
    },
    SubmissionStatus: {
      type: DataTypes.STRING
    },
    DateTimeSent: {
      type: DataTypes.DATE
    },
    DateTimeUpdated: {
      type: DataTypes.DATE
    },
    RejectionDetails: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    FileName: {
      type: DataTypes.STRING
    }
  }, {
    tableName: 'WP_SUBMISSION_STATUS',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['DocNum']
      },
      {
        unique: true,
        fields: ['FileName']
      }
    ]
  });

  return WP_SUBMISSION_STATUS;
};
