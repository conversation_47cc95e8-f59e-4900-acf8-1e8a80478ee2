// Initialize profile page
async function initializeProfile() {
  try {
    // Initially hide edit mode and ensure view mode is visible
    const editMode = document.getElementById('editMode');
    const viewMode = document.getElementById('viewMode');
    
    if (editMode) {
      editMode.style.display = 'none';
      editMode.classList.remove('active');
    }
    if (viewMode) {
      viewMode.style.display = 'block';
    }

    console.log('Fetching profile details...');
    const response = await fetch('/api/getProfileDetails', {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    console.log('Response status:', response.status);
    
    if (!response.ok) {
      if (response.status === 401) {
        window.location.href = '/auth/login';
        return;
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Profile data:', data);

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch user details');
    }

    // Ensure data has the expected structure
    const sanitizedData = {
      user: data.user || {},
      company: data.company || {},
      success: data.success
    };

    // Update view mode with combined data
    updateViewMode(sanitizedData);
    // Update edit mode form values
    updateEditMode(sanitizedData);
    // Update profile image
    updateProfileImage(sanitizedData.company?.companyImage);

    // Set the username in the hidden field for the auth form
    const authUsernameInput = document.getElementById('authUsername');
    if (authUsernameInput && sanitizedData.user) {
      authUsernameInput.value = sanitizedData.user.username || '';
    }

  } catch (error) {
    console.error('Error initializing profile:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
    
    // Show error message to user
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: 'Failed to load profile data: ' + error.message
    });

    // Set default values to prevent UI breaking
    updateViewMode({
      user: {},
      company: {},
      success: false
    });
  }
}

function updateViewMode(data) {
  try {
    console.log('Updating view mode with data:', data);
    const { user = {}, company = {} } = data;
    
    // Update user details with fallbacks
    const elements = {
      '.profile-name': `<i class="bi bi-building"></i> ${company.companyName || 'N/A'}`,
      '.profile-email': `<i class="bi bi-envelope"></i> ${user.email || company.email || 'N/A'}`,
      '.profile-tin': `<i class="bi bi-upc"></i> ${company.tin || user.tin || 'N/A'}`,
      '.profile-brn': `<i class="bi bi-card-text"></i> ${company.brn || 'N/A'}`,
      '.profile-phone': `<i class="bi bi-telephone"></i> ${company.phone || 'N/A'}`,
      '.profile-address': `<i class="bi bi-geo-alt"></i> ${company.address || 'N/A'}`
    };

    // Safely update each element
    Object.entries(elements).forEach(([selector, value]) => {
      const element = document.querySelector(selector);
      if (element) {
        element.innerHTML = value;
      }
    });

    // Update company details
    if (company) {
      const companyElements = {
        '.companyName': company.companyName || 'N/A',
        '.industry': company.industry || 'N/A',
        '.country': company.country || 'N/A',
        '.phone': company.phone || 'N/A',
        '.address': company.address || 'N/A',
        '.about': company.about || 'N/A',
        '.tin': company.tin || 'N/A',
        '.brn': company.brn || 'N/A'
      };

      // Update each company element
      Object.entries(companyElements).forEach(([selector, value]) => {
        const element = document.querySelector(selector);
        if (element) {
          element.textContent = value;
        }
      });

      // Update email elements separately since they need special handling
      document.querySelectorAll('.email').forEach(el => {
        if (el) {
          el.textContent = company.email || user.email || 'N/A';
        }
      });
    }

    // Show/hide admin badge
    const adminBadge = document.querySelector('.admin-badge');
    if (adminBadge) {
      adminBadge.style.display = user.admin ? 'inline-block' : 'none';
    }
  } catch (error) {
    console.error('Error in updateViewMode:', error);
    // Set default values for critical UI elements
    const defaultElements = document.querySelectorAll(
      '.profile-name, .profile-email, .profile-tin, .profile-brn, .profile-phone, .profile-address, ' +
      '.companyName, .industry, .country, .email, .phone, .address, .about, .tin, .brn'
    );
    defaultElements.forEach(element => {
      if (element) {
        element.innerHTML = element.classList.contains('profile-') ? 
          '<i class="bi bi-exclamation-triangle"></i> N/A' : 
          'N/A';
      }
    });
  }
}

function updateEditMode(data) {
  console.log('Updating edit mode with data:', data);
  const { user, company } = data;
  
  // Basic Details Form
  const companyNameInput = document.getElementById('companyName');
  const industryInput = document.getElementById('industry');
  const countryInput = document.getElementById('country');
  
  if (companyNameInput) companyNameInput.value = company?.companyName || '';
  if (industryInput) industryInput.value = company?.industry || '';
  if (countryInput) countryInput.value = company?.country || '';
  
  // Contact Information Form
  const usernameInput = document.getElementById('username');
  const emailInput = document.getElementById('email');
  const phoneInput = document.getElementById('phone');
  const addressInput = document.getElementById('address');
  
  if (usernameInput) usernameInput.value = user.username || '';
  if (emailInput) emailInput.value = company?.email || user.email || '';
  if (phoneInput) phoneInput.value = company?.phone || '';
  if (addressInput) addressInput.value = company?.address || '';
  
  // About Company Form
  const aboutInput = document.getElementById('about');
  if (aboutInput) aboutInput.value = company?.about || '';

  // Registration Details Form
  const tinInput = document.getElementById('tin');
  const brnInput = document.getElementById('brn');
  const clientIdInput = document.getElementById('clientId');
  const clientSecretInput = document.getElementById('clientSecret');
  
  if (tinInput) tinInput.value = company?.tin || user.tin || '';
  if (brnInput) brnInput.value = company?.brn || '';
  if (clientIdInput) clientIdInput.value = user.clientId || '';
  if (clientSecretInput) clientSecretInput.value = user.clientSecret || '';
}

function updateProfileImage(logoUrl) {
  const profileImg = document.getElementById('profileImg');
  const editProfileImg = document.getElementById('editProfileImg');
  const defaultImage = '/assets/img/default-avatar.png';
  
  // Add base URL if the path is relative
  const fullLogoUrl = logoUrl?.startsWith('http') ? 
    logoUrl : 
    (logoUrl ? `${window.location.origin}${logoUrl}` : defaultImage);
  
  // Update both images
  [profileImg, editProfileImg].forEach(img => {
    if (img) {
      img.src = fullLogoUrl;
      img.onerror = () => {
        img.src = defaultImage;
      };
    }
  });
}

function showLoadingSpinner() {
  const editProfileImg = document.getElementById('editProfileImg');
  const uploadBtn = document.getElementById('uploadImageBtn');
  if (editProfileImg && uploadBtn) {
    // Add blur effect to image
    editProfileImg.style.filter = 'blur(2px)';
    // Disable upload button and show loading state
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
  }
}

function hideLoadingSpinner() {
  const editProfileImg = document.getElementById('editProfileImg');
  const uploadBtn = document.getElementById('uploadImageBtn');
  if (editProfileImg && uploadBtn) {
    // Remove blur effect
    editProfileImg.style.filter = '';
    // Re-enable upload button and restore text
    uploadBtn.disabled = false;
    uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload Image';
  }
}

// Handle image upload
async function handleImageUpload(file) {
  try {
    // Check if file exists and is a valid File object
    if (!file || !(file instanceof File)) {
      throw new Error('Invalid file object');
    }

    showLoadingSpinner();

    // Create FormData and append file
    const formData = new FormData();
    formData.append('companyLogo', file);

    const response = await fetch('/api/updateCompanyLogo', {
      method: 'POST',
      body: formData,
      credentials: 'include'
    });

    let data;
    try {
      data = await response.json();
      console.log('Response data:', data);
    } catch (e) {
      console.error('Error parsing response:', e);
      throw new Error('Server returned an invalid response');
    }

    if (!response.ok) {
      throw new Error(data.message || `Upload failed (${response.status}: ${response.statusText})`);
    }

    if (data.success && data.logoUrl) {
      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const imageUrl = data.logoUrl.startsWith('http') ? 
        data.logoUrl : 
        `${window.location.origin}${data.logoUrl}?t=${timestamp}`;

      // Update both profile images
      const profileImg = document.getElementById('profileImg');
      const editProfileImg = document.getElementById('editProfileImg');
      
      if (profileImg) {
        profileImg.src = imageUrl;
      }
      if (editProfileImg) {
        editProfileImg.src = imageUrl;
      }

      // Update the company logo in the navbar if it exists
      const navbarLogo = document.querySelector('.navbar-logo img');
      if (navbarLogo) {
        navbarLogo.src = imageUrl;
      }

      // Show success message with enhanced styling and feedback
      await Swal.fire({
        icon: 'success',
        title: 'Profile Saved Successfully',
        text: 'Your company logo has been updated successfully',
        timer: 2000,
        timerProgressBar: true,
        showConfirmButton: false,
        position: 'top-end',
        toast: true,
        customClass: {
          popup: 'animated fadeInRight'
        }
      });

      // Wait a bit before refreshing to ensure the server has processed the update
      setTimeout(async () => {
        await initializeProfile();
      }, 500);
    } else {
      throw new Error('Failed to update company logo');
    }
  } catch (error) {
    console.error('Error uploading image:', error);
    // Show error message with enhanced styling
    Swal.fire({
      icon: 'error',
      title: 'Upload Failed',
      text: error.message || 'Failed to upload image',
      timer: 2000,
      timerProgressBar: true,
      showConfirmButton: false,
      position: 'top-end',
      toast: true,
      customClass: {
        popup: 'animated fadeInRight'
      }
    });
  } finally {
    hideLoadingSpinner();
  }
}

// Setup event listeners
function setupEventListeners() {
  // Handle image upload button click
  const uploadButton = document.getElementById('uploadImageBtn');
  const fileInput = document.getElementById('imageUpload');

  if (uploadButton && fileInput) {
    uploadButton.addEventListener('click', () => {
      fileInput.click();
    });

    fileInput.addEventListener('change', async (e) => {
      const file = e.target.files[0];
      if (!file) {
        return; // User cancelled file selection
      }

      // Log file details
      console.log('Selected file:', {
        name: file.name,
        type: file.type,
        size: file.size
      });

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        Swal.fire({
          icon: 'error',
          title: 'Invalid File Type',
          text: 'Please upload only JPG, PNG or GIF images'
        });
        fileInput.value = ''; // Clear the file input
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        Swal.fire({
          icon: 'error',
          title: 'File Too Large',
          text: 'Image size should not exceed 5MB'
        });
        fileInput.value = ''; // Clear the file input
        return;
      }

      try {
        await handleImageUpload(file);
      } finally {
        fileInput.value = ''; // Clear the file input after upload attempt
      }
    });
  }

  // Handle main profile form submission
  const profileForm = document.getElementById('profileForm');
  if (profileForm) {
    profileForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      try {
        // Collect all form data
        const formData = {
          // Authentication details
          clientId: document.getElementById('clientId')?.value || '',
          clientSecret: document.getElementById('clientSecret')?.value || '',
          
          // Basic details
          companyName: document.getElementById('companyName')?.value || '',
          industry: document.getElementById('industry')?.value || '',
          country: document.getElementById('country')?.value || '',
          
          // Contact info
          email: document.getElementById('email')?.value || '',
          phone: document.getElementById('phone')?.value || '',
          address: document.getElementById('address')?.value || '',
          
          // About
          about: document.getElementById('about')?.value || ''
        };

        // Make all API calls in parallel
        await Promise.all([
          // Update authentication details
          fetch('/api/updateAuthDetails', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              clientId: formData.clientId,
              clientSecret: formData.clientSecret
            })
          }),
          
          // Update user email
          fetch('/api/updateUserDetails', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              email: formData.email
            })
          }),
          
          // Update company details
          fetch('/api/updateCompanyDetails', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              companyName: formData.companyName,
              industry: formData.industry,
              country: formData.country,
              phone: formData.phone,
              email: formData.email,
              address: formData.address,
              about: formData.about
            })
          })
        ]);

        
        // Switch back to view mode
        toggleEditMode();
        initialize()
        // Show success message with enhanced styling and feedback
        await Swal.fire({
          icon: 'success',
          title: 'Profile Saved Successfully',
          text: 'Your profile has been updated and all changes have been saved',
          timer: 2000,
          timerProgressBar: true,
          showConfirmButton: false,
          position: 'top-end',
          toast: true,
          customClass: {
            popup: 'animated fadeInRight'
          }
        });

  

      } catch (error) {
        console.error('Error saving profile changes:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: error.message || 'Failed to save changes'
        });
      }
    });
  }
}

// Initialize everything
async function initialize() {
  await initializeProfile();
  setupEventListeners();
  
  // Initialize tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

// Event Listeners
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// Remove profile image
async function removeImage() {
  try {
    const response = await fetch('/api/updateCompanyLogo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        companyImage: ''
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'Failed to remove image');
    }

    const profileImg = document.getElementById('profileImg');
    if (profileImg) {
      profileImg.src = '/assets/img/default-avatar.png';
    }

    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: 'Company logo removed successfully'
    });

  } catch (error) {
    console.error('Error removing image:', error);
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: error.message || 'Failed to remove image'
    });
  }
}

// Toggle edit mode
function toggleEditMode() {
  const viewMode = document.getElementById('viewMode');
  const editMode = document.getElementById('editMode');
  const editButton = document.getElementById('editButton');
  const uploadBtn = document.getElementById('uploadImageBtn');

  if (!viewMode || !editMode) return;

  const isEditMode = !editMode.classList.contains('active');

  // Toggle display and active class
  viewMode.style.display = isEditMode ? 'none' : 'block';
  editMode.style.display = isEditMode ? 'block' : 'none';
  
  if (isEditMode) {
    editMode.classList.add('active');
    if (editButton) {
      editButton.innerHTML = '<i class="fas fa-eye"></i> View Details';
    }
  } else {
    editMode.classList.remove('active');
    if (editButton) {
      editButton.innerHTML = '<i class="fas fa-edit"></i> Edit Details';
    }
  }
}

// Handle LHDN field edits
async function handleLHDNEdit(fieldType) {
  const messages = {
    tin: {
      title: 'Edit Tax Identification Number (TIN)',
      text: 'Changes to TIN must comply with LHDN requirements. This action will be logged and verified. Do you want to proceed?',
      inputLabel: 'New TIN',
      pattern: '^[0-9]{12}$',
      validationMessage: 'TIN must be exactly 12 digits'
    },
    brn: {
      title: 'Edit Business Registration Number (BRN)',
      text: 'Changes to BRN must comply with LHDN requirements. This action will be logged and verified. Do you want to proceed?',
      inputLabel: 'New BRN',
      pattern: '^[A-Z0-9]{9,12}$',
      validationMessage: 'BRN must be 9-12 alphanumeric characters'
    },
    clientId: {
      title: 'Edit Client ID',
      text: 'Changes to Client ID will affect your LHDN integration. This action will be logged. Do you want to proceed?',
      inputLabel: 'New Client ID',
      pattern: '^[A-Za-z0-9]{8,}$',
      validationMessage: 'Client ID must be at least 8 alphanumeric characters'
    },
    clientSecret: {
      title: 'Edit Client Secret',
      text: 'Changes to Client Secret will affect your LHDN integration. This action will be logged. Do you want to proceed?',
      inputLabel: 'New Client Secret',
      pattern: '^[A-Za-z0-9]{12,}$',
      validationMessage: 'Client Secret must be at least 12 alphanumeric characters'
    }
  };

  const field = messages[fieldType];
  
  // First confirmation
  const confirmResult = await Swal.fire({
    title: field.title,
    text: field.text,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#3085d6',
    cancelButtonColor: '#d33',
    confirmButtonText: 'Yes, proceed',
    cancelButtonText: 'Cancel'
  });

  if (!confirmResult.isConfirmed) {
    return;
  }

  // Input dialog
  const { value: newValue } = await Swal.fire({
    title: field.inputLabel,
    input: 'text',
    inputAttributes: {
      pattern: field.pattern,
      required: 'required'
    },
    inputValidator: (value) => {
      if (!value) {
        return 'This field cannot be empty!';
      }
      if (!new RegExp(field.pattern).test(value)) {
        return field.validationMessage;
      }
    },
    showCancelButton: true,
    confirmButtonText: 'Update',
    showLoaderOnConfirm: true,
    preConfirm: async (value) => {
      try {
        const response = await fetch('/api/updateLHDNDetails', {
      method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            fieldType,
            value
          })
        });

        const data = await response.json();
        if (!data.success) {
          throw new Error(data.message || 'Failed to update');
        }
        return data;
  } catch (error) {
        Swal.showValidationMessage(
          `Request failed: ${error.message}`
        );
      }
    },
    allowOutsideClick: () => !Swal.isLoading()
  });

  if (newValue) {
    // Update the field value
    const input = document.getElementById(fieldType);
    if (input) {
      input.value = fieldType.includes('client') ? '****************' : newValue;
    }
    
    await Swal.fire({
      icon: 'success',
      title: 'Updated Successfully',
      text: 'The changes have been saved and logged',
      timer: 1500
    });
  }
}

// After successful profile update
async function handleProfileUpdate(formData) {
  try {
    const response = await fetch('/api/updateProfile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'Failed to update profile');
    }

    // Refresh the navbar to show updated data
    if (typeof refreshNavbar === 'function') {
      await refreshNavbar();
    }

    showSuccessAlert('Profile updated successfully');
  } catch (error) {
    console.error('Error updating profile:', error);
    showErrorAlert(error.message);
  }
}
