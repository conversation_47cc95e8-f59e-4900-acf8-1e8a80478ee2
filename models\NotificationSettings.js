module.exports = (sequelize, DataTypes) => {
  const NotificationSettings = sequelize.define('NotificationSettings', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'WP_USER_REGISTRATION',
        key: 'ID'
      }
    },
    notificationEmail: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true
      }
    },
    emailNewInvoice: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    emailStatusUpdate: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    emailPaymentReceived: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    emailDailyDigest: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    browserNotifications: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    soundNotifications: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    alertDuration: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
      validate: {
        min: 1,
        max: 30
      }
    },
    mobileNumber: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    mobileVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    mobileVerificationCode: {
      type: DataTypes.STRING(6),
      allowNull: true
    },
    mobileVerificationExpires: {
      type: DataTypes.DATE,
      allowNull: true
    },
    smsNotifications: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    pushNotifications: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    quietHoursStart: {
      type: DataTypes.TIME,
      defaultValue: '22:00:00'
    },
    quietHoursEnd: {
      type: DataTypes.TIME,
      defaultValue: '08:00:00'
    },
    timezone: {
      type: DataTypes.STRING(50),
      defaultValue: 'Asia/Kuala_Lumpur'
    },
    workdaysOnly: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    tableName: 'notification_settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return NotificationSettings;
}; 