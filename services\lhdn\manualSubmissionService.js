const axios = require('axios');
const qs = require('querystring');
const { WP_CONFIGURATION } = require('../../models');

/**
 * Manual Submission Service following LHDN Integration Best Practices
 * This service is dedicated to manual submissions and follows LHDN SDK guidelines
 * Separate from BQE service to maintain clean separation of concerns
 */
class ManualSubmissionService {
  constructor() {
    // Configuration will be loaded from database
    this.baseUrl = null;
    this.clientId = null;
    this.clientSecret = null;
    this.environment = null;

    // Token cache following LHDN best practices (60-minute lifetime)
    this.tokenCache = {
      token: null,
      expiresAt: null,
      tin: null
    };

    // Rate limiting awareness
    this.rateLimits = {
      loginTaxpayer: { rpm: 12, lastCall: 0, calls: [] },
      submitDocuments: { rpm: 100, lastCall: 0, calls: [] },
      getSubmission: { rpm: 300, lastCall: 0, calls: [] }
    };
  }

  /**
   * Load LHDN configuration from database
   * Following the existing pattern used throughout the application
   */
  async loadConfig() {
    try {
      const config = await WP_CONFIGURATION.findOne({
        where: {
          Type: 'LHDN',
          IsActive: true
        },
        order: [['CreateTS', 'DESC']]
      });

      if (!config || !config.Settings) {
        throw new Error('LHDN configuration not found in database');
      }

      // Parse settings if it's a string
      let settings = typeof config.Settings === 'string' ? JSON.parse(config.Settings) : config.Settings;

      // Set configuration values
      this.environment = settings.environment || 'sandbox';
      this.clientId = settings.clientId;
      this.clientSecret = settings.clientSecret;

      // Set base URL based on environment
      if (this.environment === 'production') {
        this.baseUrl = settings.productionUrl || settings.middlewareUrl;
      } else {
        this.baseUrl = settings.sandboxUrl || settings.middlewareUrl || 'https://preprod-api.myinvois.hasil.gov.my';
      }

      if (!this.baseUrl) {
        throw new Error('LHDN API URL not configured in database');
      }

      if (!this.clientId || !this.clientSecret) {
        throw new Error('LHDN Client ID and Client Secret not configured in database');
      }

      console.log('[Manual Submission] Configuration loaded from database:');
      console.log('[Manual Submission] Environment:', this.environment);
      console.log('[Manual Submission] Base URL:', this.baseUrl);
      console.log('[Manual Submission] Client ID:', this.clientId ? `Present (${this.clientId.substring(0, 8)}...)` : 'Missing');
      console.log('[Manual Submission] Client Secret:', this.clientSecret ? 'Present' : 'Missing');

      return settings;
    } catch (error) {
      console.error('[Manual Submission] Error loading configuration from database:', error);
      throw new Error(`Failed to load LHDN configuration: ${error.message}`);
    }
  }

  /**
   * Check rate limit before making API calls
   * Following LHDN rate limiting guidelines
   */
  async checkRateLimit(apiType) {
    const now = Date.now();
    const limit = this.rateLimits[apiType];

    if (!limit) return true;

    // Remove calls older than 1 minute
    limit.calls = limit.calls.filter(callTime => now - callTime < 60000);

    // Check if we're at the limit
    if (limit.calls.length >= limit.rpm) {
      const oldestCall = Math.min(...limit.calls);
      const waitTime = 60000 - (now - oldestCall);

      if (waitTime > 0) {
        console.log(`[Manual Submission] Rate limit reached for ${apiType}, waiting ${waitTime}ms`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    // Record this call
    limit.calls.push(now);
    return true;
  }

  /**
   * DEPRECATED: Use BQE service getTokenAsTaxPayer instead
   * This method is kept for backward compatibility but redirects to BQE service
   */
  async getAuthToken(userTin, req = null) {
    console.warn('[DEPRECATED] ManualSubmissionService.getAuthToken is deprecated. Use BQE service getTokenAsTaxPayer instead.');

    if (!req) {
      throw new Error('Request context is required for token generation. Please use BQE service getTokenAsTaxPayer(req) instead.');
    }

    // Redirect to BQE service for consistent token generation
    const { getTokenAsTaxPayer } = require('../bqe/service');
    const tokenResponse = await getTokenAsTaxPayer(req);

    if (!tokenResponse?.access_token) {
      throw new Error('Failed to get authentication token from BQE service');
    }

    return tokenResponse.access_token;
  }

  /**
   * DEPRECATED: Use BQE service submitDocument instead
   * This method is kept for backward compatibility but redirects to BQE service
   */
  async submitDocuments(documents, userTin, req = null) {
    console.warn('[DEPRECATED] ManualSubmissionService.submitDocuments is deprecated. Use BQE service submitDocument instead.');

    if (!req) {
      throw new Error('Request context is required for document submission. Please use BQE service submitDocument instead.');
    }

    // Get token using BQE service
    const { getTokenAsTaxPayer, submitDocument } = require('../bqe/service');
    const tokenResponse = await getTokenAsTaxPayer(req);

    if (!tokenResponse?.access_token) {
      throw new Error('Failed to get authentication token from BQE service');
    }

    // Use BQE service's submitDocument function
    return await submitDocument(documents, tokenResponse.access_token);
  }

  /**
   * Poll submission status following LHDN best practices
   * Implements proper polling approach as recommended
   */
  async pollSubmissionStatus(submissionUID, userTin, maxAttempts = 20) {
    try {
      console.log('[Manual Submission] Starting polling for submission:', submissionUID);

      // Get authentication token
      const token = await this.getAuthToken(userTin);

      let attempts = 0;
      const baseDelay = 5000; // Start with 5 seconds as recommended

      while (attempts < maxAttempts) {
        attempts++;

        // Check rate limit
        await this.checkRateLimit('getSubmission');

        console.log(`[Manual Submission] Polling attempt ${attempts}/${maxAttempts}`);

        try {
          const response = await axios.get(
            `${this.baseUrl}/api/v1.0/documentsubmissions/${submissionUID}`,
            {
              params: { pageNo: 1, pageSize: 10 },
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );

          if (response.status === 200 && response.data) {
            const status = response.data.overallStatus || response.data.status;
            console.log('[Manual Submission] Current status:', status);

            // Check if processing is complete
            if (status && !status.toLowerCase().includes('progress')) {
              console.log('[Manual Submission] Processing complete with status:', status);
              return {
                success: true,
                status: status.toLowerCase(),
                data: response.data
              };
            }

            // Still in progress, wait before next attempt
            const delay = Math.min(baseDelay * Math.pow(1.5, attempts - 1), 30000); // Max 30 seconds
            console.log(`[Manual Submission] Status in progress, waiting ${delay}ms before next attempt`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }

        } catch (pollError) {
          console.error(`[Manual Submission] Polling attempt ${attempts} failed:`, pollError.message);

          // Handle rate limiting during polling
          if (pollError.response?.status === 429) {
            const retryAfter = pollError.response.headers['retry-after'] || 60;
            console.log(`[Manual Submission] Polling rate limited, waiting ${retryAfter}s`);
            await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
            continue; // Don't count this as an attempt
          }

          // Wait before retry on other errors
          await new Promise(resolve => setTimeout(resolve, baseDelay));
        }
      }

      // Max attempts reached
      console.log('[Manual Submission] Max polling attempts reached');
      return {
        success: false,
        error: 'Maximum polling attempts reached',
        status: 'timeout'
      };

    } catch (error) {
      console.error('[Manual Submission] Error during polling:', error.message);
      return {
        success: false,
        error: error.message,
        status: 'error'
      };
    }
  }

  /**
   * Clear token cache (useful for testing or forced refresh)
   */
  clearTokenCache() {
    this.tokenCache = {
      token: null,
      expiresAt: null,
      tin: null
    };
    console.log('[Manual Submission] Token cache cleared');
  }

  /**
   * Refresh configuration from database
   * Useful when configuration changes during runtime
   */
  async refreshConfig() {
    console.log('[Manual Submission] Refreshing configuration from database...');
    this.baseUrl = null;
    this.clientId = null;
    this.clientSecret = null;
    this.environment = null;

    // Clear token cache as well since configuration changed
    this.clearTokenCache();

    return await this.loadConfig();
  }

  /**
   * Get current configuration status
   */
  getConfigStatus() {
    return {
      configured: !!(this.baseUrl && this.clientId && this.clientSecret),
      environment: this.environment,
      baseUrl: this.baseUrl,
      hasClientId: !!this.clientId,
      hasClientSecret: !!this.clientSecret,
      tokenCached: !!this.tokenCache.token,
      tokenExpiry: this.tokenCache.expiresAt
    };
  }
}

module.exports = ManualSubmissionService;
