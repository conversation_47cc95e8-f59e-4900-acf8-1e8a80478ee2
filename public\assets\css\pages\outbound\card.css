/* Cards Container */
.cards-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.welcome-datetime {
    text-align: right;
    padding-left: 2rem;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  
  .current-time {
    font-size: 2rem;
    font-weight: 600;
    color: #fff;
    letter-spacing: 0.5px;
    text-shadow: none;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }
  
  .current-date {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    display: flex;
    align-items: center;
  }
  
  /* Icons in datetime */
  .current-time i,
  .current-date i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    opacity: 0.9;
  }

/* Base Card Styles */
.info-card {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 120px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Card Icon and Count Layout */
.card-info {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
}

/* Icon Styles */
.card-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

/* Count Info Styles */
.count-info {
    text-align: right;
}

.count-info h6 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.count-info .text-muted {
    font-size: 0.875rem;
    color: #6c757d !important;
}

/* Card Title */
.card-title-new {
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: auto;
}

/* Card Type Specific Colors */
.total-invoice-card {
    border-top: 4px solid #4154f1;
}
.total-invoice-card .card-icon {
    color: #4154f1;
    background: rgba(65, 84, 241, 0.1);
}
.total-invoice-card .count-info h6,
.total-invoice-card .card-title-new {
    color: #4154f1;
}

.total-submitted-card {
    border-top: 4px solid #2eca6a;
}
.total-submitted-card .card-icon {
    color: #2eca6a;
    background: rgba(46, 202, 106, 0.1);
}
.total-submitted-card .count-info h6,
.total-submitted-card .card-title-new {
    color: #2eca6a;
}

.total-rejected-card {
    border-top: 4px solid #dc3545;
}
.total-rejected-card .card-icon {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}
.total-rejected-card .count-info h6,
.total-rejected-card .card-title-new {
    color: #dc3545;
}

.total-cancelled-card {
    border-top: 4px solid #ff771d;
}
.total-cancelled-card .card-icon {
    color: #ff771d;
    background: rgba(255, 119, 29, 0.1);
}
.total-cancelled-card .count-info h6,
.total-cancelled-card .card-title-new {
    color: #ff771d;
}

.total-pending-card {
    border-top: 4px solid #ffce3a;
}
.total-pending-card .card-icon {
    color: #ffce3a;
    background: rgba(255, 206, 58, 0.1);
}
.total-pending-card .count-info h6,
.total-pending-card .card-title-new {
    color: #ffce3a;
}

/* Loading Spinner */
.loading-spinner {
    width: 2rem;
    height: 2rem;
    border-width: 0.2rem;
    margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cards-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .cards-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .cards-container {
        grid-template-columns: 1fr;
    }
}

/* Card Hover Effect */
.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
}
