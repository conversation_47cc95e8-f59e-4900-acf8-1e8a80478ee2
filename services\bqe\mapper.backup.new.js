const fs = require('fs');
const path = require('path');
const { getCertificatesHashedParams } = require('./service');
const moment = require('moment');

/**
 * Logger configuration for mapping process
 */
const createLogger = () => {
  const logs = {
    steps: [],
    mappings: [],
    errors: []
  };

  const logStep = (step, data) => {
    logs.steps.push({
      timestamp: new Date().toISOString(),
      step,
      data
    });
  };

  const logMapping = (section, input, output) => {
    logs.mappings.push({
      timestamp: new Date().toISOString(),
      section,
      input,
      output
    });
  };

  const logError = (error, context) => {
    logs.errors.push({
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      context
    });
  };

  const writeLogs = (invoiceNo, lhdnFormat) => {
    try {
      const logsDir = path.join(process.cwd(), 'logs', 'lhdn');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }

      // Sanitize invoice number for filenames
      const safeInvoiceNo = invoiceNo.replace(/[\/\\:*?"<>|]/g, '_');

      // Write processing logs
      const processLogFileName = `lhdn_process_${safeInvoiceNo}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      const processLogPath = path.join(logsDir, processLogFileName);
      fs.writeFileSync(processLogPath, JSON.stringify(logs, null, 2));
      console.log(`[INFO] LHDN Processing logs written to: ${processLogPath}`);

      // Write LHDN format JSON
      const lhdnFileName = `lhdn_output_${safeInvoiceNo}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      const lhdnPath = path.join(logsDir, lhdnFileName);
      fs.writeFileSync(lhdnPath, JSON.stringify(lhdnFormat, null, 2));
      console.log(`[INFO] LHDN Output JSON written to: ${lhdnPath}`);
    } catch (error) {
      console.error('[ERROR] Failed to write LHDN logs:', error);
    }
  };

  return {
    logStep,
    logMapping,
    logError,
    writeLogs,
    getLogs: () => logs
  };
};

// Helper functions
const convertToBoolean = (value) => {
  if (value === true || value === 'true' || value === 1) return true;
  if (value === false || value === 'false' || value === 0) return false;
  return false; // default to false if undefined/null
};

const wrapValue = (value, currencyID = null) => {
  // For currency amounts, keep as numbers or use 0 if invalid
  if (currencyID) {
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      return [{
        "_": numValue, // Ensure it's a number, not a string
        "currencyID": currencyID
      }];
    }
    // Return 0 instead of undefined for invalid numbers
    return [{
      "_": 0,
      "currencyID": currencyID
    }];
  }

  // For non-currency fields, convert null/undefined to empty string
  if (value === null || value === undefined || value === '') {
    return [{
      "_": ""
    }];
  }

  // Convert everything else to string
  return [{
    "_": String(value)
  }];
};

const wrapBoolean = (value) => {
  return [{
    "_": convertToBoolean(value)
  }];
};

const formatDate = (date) => {
  if (!date) {
    // Return current date in YYYY-MM-DD format
    return moment().format('YYYY-MM-DD');
  }

  try {
    return moment(date).format('YYYY-MM-DD');
  } catch (error) {
    console.error('Error formatting date:', error);
    return moment().format('YYYY-MM-DD');
  }
};

const formatTime = (time) => {
  if (!time) {
    // Return current time in HH:mm:ssZ format
    return moment().format('HH:mm:ss') + 'Z';
  }

  try {
    return moment(time).format('HH:mm:ss') + 'Z';
  } catch (error) {
    console.error('Error formatting time:', error);
    return moment().format('HH:mm:ss') + 'Z';
  }
};

const mapAddressLines = (line) => {
  if (!line) {
    return [{ "Line": [{ "_": "" }] }];
  }

  // Split the address line by commas or line breaks
  const lines = line.split(/[,\n]/).map(l => l.trim()).filter(l => l);

  // Ensure we have at least one line
  if (lines.length === 0) {
    return [{ "Line": [{ "_": "" }] }];
  }

  // Map each line to the required format
  const mappedLines = lines.map(l => ({
    "Line": [{ "_": l }]
  }));

  return mappedLines;
};

const mapPartyIdentifications = (identifications = []) => {
  const requiredTypes = ['TIN', 'BRN', 'SST', 'TTX'];

  const idMap = identifications.reduce((acc, id) => {
    if (id && id.schemeId) {
      acc[id.schemeId] = id.id || "NA";
    }
    return acc;
  }, {});

  return requiredTypes.map(schemeId => ({
    "ID": [{
      "_": idMap[schemeId] || "NA",
      "schemeID": schemeId,
    }]
  }));
};

const mapPartyAddress = (address) => {
  if (!address) {
    return {
      "CityName": wrapValue(""),
      "PostalZone": wrapValue(""),
      "CountrySubentityCode": wrapValue(""),
      "AddressLine": mapAddressLines(""),
      "Country": [{
        "IdentificationCode": [{
          "_": "MYS",
          "listID": "ISO3166-1",
          "listAgencyID": "6"
        }]
      }]
    };
  }

  // Use formattedAddress if available, otherwise use line
  const addressLine = address.formattedAddress || address.line || "";

  return {
    "CityName": wrapValue(address.city || ""),
    "PostalZone": wrapValue(address.postcode || ""),
    "CountrySubentityCode": wrapValue(address.state || ""),
    "AddressLine": mapAddressLines(addressLine),
    "Country": [{
      "IdentificationCode": [{
        "_": "MYS",
        "listID": "ISO3166-1",
        "listAgencyID": "6"
      }]
    }]
  };
};

// Clean up the format by removing undefined values
const cleanObject = (obj) => {
  if (!obj) return obj;

  if (Array.isArray(obj)) {
    return obj
      .map(item => cleanObject(item))
      .filter(item => item !== null && item !== undefined);
  }

  if (typeof obj === 'object') {
    const cleaned = Object.entries(obj)
      .reduce((acc, [key, value]) => {
        const cleanedValue = cleanObject(value);
        if (cleanedValue !== null && cleanedValue !== undefined) {
          // Handle empty arrays
          if (Array.isArray(cleanedValue) && cleanedValue.length === 0) {
            return acc;
          }
          acc[key] = cleanedValue;
        }
        return acc;
      }, {});

    // If the object is empty after cleaning, return null
    return Object.keys(cleaned).length === 0 ? null : cleaned;
  }

  return obj;
};

// Check if a value is empty (null, undefined, empty string, empty array, empty object)
const isEmptyValue = (value) => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string' && value.trim() === '') return true;
  if (Array.isArray(value) && value.length === 0) return true;
  if (typeof value === 'object' && Object.keys(value).length === 0) return true;
  return false;
};

// Remove empty fields from an object
const cleanEmptyFields = (obj) => {
  if (!obj) return obj;

  if (Array.isArray(obj)) {
    return obj
      .map(item => cleanEmptyFields(item))
      .filter(item => !isEmptyValue(item));
  }

  if (typeof obj === 'object') {
    const cleaned = Object.entries(obj)
      .reduce((acc, [key, value]) => {
        const cleanedValue = cleanEmptyFields(value);
        if (!isEmptyValue(cleanedValue)) {
          acc[key] = cleanedValue;
        }
        return acc;
      }, {});

    return Object.keys(cleaned).length === 0 ? null : cleaned;
  }

  return obj;
};

/**
 * Maps BQE data to LHDN format following the LHDN SDK sample for foreign currency
 * @param {Object} bqeData - The processed BQE data
 * @param {string} version - The LHDN format version
 * @returns {Object} - The LHDN formatted data
 */
const mapBQEToLHDNFormat = (bqeData, version) => {
  const logger = createLogger();

  try {
    console.log('Starting mapping to LHDN format...');

    if (!bqeData || !bqeData._rawInvoice) {
      const error = new Error('No document data provided');
      logger.logError(error, { bqeData });
      throw error;
    }

    // Log the raw invoice data for debugging
    console.log('Raw invoice data:', JSON.stringify(bqeData._rawInvoice, null, 2));

    // Extract basic invoice data
    const invoice = bqeData._rawInvoice;
    const supplier = bqeData.supplier || {};
    const buyer = bqeData.buyer || {};

    // Get version value
    const versionValue = version || '1.0';

    // Get date and time
    const date = formatDate(invoice.date);
    const time = formatTime(invoice.date);

    // Get invoice period
    const invoiceFrom = formatDate(invoice.fromDate || invoice.date);
    const invoiceTo = formatDate(invoice.toDate || invoice.date);

    // Get currency information
    const currencyInfo = bqeData.currency_info || {};
    const currency = currencyInfo.currency || 'MYR';
    const isForeignCurrency = currency !== 'MYR';
    const exchangeRate = currencyInfo.exchangeRate || 1;

    console.log('Currency information:', {
      currency,
      isForeignCurrency,
      exchangeRate
    });

    // Get tax information
    const taxInfo = bqeData.tax_info || {};
    const taxRate = taxInfo.originalTaxRate || 0;

    // Calculate amounts
    const invoiceAmount = invoice.amount || 0;
    const taxableAmount = taxInfo.taxableAmount || invoiceAmount;
    const taxAmount = taxInfo.taxAmount || 0;

    // For foreign currency, calculate MYR amounts
    const taxableAmountMYR = isForeignCurrency ? taxableAmount * exchangeRate : taxableAmount;
    const taxAmountMYR = isForeignCurrency ? taxAmount * exchangeRate : taxAmount;

    console.log('Amount calculations:', {
      invoiceAmount,
      taxableAmount,
      taxAmount,
      taxableAmountMYR,
      taxAmountMYR
    });

    // Create tax category
    const taxCategory = {
      "ID": [{ "_": "01" }],
      "TaxScheme": [{
        "ID": [{
          "_": "OTH",
          "schemeID": "UN/ECE 5153",
          "schemeAgencyID": "6"
        }]
      }]
    };

    // Create TaxTotal section
    const taxTotal = [{
      "TaxAmount": [{
        "_": isForeignCurrency ? taxAmountMYR : taxAmount,
        "currencyID": isForeignCurrency ? "MYR" : currency
      }],
      "TaxSubtotal": [{
        "TaxableAmount": [{
          "_": taxableAmount,
          "currencyID": currency
        }],
        "TaxAmount": [{
          "_": isForeignCurrency ? taxAmountMYR : taxAmount,
          "currencyID": isForeignCurrency ? "MYR" : currency
        }],
        "TaxCategory": [taxCategory]
      }]
    }];

    // Create LegalMonetaryTotal section
    const legalMonetaryTotal = [{
      "LineExtensionAmount": wrapValue(taxableAmount, currency),
      "TaxExclusiveAmount": wrapValue(taxableAmount, currency),
      "TaxInclusiveAmount": wrapValue(invoiceAmount, currency),
      "AllowanceTotalAmount": wrapValue(0, currency),
      "ChargeTotalAmount": wrapValue(0, currency),
      "PayableRoundingAmount": wrapValue(0, currency),
      "PayableAmount": wrapValue(invoiceAmount, currency)
    }];

    // Process line items
    const lineItems = bqeData.line_items || [];

    // Log line items for debugging
    console.log('Line items to process:', lineItems.map(item => ({
      id: item.id,
      description: item.description,
      amount: item.amount,
      classifications: item.classifications
    })));

    // Create invoice lines
    const invoiceLines = lineItems.map((item, index) => {
      const lineItemAmount = item.amount || 0;
      const lineItemTaxRate = item.tax?.rate || taxRate;
      const lineItemTaxAmount = lineItemAmount * lineItemTaxRate / 100;

      // For foreign currency, calculate MYR amounts
      const lineItemAmountMYR = isForeignCurrency ? lineItemAmount * exchangeRate : lineItemAmount;
      const lineItemTaxAmountMYR = isForeignCurrency ? lineItemTaxAmount * exchangeRate : lineItemTaxAmount;

      return {
        "ID": [{ "_": (index + 1).toString() }],
        "InvoicedQuantity": [{
          "_": item.quantity || 1,
          "unitCode": "C62"
        }],
        "LineExtensionAmount": [{
          "_": lineItemAmount,
          "currencyID": currency
        }],
        "TaxTotal": [{
          "TaxAmount": [{
            "_": isForeignCurrency ? lineItemTaxAmountMYR : lineItemTaxAmount,
            "currencyID": isForeignCurrency ? "MYR" : currency
          }],
          "TaxSubtotal": [{
            "TaxableAmount": [{
              "_": lineItemAmount,
              "currencyID": currency
            }],
            "TaxAmount": [{
              "_": isForeignCurrency ? lineItemTaxAmountMYR : lineItemTaxAmount,
              "currencyID": isForeignCurrency ? "MYR" : currency
            }],
            "Percent": [{
              "_": lineItemTaxRate
            }],
            "TaxCategory": [taxCategory]
          }]
        }],
        "Item": [{
          "Description": [{ "_": item.description || "" }],
          "CommodityClassification": [
            {
              "ItemClassificationCode": [{
                "_": "9800.00.0010",
                "listID": "PTC"
              }]
            },
            {
              "ItemClassificationCode": [{
                "_": item.classifications?.invoice || "022",
                "listID": "CLASS"
              }]
            }
          ],
          "OriginCountry": [{
            "IdentificationCode": [{ "_": "MYS" }]
          }]
        }],
        "Price": [{
          "PriceAmount": [{
            "_": item.unitPrice || lineItemAmount,
            "currencyID": currency
          }]
        }],
        "ItemPriceExtension": [{
          "Amount": [{
            "_": lineItemAmount,
            "currencyID": currency
          }]
        }]
      };
    });

    // If no line items, create a default one
    const finalInvoiceLines = invoiceLines.length > 0 ? invoiceLines : [{
      "ID": [{ "_": "1" }],
      "InvoicedQuantity": [{
        "_": 1,
        "unitCode": "C62"
      }],
      "LineExtensionAmount": [{
        "_": taxableAmount,
        "currencyID": currency
      }],
      "TaxTotal": [{
        "TaxAmount": [{
          "_": isForeignCurrency ? taxAmountMYR : taxAmount,
          "currencyID": isForeignCurrency ? "MYR" : currency
        }],
        "TaxSubtotal": [{
          "TaxableAmount": [{
            "_": taxableAmount,
            "currencyID": currency
          }],
          "TaxAmount": [{
            "_": isForeignCurrency ? taxAmountMYR : taxAmount,
            "currencyID": isForeignCurrency ? "MYR" : currency
          }],
          "Percent": [{
            "_": taxRate
          }],
          "TaxCategory": [taxCategory]
        }]
      }],
      "Item": [{
        "Description": [{ "_": invoice.description || "" }],
        "CommodityClassification": [
          {
            "ItemClassificationCode": [{
              "_": "9800.00.0010",
              "listID": "PTC"
            }]
          },
          {
            "ItemClassificationCode": [{
              "_": "022",
              "listID": "CLASS"
            }]
          }
        ],
        "OriginCountry": [{
          "IdentificationCode": [{ "_": "MYS" }]
        }]
      }],
      "Price": [{
        "PriceAmount": [{
          "_": taxableAmount,
          "currencyID": currency
        }]
      }],
      "ItemPriceExtension": [{
        "Amount": [{
          "_": taxableAmount,
          "currencyID": currency
        }]
      }]
    }];

    // Create the final LHDN format
    const lhdnFormat = {
      "_D": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
      "_A": "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
      "_B": "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
      "Invoice": [{
        "ID": [{ "_": invoice.invoiceNumber || "" }],
        "IssueDate": [{ "_": date }],
        "IssueTime": [{ "_": time }],
        "InvoiceTypeCode": [{
          "_": invoice.EinvoiceTypeCode || "01",
          "listVersionID": versionValue
        }],
        "DocumentCurrencyCode": [{ "_": currency }],
        "TaxCurrencyCode": [{ "_": isForeignCurrency ? "MYR" : currency }],
        "InvoicePeriod": [{
          "StartDate": [{ "_": invoiceFrom }],
          "EndDate": [{ "_": invoiceTo }],
          "Description": [{ "_": invoice.periodDescription || "" }]
        }],
        "AccountingSupplierParty": [{
          "AdditionalAccountID": [{
            "_": supplier.certExId || "",
            "schemeAgencyName": "CertEX"
          }],
          "Party": [{
            "IndustryClassificationCode": [{
              "_": supplier.msicCode || "",
              "name": supplier.businessActivity || ""
            }],
            "PartyIdentification": mapPartyIdentifications([
              { id: supplier.tin, schemeId: 'TIN' },
              { id: supplier.registrationNumber, schemeId: 'BRN' },
              { id: supplier.sstId, schemeId: 'SST' },
              { id: supplier.ttxId, schemeId: 'TTX' }
            ]),
            "PostalAddress": [mapPartyAddress(supplier.address)],
            "PartyLegalEntity": [{
              "RegistrationName": [{ "_": supplier.name || "" }]
            }],
            "Contact": [{
              "Telephone": [{ "_": supplier.phone || "" }],
              "ElectronicMail": [{ "_": supplier.email || "" }]
            }]
          }]
        }],
        "AccountingCustomerParty": [{
          "Party": [{
            "PartyIdentification": mapPartyIdentifications([
              { id: buyer.tin, schemeId: 'TIN' },
              { id: buyer.registrationNumber, schemeId: 'BRN' },
              { id: buyer.sstId, schemeId: 'SST' },
              { id: buyer.ttxId, schemeId: 'TTX' }
            ]),
            "PostalAddress": [mapPartyAddress(buyer.address)],
            "PartyLegalEntity": [{
              "RegistrationName": [{ "_": buyer.name || "" }]
            }],
            "Contact": [{
              "Telephone": [{ "_": buyer.phone || "" }],
              "ElectronicMail": [{ "_": buyer.email || "" }]
            }]
          }]
        }],
        "PaymentMeans": [{
          "PaymentMeansCode": [{ "_": "03" }],
          "PayeeFinancialAccount": [{
            "ID": [{ "_": invoice.paymentInfo || "" }]
          }]
        }],
        "PaymentTerms": [{
          "Note": [{ "_": invoice.messageOnInvoice || "" }]
        }],
        "TaxTotal": taxTotal,
        "LegalMonetaryTotal": legalMonetaryTotal,
        "InvoiceLine": finalInvoiceLines,
        ...(isForeignCurrency ? {
          "TaxExchangeRate": [{
            "SourceCurrencyCode": [{ "_": currency }],
            "TargetCurrencyCode": [{ "_": "MYR" }],
            "CalculationRate": [{ "_": Number(exchangeRate) }]
          }]
        } : {})
      }]
    };

    // Log the raw format before cleaning for debugging
    if (isForeignCurrency) {
      console.log('Raw LHDN format for foreign currency before cleaning:', {
        currency,
        isForeignCurrency,
        exchangeRate,
        hasTaxExchangeRate: !!lhdnFormat.Invoice[0].TaxExchangeRate,
        taxCurrencyCode: lhdnFormat.Invoice[0].TaxCurrencyCode?.[0]?._,
        documentCurrencyCode: lhdnFormat.Invoice[0].DocumentCurrencyCode?.[0]?._,
        taxExchangeRateSourceCurrency: lhdnFormat.Invoice[0].TaxExchangeRate?.[0]?.SourceCurrencyCode?.[0]?._,
        taxExchangeRateTargetCurrency: lhdnFormat.Invoice[0].TaxExchangeRate?.[0]?.TargetCurrencyCode?.[0]?._,
        taxExchangeRateCalculationRate: lhdnFormat.Invoice[0].TaxExchangeRate?.[0]?.CalculationRate?.[0]?._,
        taxTotalCurrency: lhdnFormat.Invoice[0].TaxTotal?.[0]?.TaxAmount?.[0]?.currencyID,
        taxTotalAmount: lhdnFormat.Invoice[0].TaxTotal?.[0]?.TaxAmount?.[0]?._
      });
    }

    // Clean up the format by removing undefined values
    const cleanedFormat = cleanObject(lhdnFormat);

    // Log the final format for debugging
    if (isForeignCurrency) {
      console.log('Final LHDN format for foreign currency after cleaning:', {
        hasTaxExchangeRate: !!cleanedFormat.Invoice[0].TaxExchangeRate,
        taxCurrencyCode: cleanedFormat.Invoice[0].TaxCurrencyCode?.[0]?._,
        documentCurrencyCode: cleanedFormat.Invoice[0].DocumentCurrencyCode?.[0]?._,
        taxExchangeRateSourceCurrency: cleanedFormat.Invoice[0].TaxExchangeRate?.[0]?.SourceCurrencyCode?.[0]?._,
        taxExchangeRateTargetCurrency: cleanedFormat.Invoice[0].TaxExchangeRate?.[0]?.TargetCurrencyCode?.[0]?._,
        taxExchangeRateCalculationRate: cleanedFormat.Invoice[0].TaxExchangeRate?.[0]?.CalculationRate?.[0]?._
      });
    }

    // Write logs
    logger.writeLogs(invoice.invoiceNumber || 'unknown', cleanedFormat);
    console.log('Mapped to LHDN format successfully');

    return cleanedFormat;
  } catch (error) {
    logger.logError(error, { bqeData });
    console.error('Error mapping BQE to LHDN format:', error);
    throw error;
  }
};

module.exports = {
  mapBQEToLHDNFormat,
  cleanObject,
  cleanEmptyFields,
  isEmptyValue
};