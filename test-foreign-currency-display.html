<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foreign Currency Display Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/assets/css/pages/inbound.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fff;
        }
        .test-title {
            color: #333;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #007bff;
        }
        .currency-demo {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            align-items: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-5">Foreign Currency Display Test</h1>
        
        <!-- Currency Column Test -->
        <div class="test-section">
            <h2 class="test-title">Currency Column Display</h2>
            <p class="text-muted">Testing the currency column display for both foreign and local currencies.</p>
            
            <div class="currency-demo">
                <strong>Foreign Currency (USD):</strong>
                <div class="currency-info-wrapper" style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 4px;
                ">
                    <span class="currency-badge foreign" style="
                        display: inline-flex;
                        align-items: center;
                        gap: 4px;
                        padding: 6px 10px;
                        border-radius: 6px;
                        font-size: 0.85rem;
                        font-weight: 600;
                        background-color: #ffc107;
                        color: #000;
                        border: 1px solid rgba(255, 193, 7, 0.3);
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    ">
                        <i class="bi bi-currency-exchange"></i>
                        USD
                    </span>
                    <span class="currency-label" style="
                        font-size: 0.65rem;
                        color: #6c757d;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">
                        Foreign
                    </span>
                </div>
            </div>
            
            <div class="currency-demo">
                <strong>Local Currency (MYR):</strong>
                <div class="currency-info-wrapper" style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 4px;
                ">
                    <span class="currency-badge local" style="
                        display: inline-flex;
                        align-items: center;
                        gap: 4px;
                        padding: 6px 10px;
                        border-radius: 6px;
                        font-size: 0.85rem;
                        font-weight: 500;
                        background-color: #6c757d;
                        color: #fff;
                        border: 1px solid rgba(108, 117, 125, 0.3);
                    ">
                        <i class="bi bi-cash"></i>
                        MYR
                    </span>
                    <span class="currency-label" style="
                        font-size: 0.65rem;
                        color: #6c757d;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">
                        Local
                    </span>
                </div>
            </div>
        </div>

        <!-- Total Sales Currency Badge Test -->
        <div class="test-section">
            <h2 class="test-title">Total Sales with Currency Badge</h2>
            <p class="text-muted">Testing the total sales display with currency badges.</p>
            
            <div class="currency-demo">
                <strong>Foreign Currency Amount (USD):</strong>
                <div class="total-amount-wrapper" style="
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    flex-direction: column;
                    gap: 4px;
                ">
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <span class="total-amount" style="
                            font-weight: 500;
                            color: #1e40af;
                            font-family: 'SF Mono', SFMono-Regular, ui-monospace, monospace;
                            background: rgba(30, 64, 175, 0.1);
                            padding: 4px 8px;
                            border-radius: 4px;
                            display: inline-block;
                            letter-spacing: 0.5px;
                            white-space: nowrap;
                            transition: all 0.2s ease;
                        ">
                            1,234.56
                        </span>
                        <span class="currency-badge" style="
                            display: inline-flex;
                            align-items: center;
                            padding: 2px 6px;
                            border-radius: 4px;
                            font-size: 0.75rem;
                            font-weight: 600;
                            background-color: #ffc107;
                            color: #000;
                            border: 1px solid rgba(255, 193, 7, 0.3);
                        ">
                            USD
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="currency-demo">
                <strong>Local Currency Amount (MYR):</strong>
                <div class="total-amount-wrapper" style="
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    flex-direction: column;
                    gap: 4px;
                ">
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <span class="total-amount" style="
                            font-weight: 500;
                            color: #1e40af;
                            font-family: 'SF Mono', SFMono-Regular, ui-monospace, monospace;
                            background: rgba(30, 64, 175, 0.1);
                            padding: 4px 8px;
                            border-radius: 4px;
                            display: inline-block;
                            letter-spacing: 0.5px;
                            white-space: nowrap;
                            transition: all 0.2s ease;
                        ">
                            5,678.90
                        </span>
                        <span class="currency-badge" style="
                            display: inline-flex;
                            align-items: center;
                            padding: 2px 6px;
                            border-radius: 4px;
                            font-size: 0.75rem;
                            font-weight: 500;
                            background-color: #6c757d;
                            color: #fff;
                        ">
                            MYR
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Data Test -->
        <div class="test-section">
            <h2 class="test-title">Sample Currency Codes</h2>
            <p class="text-muted">Various foreign currency examples that should be supported.</p>
            
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="currency-demo">
                        <div class="currency-info-wrapper" style="display: flex; flex-direction: column; align-items: center; gap: 4px;">
                            <span class="currency-badge foreign" style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 10px; border-radius: 6px; font-size: 0.85rem; font-weight: 600; background-color: #ffc107; color: #000; border: 1px solid rgba(255, 193, 7, 0.3); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                <i class="bi bi-currency-exchange"></i>USD
                            </span>
                            <span class="currency-label" style="font-size: 0.65rem; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px;">Foreign</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="currency-demo">
                        <div class="currency-info-wrapper" style="display: flex; flex-direction: column; align-items: center; gap: 4px;">
                            <span class="currency-badge foreign" style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 10px; border-radius: 6px; font-size: 0.85rem; font-weight: 600; background-color: #ffc107; color: #000; border: 1px solid rgba(255, 193, 7, 0.3); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                <i class="bi bi-currency-exchange"></i>EUR
                            </span>
                            <span class="currency-label" style="font-size: 0.65rem; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px;">Foreign</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="currency-demo">
                        <div class="currency-info-wrapper" style="display: flex; flex-direction: column; align-items: center; gap: 4px;">
                            <span class="currency-badge foreign" style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 10px; border-radius: 6px; font-size: 0.85rem; font-weight: 600; background-color: #ffc107; color: #000; border: 1px solid rgba(255, 193, 7, 0.3); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                <i class="bi bi-currency-exchange"></i>SGD
                            </span>
                            <span class="currency-label" style="font-size: 0.65rem; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px;">Foreign</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="currency-demo">
                        <div class="currency-info-wrapper" style="display: flex; flex-direction: column; align-items: center; gap: 4px;">
                            <span class="currency-badge foreign" style="display: inline-flex; align-items: center; gap: 4px; padding: 6px 10px; border-radius: 6px; font-size: 0.85rem; font-weight: 600; background-color: #ffc107; color: #000; border: 1px solid rgba(255, 193, 7, 0.3); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                <i class="bi bi-currency-exchange"></i>IDR
                            </span>
                            <span class="currency-label" style="font-size: 0.65rem; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px;">Foreign</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
