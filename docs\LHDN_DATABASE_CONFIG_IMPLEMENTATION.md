# LHDN Database Configuration Implementation

## Overview

I have successfully updated the ManualSubmissionService to use database configuration instead of environment variables, following the existing pattern used throughout the application.

## Changes Made

### 1. Updated ManualSubmissionService (`services/lhdn/manualSubmissionService.js`)

**Before:**
```javascript
constructor() {
  this.baseUrl = process.env.PREPROD_BASE_URL || 'https://preprod-api.myinvois.hasil.gov.my';
  this.clientId = process.env.CLIENT_ID;
  this.clientSecret = process.env.CLIENT_SECRET;
}
```

**After:**
```javascript
constructor() {
  // Configuration will be loaded from database
  this.baseUrl = null;
  this.clientId = null;
  this.clientSecret = null;
  this.environment = null;
}
```

### 2. Added Database Configuration Loading

**New Method: `loadConfig()`**
- Queries `WP_CONFIGURATION` table with `Type = 'LHDN'` and `IsActive = true`
- Parses JSON settings from database
- Sets configuration values based on environment (sandbox/production)
- Validates required fields (clientId, clientSecret, baseUrl)
- Provides detailed logging for debugging

**Configuration Mapping:**
- `environment`: From `settings.environment` (defaults to 'sandbox')
- `clientId`: From `settings.clientId`
- `clientSecret`: From `settings.clientSecret`
- `baseUrl`: From `settings.sandboxUrl` or `settings.productionUrl` based on environment

### 3. Enhanced Authentication Flow

**Updated `getAuthToken()` method:**
- Automatically loads configuration from database if not already loaded
- Maintains existing token caching functionality
- Provides better error messages for configuration issues

### 4. Added Utility Methods

**`refreshConfig()`**
- Reloads configuration from database
- Clears token cache when configuration changes
- Useful for runtime configuration updates

**`getConfigStatus()`**
- Returns current configuration status
- Helps with debugging and monitoring
- Shows which configuration values are present/missing

### 5. Enhanced Route Error Handling

**Updated `/submit-to-lhdn-best-practices` endpoint:**
- Validates configuration before processing
- Provides clear error messages for configuration issues
- Returns configuration status in error responses

## Database Structure

The service reads from the `WP_CONFIGURATION` table:

```sql
SELECT TOP 1 [Settings] 
FROM [WP_CONFIGURATION] 
WHERE [Type] = 'LHDN' 
  AND [IsActive] = 1 
ORDER BY [CreateTS] DESC
```

**Expected Settings JSON Structure:**
```json
{
  "environment": "sandbox",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "sandboxUrl": "https://preprod-api.myinvois.hasil.gov.my",
  "productionUrl": "https://api.myinvois.hasil.gov.my",
  "middlewareUrl": "https://preprod-api.myinvois.hasil.gov.my"
}
```

## Benefits

1. **Centralized Configuration**: All LHDN settings managed through admin panel
2. **Runtime Updates**: Configuration can be updated without restarting the application
3. **Environment Management**: Easy switching between sandbox and production
4. **Security**: No sensitive data in environment variables or code
5. **Consistency**: Follows the same pattern as other services in the application
6. **Validation**: Proper validation and error handling for missing configuration

## Testing

### 1. Verify Configuration Loading
```javascript
const ManualSubmissionService = require('./services/lhdn/manualSubmissionService');
const service = new ManualSubmissionService();

// Test configuration loading
try {
  await service.loadConfig();
  console.log('Configuration Status:', service.getConfigStatus());
} catch (error) {
  console.error('Configuration Error:', error.message);
}
```

### 2. Check Database Configuration
```sql
-- Verify LHDN configuration exists
SELECT * FROM [WP_CONFIGURATION] 
WHERE [Type] = 'LHDN' AND [IsActive] = 1
ORDER BY [CreateTS] DESC
```

### 3. Test API Endpoint
```bash
# Test the new best practices endpoint
curl -X POST "https://your-domain/api/outbound-files/test-file.xls/submit-to-lhdn-best-practices" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "Self-billed Invoice",
    "company": "Test Company",
    "date": "2025-05-26",
    "version": "1.0"
  }'
```

## Error Handling

The service now provides specific error messages for different configuration issues:

1. **Configuration Not Found**: "LHDN configuration not found in database"
2. **Missing Credentials**: "LHDN Client ID and Client Secret not configured in database"
3. **Missing URL**: "LHDN API URL not configured in database"
4. **Invalid JSON**: "Failed to parse LHDN configuration JSON"

## Logging

Enhanced logging provides visibility into configuration loading:

```
[Manual Submission] Loading configuration from database...
[Manual Submission] Configuration loaded from database:
[Manual Submission] Environment: sandbox
[Manual Submission] Base URL: https://preprod-api.myinvois.hasil.gov.my
[Manual Submission] Client ID: Present (eadbaef4...)
[Manual Submission] Client Secret: Present
```

## Migration Notes

- **No Breaking Changes**: Existing functionality remains unchanged
- **Backward Compatibility**: Falls back to default URLs if not configured
- **Gradual Migration**: Can be deployed without immediate configuration changes
- **Environment Variables**: No longer needed for LHDN configuration

## Next Steps

1. **Update Admin Panel**: Ensure LHDN configuration form saves all required fields
2. **Database Migration**: Verify existing LHDN configuration in database
3. **Testing**: Test with actual LHDN credentials and documents
4. **Documentation**: Update user documentation for configuration management
5. **Monitoring**: Add monitoring for configuration-related errors

## Configuration Checklist

- [ ] LHDN configuration exists in `WP_CONFIGURATION` table
- [ ] `Type = 'LHDN'` and `IsActive = true`
- [ ] Settings JSON contains `clientId` and `clientSecret`
- [ ] Settings JSON contains appropriate URL for environment
- [ ] Environment is set to 'sandbox' or 'production'
- [ ] Test the new endpoint with valid credentials
- [ ] Verify token caching works correctly
- [ ] Check error handling for missing configuration
