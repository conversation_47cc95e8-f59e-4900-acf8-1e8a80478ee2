/* Modal Base */
.modal-xl {
    max-width: 1600px;
}

.modal-content {
    border-radius: 0;
    border: none;
    background: #f8f9fa;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Modal Header */
.modal-header {
    background-color: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.modal-header .modal-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-header .document-id {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: normal;
    margin-left: 0.5rem;
}

.modal-header .valid-badge {
    padding: 0.25rem 0.75rem;
    background: #22c55e;
    color: white;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: auto;
}

/* .modal-body {
    display: grid;
    grid-template-columns: 600px 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
    height: calc(100vh - 60px);
    background-color: #f8f9fa;
} */

/* Left Panel */
.document-info {
    overflow-y: auto;
    padding-right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-width: 0;
    max-width: 600px;
}

/* Section Styles */
.section-header {
    margin-left: 10px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: #fff;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.section-header:hover {
    background: #f8f9fa;
}

.section-header i {
    font-size: 1.1rem;
    color: #4154f1;
}

.section-header h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e293b;
    flex: 1;
}

.section-header .toggle-icon {
    transition: transform 0.3s ease;
}

.section-header[aria-expanded="true"] .toggle-icon {
    transform: rotate(180deg);
}

.section-content {
    background: #fff;
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 1rem;
    display: none;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-top: none;
}

.section-content.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}



/* Scrollbar Styles */
.document-info::-webkit-scrollbar {
    width: 6px;
}

.document-info::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.document-info::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.document-info::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
