module.exports = (sequelize, DataTypes) => {
  const AdminSettings = sequelize.define('AdminSettings', {
    ID: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    SettingKey: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    SettingValue: {
      type: DataTypes.TEXT,
      allowNull: false,
      get() {
        const rawValue = this.getDataValue('SettingValue');
        try {
          return JSON.parse(rawValue);
        } catch (e) {
          return rawValue;
        }
      },
      set(value) {
        this.setDataValue('SettingValue', typeof value === 'object' ? JSON.stringify(value) : value);
      }
    },
    SettingGroup: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    Description: {
      type: DataTypes.STRING(500),
      allowNull: true
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    CreatedBy: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    UpdatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    CreateTS: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdateTS: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'AdminSettings',
    timestamps: false,
    
    // Class methods
    classMethods: {
      // Get all settings by group
      async getSettingsByGroup(group) {
        const settings = await this.findAll({
          where: {
            SettingGroup: group,
            IsActive: true
          }
        });
        return settings.reduce((acc, setting) => {
          acc[setting.SettingKey] = setting.SettingValue;
          return acc;
        }, {});
      },

      // Get a single setting value
      async getSetting(key) {
        const setting = await this.findOne({
          where: {
            SettingKey: key,
            IsActive: true
          }
        });
        return setting ? setting.SettingValue : null;
      },

      // Update or create a setting
      async upsertSetting(key, value, group, description, userId) {
        const [setting, created] = await this.upsert({
          SettingKey: key,
          SettingValue: value,
          SettingGroup: group,
          Description: description,
          CreatedBy: userId,
          UpdatedBy: userId,
          UpdateTS: new Date()
        }, {
          returning: true
        });
        return setting;
      },

      // Bulk upsert settings
      async bulkUpsertSettings(settings, userId) {
        const promises = settings.map(setting => 
          this.upsertSetting(
            setting.key,
            setting.value,
            setting.group,
            setting.description,
            userId
          )
        );
        return Promise.all(promises);
      }
    }
  });

  return AdminSettings;
}; 