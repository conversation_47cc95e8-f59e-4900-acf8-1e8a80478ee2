const { WP_LOGS } = require('../models');
const { sequelize } = require('../models');
const moment = require('moment');

// Log types
const LOG_TYPES = {
  INFO: 'INFO',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  DEBUG: 'DEBUG'
};

// Modules
const MODULES = {
  AUTH: 'Authentication',
  USER: 'User Management',
  COMPANY: 'Company Management',
  INVOICE: 'Invoice Management',
  SETTINGS: 'Settings',
  SYSTEM: 'System',
  AUDIT: 'Audit Trail',
  API: 'API',
  OUTBOUND: 'Outbound Processing'
};

// Actions
const ACTIONS = {
  CREATE: 'CREATE',
  READ: 'READ',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  FAILED_LOGIN: 'FAILED_LOGIN',
  EXPORT: 'EXPORT',
  IMPORT: 'IMPORT',
  UPLOAD: 'UPLOAD',
  DOWNLOAD: 'DOWNLOAD',
  VALIDATE: 'VALIDATE',
  SUBMIT: 'SUBMIT',
  STATUS_UPDATE: 'STATUS_UPDATE',
  SUBMISSION_START: 'SUBMISSION_START',
  SUBMISSION_SUCCESS: 'SUBMISSION_SUCCESS',
  SUBMISSION_FAILURE: 'SUBMISSION_FAILURE'
};

// Status
const STATUSES = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  PENDING: 'PENDING'
};

class OutboundLoggingService {
  /**
   * Generic log creation method
   */
  static async createLog({
    description,
    username = 'System',
    userId = null,
    ipAddress = null,
    logType = LOG_TYPES.INFO,
    module = MODULES.OUTBOUND,
    action = ACTIONS.CREATE,
    status = STATUSES.SUCCESS,
    details = null,
    CreateTS = null,
  }) {
    try {
      const { sequelize } = require('../models');
      const logEntry = await WP_LOGS.create({
        Description: description,
        CreateTS: CreateTS || sequelize.literal('GETDATE()'),
        LoggedUser: username,
        IPAddress: ipAddress,
        LogType: logType,
        Module: module,
        Action: action,
        Status: status,
        UserID: userId,
        Details: details ? JSON.stringify(details) : null
      });

      return logEntry;
    } catch (error) {
      console.error('Error creating log entry:', error);
      // Don't throw - we don't want logging failures to break the application
      return null;
    }
  }

  /**
   * Log submission start
   */
  static async logSubmissionStart(req, data) {
    try {
      const username = req.session?.user?.username || req.user?.username || 'System';
      const userId = req.session?.user?.id || req.user?.id || null;
      const ipAddress = req.ip || req.connection?.remoteAddress || null;

      return await this.createLog({
        description: `Submission started for file: ${data.fileName} (${data.type})`,
        username,
        userId,
        ipAddress,
        logType: LOG_TYPES.INFO,
        module: MODULES.OUTBOUND,
        action: ACTIONS.SUBMISSION_START,
        status: STATUSES.PENDING,
        details: data
      });
    } catch (error) {
      console.error('Error logging submission start:', error);
      return null;
    }
  }

  /**
   * Log status update
   */
  static async logStatusUpdate(req, statusData) {
    try {
      const username = req.session?.user?.username || req.user?.username || 'System';
      const userId = req.session?.user?.id || req.user?.id || null;
      const ipAddress = req.ip || req.connection?.remoteAddress || null;

      return await this.createLog({
        description: `Status updated for invoice: ${statusData.invoice_number} - Status: ${statusData.status}`,
        username,
        userId,
        ipAddress,
        logType: LOG_TYPES.INFO,
        module: MODULES.OUTBOUND,
        action: ACTIONS.STATUS_UPDATE,
        status: statusData.status === 'Failed' ? STATUSES.FAILED : STATUSES.SUCCESS,
        details: statusData
      });
    } catch (error) {
      console.error('Error logging status update:', error);
      return null;
    }
  }

  /**
   * Log successful submission
   */
  static async logSubmissionSuccess(req, result, data) {
    try {
      const username = req.session?.user?.username || req.user?.username || 'System';
      const userId = req.session?.user?.id || req.user?.id || null;
      const ipAddress = req.ip || req.connection?.remoteAddress || null;

      return await this.createLog({
        description: `Submission successful for invoice: ${data.invoiceNumber} - File: ${data.fileName}`,
        username,
        userId,
        ipAddress,
        logType: LOG_TYPES.INFO,
        module: MODULES.OUTBOUND,
        action: ACTIONS.SUBMISSION_SUCCESS,
        status: STATUSES.SUCCESS,
        details: {
          ...data,
          submissionUid: result.data?.submissionUid,
          acceptedDocuments: result.data?.acceptedDocuments?.length || 0
        }
      });
    } catch (error) {
      console.error('Error logging submission success:', error);
      return null;
    }
  }

  /**
   * Log submission failure
   */
  static async logSubmissionFailure(req, error, data) {
    try {
      const username = req.session?.user?.username || req.user?.username || 'System';
      const userId = req.session?.user?.id || req.user?.id || null;
      const ipAddress = req.ip || req.connection?.remoteAddress || null;

      return await this.createLog({
        description: `Submission failed for invoice: ${data.invoiceNumber} - File: ${data.fileName} - Error: ${error.message}`,
        username,
        userId,
        ipAddress,
        logType: LOG_TYPES.ERROR,
        module: MODULES.OUTBOUND,
        action: ACTIONS.SUBMISSION_FAILURE,
        status: STATUSES.FAILED,
        details: {
          ...data,
          error: {
            message: error.message,
            stack: error.stack,
            code: error.code
          }
        }
      });
    } catch (logError) {
      console.error('Error logging submission failure:', logError);
      return null;
    }
  }

  /**
   * Get outbound logs with filtering
   */
  static async getOutboundLogs({
    page = 1,
    limit = 10,
    startDate = null,
    endDate = null,
    username = null,
    action = null,
    status = null,
    fileName = null,
    invoiceNumber = null
  }) {
    try {
      const whereClause = {
        Module: MODULES.OUTBOUND
      };

      if (startDate && endDate) {
        whereClause.CreateTS = {
          [sequelize.Op.between]: [startDate, endDate]
        };
      }

      if (username) {
        whereClause.LoggedUser = username;
      }

      if (action) {
        whereClause.Action = action;
      }

      if (status) {
        whereClause.Status = status;
      }

      // Filter by fileName or invoiceNumber in Details JSON
      if (fileName || invoiceNumber) {
        const detailsConditions = [];
        if (fileName) {
          detailsConditions.push(sequelize.literal(`JSON_EXTRACT(Details, '$.fileName') LIKE '%${fileName}%'`));
        }
        if (invoiceNumber) {
          detailsConditions.push(sequelize.literal(`JSON_EXTRACT(Details, '$.invoiceNumber') LIKE '%${invoiceNumber}%'`));
        }
        whereClause[sequelize.Op.and] = detailsConditions;
      }

      const offset = (page - 1) * limit;

      const { count, rows } = await WP_LOGS.findAndCountAll({
        where: whereClause,
        order: [['CreateTS', 'DESC']],
        limit,
        offset
      });

      return {
        logs: rows,
        total: count,
        totalPages: Math.ceil(count / limit),
        currentPage: page
      };
    } catch (error) {
      console.error('Error fetching outbound logs:', error);
      throw error;
    }
  }
}

module.exports = {
  OutboundLoggingService,
  LOG_TYPES,
  MODULES,
  ACTIONS,
  STATUSES
};
