module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    ID: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    FullName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    Email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    Username: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    Password: {
      type: DataTypes.STRING,
      allowNull: false
    },
    UserType: {
      type: DataTypes.STRING
    },
    TIN: {
      type: DataTypes.STRING
    },
    IDType: {
      type: DataTypes.STRING
    },
    IDValue: {
      type: DataTypes.STRING
    },
    ClientID: {
      type: DataTypes.STRING
    },
    ClientSecret: {
      type: DataTypes.STRING
    },
    DigitalSignaturePath: {
      type: DataTypes.STRING
    },
    DigitalSignatureFileName: {
      type: DataTypes.STRING
    },
    Admin: {
      type: DataTypes.BOOLEAN
    }
    // CreateTS: {
    //   type: DataTypes.DATE, // Sequelize DATE type to represent timestamp
    //   allowNull: false,
    //   defaultValue: DataTypes.NOW // Set default value to current timestamp
    // }
  }, {
    tableName: 'WP_USER_REGISTRATION',
    timestamps: false // Disable automatic timestamps (createdAt, updatedAt)
  });

  return User;
};
