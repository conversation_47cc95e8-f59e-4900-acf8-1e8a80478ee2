// Status badge classes mapping
const statusClasses = {
    'Valid': 'badge-status Valid',
    'Invalid': 'badge-status Invalid',
    'Pending': 'badge-status Pending',
    'Rejected': 'badge-status Rejected',
    'Cancelled': 'badge-status Cancelled',
    'Queue': 'badge-status Queue'
};

// DateTime Manager Class
class DateTimeManager {
    static updateDateTime() {
        const timeElement = document.getElementById('currentTime');
        const dateElement = document.getElementById('currentDate');
        
        function update() {
            const now = new Date();
            
            // Update time
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                });
            }
            
            // Update date
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
        }
        
        // Update immediately and then every second
        update();
        setInterval(update, 1000);
    }
}

// Create a class for managing inbound invoices
class InvoiceTableManager {
    constructor() {
        this.table = $('#invoiceTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: '/api/lhdn/documents/recent',
                method: 'GET',
                dataSrc: (json) => {
                    const result = json && json.result ? json.result : [];
                    // Update totals after data is loaded and table is ready
                    setTimeout(() => this.updateCardTotals(), 100);
                    return result;
                }
            },
            columns: [
                {
                    data: null,
                    orderable: false,
                    className: 'checkbox-column',
                    defaultContent: `
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input row-checkbox">
                        </div>`,
                    width: '28px'
                },
                {
                    data: 'uuid',
                    className: 'uuid-column',
                    width: '180px',
                    render: function(data) {
                        return `<a href="#" class="uuid-link" data-bs-toggle="tooltip" data-bs-placement="top" title="Click to copy UUID">${data}</a>`;
                    }
                },
                {
                    data: 'internalId',
                    title: 'INTERNAL ID',
                    className: 'invoice-number text-center',
                    render: data => `<span class="badge-invoice">${data}</span>`
                },
                {
                    data: null,
                    title: 'TYPE',
                    className: 'type-column',
                    render: function(row) {
                        return `<span class="badge-type invoice">Invoice ${row.typeVersionName || '1.0'}</span>`;
                    }
                },
                {
                    data: 'supplierName',
                    title: 'SUPPLIER',
                    className: 'customer-name',
                    render: data => `<div class="text-truncate" data-bs-toggle="tooltip" data-bs-placement="top" title="${data}">${data}</div>`
                },
                {
                    data: 'receiverName',
                    title: 'RECEIVER',
                    className: 'customer-name',
                    render: data => `<div class="text-truncate" data-bs-toggle="tooltip" data-bs-placement="top" title="${data}">${data}</div>`
                },
                {
                    data: 'dateTimeIssued',
                    title: 'ISSUE DATE', 
                    className: 'date-column text-left',
                    render: data => `<div class="text-truncate" data-bs-toggle="tooltip" data-bs-placement="top" title="${this.formatDate(data)}">${this.formatDate(data)}</div>`
                },
                {
                    data: 'dateTimeReceived',
                    title: 'RECEIVED DATE',
                    className: 'date-column text-left',
                    render: data => `<div class="text-truncate" data-bs-toggle="tooltip" data-bs-placement="top" title="${this.formatDate(data)}">${this.formatDate(data)}</div>`
                },
                {
                    data: null,
                    title: 'STATUS',
                    className: 'status-column text-center',
                    render: function(row) {
                        const statusClasses = {
                            'Valid': 'badge-status Valid',
                            'Invalid': 'badge-status Invalid',
                            'Pending': 'badge-status Pending',
                            'Rejected': 'badge-status Rejected',
                            'Cancelled': 'badge-status Cancelled'
                        };
                        return `<span class="${statusClasses[row.status] || 'badge-status'}">${row.status}</span>`;
                    }
                },
                {
                    data: 'submissionChannel',
                    title: 'SOURCE',
                    className: 'source-column text-center',
                    render: data => `<span class="badge bg-primary rounded-pill">LHDN</span>`
                },
                {
                    data: 'totalSales',
                    title: 'TOTAL SALES',
                    className: 'amount-column text-end',
                    render: data => `<span class="text-nowrap">RM ${parseFloat(data || 0).toLocaleString('en-MY', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}</span>`
                },
                {
                    data: null,
                    title: '',
                    className: 'action-column text-center',
                    orderable: false,
                    render: function(row) {
                        return `
                            <button class="btn-lhdn" 
                                    onclick="viewInvoiceDetails('${row.uuid}')"
                                    data-uuid="${row.uuid}">
                                <i class="bi bi-eye me-1"></i>View</button>`;
                    }
                }
            ],
           
            scrollX: true,
            scrollCollapse: true,
            autoWidth: false,
            pageLength: 10,
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            language: {
                search: '',
                searchPlaceholder: 'Search...',
                lengthMenu: 'Show _MENU_ entries',
                info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                infoEmpty: 'Showing 0 to 0 of 0 entries',
                infoFiltered: '(filtered from _MAX_ total entries)',
                paginate: {
                    first: '<i class="bi bi-chevron-double-left"></i>',
                    previous: '<i class="bi bi-chevron-left"></i>',
                    next: '<i class="bi bi-chevron-right"></i>',
                    last: '<i class="bi bi-chevron-double-right"></i>'
                },
                select: {
                    rows: {
                        _: 'Selected %d rows',
                        0: 'Click a row to select it',
                        1: 'Selected 1 row'
                    }
                }
            },
            drawCallback: (settings) => {
                // Only update totals if this is not the first draw
                if (settings._iDisplayLength !== undefined) {
                    this.updateCardTotals();
                }
            },
            initComplete: () => {
                // Update totals once table is fully initialized
                this.updateCardTotals();
            }
        });

        // Initialize all required features - remove duplicates
        this.initializeTableStyles();
        this.initializeEventListeners();
        this.initializeSelectAll();
        this.addExportButton();
        this.initializeTooltipsAndCopy();
    }

    // Add this new method to handle table styles initialization
    initializeTableStyles() {
        // Add custom search styling
        $('.dataTables_filter input').addClass('form-control form-control-sm');
        $('.dataTables_length select').addClass('form-select form-select-sm');
    }

    initializeEventListeners() {
        $('#invoiceTable').on('click', '.view-details', async (e) => {
            const uuid = $(e.currentTarget).data('uuid');
            await this.showInvoiceDetails(uuid, longId);
        });
    }

    async showInvoiceDetails(uuid) {
        try {
            // Show loading state
            $('#modalLoadingOverlay').removeClass('d-none');
            
            // Fetch document details
            const response = await fetch(`/api/documents/${uuid}/display-details`);
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to fetch invoice details');
            }

            // Populate modal with data
            this.populateModalWithData(data);
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('documentDetailsModal'));
            const modalElement = document.getElementById('documentDetailsModal');

            modalElement.addEventListener('shown.bs.modal', () => {
                // Remove aria-hidden from main when modal is shown
                document.getElementById('main').removeAttribute('aria-hidden');
            }, { once: true });

            modalElement.addEventListener('hidden.bs.modal', () => {
                // Restore aria-hidden when modal is closed
                document.getElementById('main').setAttribute('aria-hidden', 'true');
            }, { once: true });

            modal.show();

        } catch (error) {
            console.error('Error fetching invoice details:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to load invoice details'
            });
        } finally {
            // Hide loading state
            $('#modalLoadingOverlay').addClass('d-none');
        }
    }

    populateModalWithData(data) {
        // Clear previous content
        $('.invoice-info').empty();
        $('.supplier-info').find('.detail-content').empty();
        $('.buyer-info').find('.detail-content').empty();
        $('.invoice-items').find('.detail-content').empty();

        // Update invoice number and status
        $('#invoice-number').text(data.documentInfo.internalId || 'N/A');
        $('#invoice-number').text(data.longId || 'N/A');
        // Populate supplier information
        const supplierInfo = `
            <div class="info-grid">
                <div class="info-item">
                    <label>Company Name</label>
                    <span>${data.supplierInfo.name || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <label>Registration No</label>
                    <span>${data.supplierInfo.registrationNo || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <label>Tax Registration No</label>
                    <span>${data.supplierInfo.taxRegistrationNo || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <label>Address</label>
                    <span>${data.supplierInfo.address || 'N/A'}</span>
                </div>
            </div>
        `;
        $('.supplier-info .detail-content').html(supplierInfo);

        // Populate buyer information
        const buyerInfo = `
            <div class="info-grid">
                <div class="info-item">
                    <label>Company Name</label>
                    <span>${data.buyerInfo.name || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <label>Registration No</label>
                    <span>${data.buyerInfo.registrationNo || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <label>Tax Registration No</label>
                    <span>${data.buyerInfo.taxRegistrationNo || 'N/A'}</span>
                </div>
                <div class="info-item">
                    <label>Address</label>
                    <span>${data.buyerInfo.address || 'N/A'}</span>
                </div>
            </div>
        `;
        $('.buyer-info .detail-content').html(buyerInfo);

        // Populate invoice items
        if (data.items && data.items.length > 0) {
            const itemsTable = `
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.items.map((item, index) => `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${item.description}</td>
                                    <td class="text-end">${item.quantity}</td>
                                    <td class="text-end">RM ${parseFloat(item.unitPrice).toFixed(2)}</td>
                                    <td class="text-end">RM ${parseFloat(item.amount).toFixed(2)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            $('.invoice-items .detail-content').html(itemsTable);
        } else {
            $('.invoice-items .detail-content').html('<p class="text-muted">No items found</p>');
        }
    }

    cleanup() {
        // Cleanup function to be called when page is unloaded
        if (this.table) {
            this.table.destroy();
        }
    }

    // Helper methods
    formatDate(date) {
        if (!date) return 'N/A';
        const d = new Date(date);
        return d.toLocaleString('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-MY', {
            style: 'currency',
            currency: 'MYR'
        }).format(amount || 0);
    }

    createStatusBadge(status, reason) {
        const statusClasses = {
            'Valid': 'bg-success',
            'Invalid': 'bg-danger',
            'Pending': 'bg-warning',
            'Rejected': 'bg-danger',
            'Cancelled': 'bg-secondary',
            'Queue': 'bg-info'
        };
        const className = statusClasses[status] || 'bg-secondary';
        const reasonHtml = reason ? `<br><small class="text-muted">${reason}</small>` : '';
        return `<span class="badge ${className}">${status || 'Unknown'}</span>${reasonHtml}`;
    }

    createSourceBadge(source) {
        const isPixelCare = source === 'PixelCare';
        return `<span class="badge ${isPixelCare ? 'bg-primary' : 'bg-info'}">
            <i class="bi ${isPixelCare ? 'bi-pc-display' : 'bi-building'}"></i>
            ${source || 'Unknown'}
        </span>`;
    }

    initializeSelectAll() {
        // Handle "Select All" checkbox
        $('#selectAll').on('change', (e) => {
            const isChecked = $(e.target).prop('checked');
            $('.row-checkbox').prop('checked', isChecked);
            this.updateExportButton();
        });

        // Handle individual checkbox changes
        $('#invoiceTable').on('change', '.row-checkbox', () => {
            const totalCheckboxes = $('.row-checkbox').length;
            const checkedCheckboxes = $('.row-checkbox:checked').length;
            $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
            this.updateExportButton();
        });
    }

    addExportButton() {
        // Add export button after the table length control
        const exportBtn = $(`
            <button id="exportSelected" class="btn btn-primary btn-sm ms-2" disabled>
                <i class="bi bi-download me-1"></i>Export Selected
                <span class="selected-count ms-1">(0)</span>
            </button>
        `);

        $('.dataTables_length').append(exportBtn);

        // Handle export button click
        $('#exportSelected').on('click', () => this.exportSelectedRecords());
    }

    updateExportButton() {
        const selectedCount = $('.row-checkbox:checked').length;
        const exportBtn = $('#exportSelected');
        
        if (selectedCount > 0) {
            exportBtn.prop('disabled', false);
            exportBtn.find('.selected-count').text(`(${selectedCount})`);
        } else {
            exportBtn.prop('disabled', true);
            exportBtn.find('.selected-count').text('(0)');
        }
    }

    async exportSelectedRecords() {
        try {
            const selectedRows = [];
            $('.row-checkbox:checked').each((_, checkbox) => {
                const rowData = this.table.row($(checkbox).closest('tr')).data();
                selectedRows.push(rowData);
            });

            if (selectedRows.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Records Selected',
                    text: 'Please select at least one record to export.'
                });
                return;
            }

            // Show loading state
            const exportBtn = $('#exportSelected');
            const originalHtml = exportBtn.html();
            exportBtn.prop('disabled', true);
            exportBtn.html('<i class="bi bi-arrow-repeat spin me-1"></i>Exporting...');

            // Prepare export data
            const exportData = selectedRows.map(row => ({
                UUID: row.uuid,
                'Internal ID': row.internalId,
                Type: row.typeName,
                Supplier: row.supplierName,
                Receiver: row.receiverName,
                'Issue Date': new Date(row.dateTimeIssued).toLocaleString(),
                'Received Date': new Date(row.dateTimeReceived).toLocaleString(),
                Status: row.status,
                'Total Sales': `RM ${parseFloat(row.totalSales).toFixed(2)}`
            }));

            // Convert to CSV
            const csvContent = this.convertToCSV(exportData);
            
            // Create and trigger download
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `inbound_invoices_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            // Reset button state
            exportBtn.prop('disabled', false);
            exportBtn.html(originalHtml);

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Export Complete',
                text: `Successfully exported ${selectedRows.length} records`,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });

        } catch (error) {
            console.error('Export error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Export Failed',
                text: 'Failed to export selected records. Please try again.'
            });
        }
    }

    convertToCSV(data) {
        if (data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const rows = [
            headers.join(','), // Header row
            ...data.map(row => 
                headers.map(header => 
                    JSON.stringify(row[header] || '')
                ).join(',')
            )
        ];
        
        return rows.join('\n');
    }

    updateCardTotals() {
        // Check if table is initialized
        if (!this.table || !$.fn.DataTable.isDataTable('#invoiceTable')) {
            return;
        }

        try {
            const data = this.table.rows().data();
            const totals = {
                invoices: 0,
                valid: 0,
                invalid: 0,
                rejected: 0,
                cancelled: 0,
                queue: 0
            };

            // Count totals
            if (data && data.length) {
                data.each(row => {
                    totals.invoices++;
                    switch (row.status) {
                        case 'Valid':
                            totals.valid++;
                            break;
                        case 'Invalid':
                            totals.invalid++;
                            break;
                        case 'Rejected':
                            totals.rejected++;
                            break;
                        case 'Cancelled':
                            totals.cancelled++;
                            break;
                        case 'Queue':
                            totals.queue++;
                            break;
                    }
                });
            }

            // Update card values and hide spinners
            $('.total-invoice-value')
                .text(totals.invoices)
                .show()
                .closest('.info-card')
                .find('.card-icon')
                .append(`<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-primary">${totals.invoices}</span>`);

            $('.total-valid-value')
                .text(totals.valid)
                .show()
                .closest('.info-card')
                .find('.card-icon')
                .append(`<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success">${totals.valid}</span>`);

            $('.total-invalid-value')
                .text(totals.invalid)
                .show()
                .closest('.info-card')
                .find('.card-icon')
                .append(`<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">${totals.invalid}</span>`);

            $('.total-rejected-value')
                .text(totals.rejected)
                .show()
                .closest('.info-card')
                .find('.card-icon')
                .append(`<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">${totals.rejected}</span>`);

            $('.total-cancel-value')
                .text(totals.cancelled)
                .show()
                .closest('.info-card')
                .find('.card-icon')
                .append(`<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning">${totals.cancelled}</span>`);

            $('.total-queue-value')
                .text(totals.queue)
                .show()
                .closest('.info-card')
                .find('.card-icon')
                .append(`<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-info">${totals.queue}</span>`);

            // Hide all spinners
            $('.loading-spinner').hide();

            // Remove any existing badges before adding new ones
            $('.card-icon .badge').remove();

        } catch (error) {
            console.error('Error updating card totals:', error);
            // Don't hide spinners if there was an error
        }
    }

    initializeTooltipsAndCopy() {
        // Initialize tooltips for new elements
        const initTooltips = () => {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.forEach(tooltipTriggerEl => {
                new bootstrap.Tooltip(tooltipTriggerEl, {
                    trigger: 'hover'
                });
            });
        };

        // Initialize tooltips on first load
        initTooltips();

        // Reinitialize tooltips after table draw
        this.table.on('draw', () => {
            // Dispose existing tooltips
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(element => {
                const tooltip = bootstrap.Tooltip.getInstance(element);
                if (tooltip) {
                    tooltip.dispose();
                }
            });
            // Initialize new tooltips
            initTooltips();
        });

        // Handle UUID copy
        this.table.on('click', '.uuid-link', (e) => {
            e.preventDefault();
            const uuid = $(e.currentTarget).text();
            const tooltipInstance = bootstrap.Tooltip.getInstance(e.currentTarget);
            
            navigator.clipboard.writeText(uuid).then(() => {
                // Hide tooltip if it exists
                if (tooltipInstance) {
                    tooltipInstance.hide();
                }
                
                // Show success message
                Swal.fire({
                    toast: true,
                    position: 'top-end',
                    icon: 'success',
                    title: 'UUID copied to clipboard!',
                    showConfirmButton: false,
                    timer: 1500
                });
            }).catch(err => {
                console.error('Failed to copy:', err);
                Swal.fire({
                    toast: true,
                    position: 'top-end',
                    icon: 'error',
                    title: 'Failed to copy UUID',
                    showConfirmButton: false,
                    timer: 1500
                });
            });
        });
    }
}

async function viewInvoiceDetails(uuid) {
    try {
        // Fetch document details
        const response = await fetch(`/api/documents/${uuid}/display-details`);
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || 'Failed to fetch invoice details');
        }

        const data = result;
        console.log('Document Details:', data);

        // Update modal content
        $('.modal-content').html(`
            <div class="modal-header">
                <div class="d-flex align-items-center">
                    <i class="bi bi-file-text me-2"></i>
                    <div>
                        <h5 class="modal-title mb-0">Document Details</h5>
                        <small class="text-muted" id="modal-invoice-number">#${data.documentInfo?.internalId || 'N/A'}</small>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <span class="${statusClasses[data.documentInfo?.status] || 'badge-status'} me-2">${data.documentInfo?.status || 'Unknown'}</span>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="document-info">
                    <!-- Supplier Information Section -->
                    <div class="section-header" onclick="toggleSection(this)" aria-expanded="false">
                        <i class="bi bi-building"></i>
                        <h6>Supplier Information</h6>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                    <div class="section-content" id="supplier-info-content">
                        ${createSupplierContent(data.supplierInfo)}
                    </div>

                    <!-- Buyer Information Section -->
                    <div class="section-header" onclick="toggleSection(this)" aria-expanded="false">
                        <i class="bi bi-person"></i>
                        <h6>Buyer Information</h6>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                    <div class="section-content" id="buyer-info-content">
                        ${createBuyerContent(data.customerInfo)}
                    </div>

                    <!-- Payment Information Section -->
                    <div class="section-header" onclick="toggleSection(this)" aria-expanded="true">
                        <i class="bi bi-cash-stack"></i>
                        <h6>Payment Information</h6>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                    <div class="section-content show" id="payment-info-content">
                        ${createPaymentContent(data.paymentInfo)}
                    </div>
                </div>
                
                <div class="pdf-container">
                    <div class="pdf-viewer-container">
                        <!-- PDF viewer will be loaded here -->
                    </div>
                </div>
            </div>
        `);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('documentDetailsModal'));
        modal.show();

        // Load PDF
        await loadPDF(uuid, data);

    } catch (error) {
        console.error('Error showing document details:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: error.message || 'Failed to show document details'
        });
    }
}

// Helper function to toggle collapsible sections
function toggleSection(button) {
    const content = button.nextElementSibling;
    const isExpanded = button.getAttribute('aria-expanded') === 'true';
    
    button.setAttribute('aria-expanded', !isExpanded);
    content.classList.toggle('show');
}

// Helper functions to create content sections
function createSupplierContent(supplierInfo) {
    return `
        <div class="info-content">
            <div class="info-row">
                <div class="label">COMPANY NAME</div>
                <div class="value fw-medium">${supplierInfo?.name || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">TAX ID</div>
                <div class="value">${supplierInfo?.tin || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">REGISTRATION NO.</div>
                <div class="value">${supplierInfo?.registrationNo || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">SST REGISTRATION</div>
                <div class="value">${supplierInfo?.taxRegNo || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">MSIC CODE</div>
                <div class="value">${supplierInfo?.msicCode || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">ADDRESS</div>
                <div class="value text-wrap">${supplierInfo?.address || 'N/A'}</div>
            </div>
        </div>
    `;
}

function createBuyerContent(customerInfo) {
    return `
        <div class="info-content">
            <div class="info-row">
                <div class="label">COMPANY NAME</div>
                <div class="value fw-medium">${customerInfo?.company || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">TAX ID</div>
                <div class="value">${customerInfo?.tin || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">REGISTRATION NO.</div>
                <div class="value">${customerInfo?.registrationNo || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">SST REGISTRATION</div>
                <div class="value">${customerInfo?.taxRegNo || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">ADDRESS</div>
                <div class="value text-wrap">${customerInfo?.address || 'N/A'}</div>
            </div>
        </div>
    `;
}

function createPaymentContent(paymentInfo) {
    return `
        <div class="info-content">
            <div class="info-row highlight-row">
                <div class="label">TOTAL AMOUNT</div>
                <div class="value fw-bold fs-5 text-primary">
                    RM ${parseFloat(paymentInfo?.totalIncludingTax || 0).toLocaleString('en-MY', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}
                </div>
            </div>
            <div class="info-row">
                <div class="label">SUBTOTAL</div>
                <div class="value">
                    RM ${parseFloat(paymentInfo?.totalExcludingTax || 0).toLocaleString('en-MY', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}
                </div>
            </div>
            <div class="info-row">
                <div class="label">TAX AMOUNT</div>
                <div class="value">
                    RM ${parseFloat(paymentInfo?.taxAmount || 0).toLocaleString('en-MY', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}
                </div>
            </div>
            <div class="info-row">
                <div class="label">PAYMENT MODE</div>
                <div class="value">${paymentInfo?.paymentMode || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">PAYMENT TERMS</div>
                <div class="value">${paymentInfo?.paymentTerms || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">PAYMENT DATE</div>
                <div class="value">${paymentInfo?.paymentDate || 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">VALIDATION DATE</div>
                <div class="value">${paymentInfo?.validationDateTime ? new Date(paymentInfo.validationDateTime).toLocaleString('en-MY', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                }) : 'N/A'}</div>
            </div>
            <div class="info-row">
                <div class="label">IRBM UNIQUE NO</div>
                <div class="value">
                    <span class="badge bg-light text-dark border">${paymentInfo?.irbmUniqueNo || 'N/A'}</span>
                </div>
            </div>
        </div>
    `;
}

// Helper function for currency formatting
function formatModalCurrency(amount) {
    if (!amount || isNaN(amount)) return 'RM 0.00';
    return `RM ${parseFloat(amount).toLocaleString('en-MY', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })}`;
}


// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing managers...');
    try {
        // Initialize invoice table
        const invoiceManager = new InvoiceTableManager();
        
        // Initialize date/time display
        DateTimeManager.updateDateTime();
        
        console.log('Managers initialized successfully');
    } catch (error) {
        console.error('Error initializing managers:', error);
        Swal.fire({
            icon: 'error',
            title: 'Initialization Error',
            text: 'Failed to initialize the application. Please refresh the page.',
            confirmButtonText: 'Refresh',
            showCancelButton: true,
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.reload();
            }
        });
    }
});

// Cleanup on page unload
window.addEventListener('unload', () => {
    if (window.invoiceTable) {
        window.invoiceTable.cleanup();
    }
});

async function loadPDF(uuid, documentData) {
    try {
        // Initial loading state with progress container
        $('.pdf-viewer-container').html(`
            <div class="d-flex flex-column align-items-center justify-content-center h-100">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div id="pdf-progress" class="text-center">
                    <p class="text-muted mb-2" id="pdf-main-status">Initializing PDF generation...</p>
                    <small class="text-muted d-block" id="pdf-status-message"></small>
                </div>
            </div>
        `);

        // Function to update both main status and detail message
        const updateStatus = (mainStatus, detailMessage = '') => {
            $('#pdf-main-status').text(mainStatus);
            $('#pdf-status-message').text(detailMessage);
        };

        updateStatus('Checking PDF status...', 'Looking for existing PDF file');

        // Try to get PDF
        const response = await fetch(`/api/lhdn/documents/${uuid}/pdf`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(documentData)
        });

        const data = await response.json();
        console.log('PDF response:', data);

        if (!data.success) {
            throw new Error(data.message || 'Failed to load PDF');
        }

        if (data.cached) {
            updateStatus('Loading cached PDF...', 'Using existing PDF from cache');
        } else {
            updateStatus('Generating new PDF...', 'This might take a few moments');
        }

        // Load the PDF
        const timestamp = new Date().getTime();
        const pdfUrl = `${data.url}?t=${timestamp}`;
        
        // Show final status before loading PDF viewer
        updateStatus(data.message || 'Loading PDF viewer...', 'Almost done');

        // Short delay to show the final status message
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Create iframe for PDF
        $('.pdf-viewer-container').html(`
            <iframe id="pdfViewer" class="w-100 h-100" style="border: none;" src="${pdfUrl}"></iframe>
        `);

    } catch (error) {
        console.error('Error loading PDF:', error);
        $('.pdf-viewer-container').html(`
            <div class="alert alert-danger m-3">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Failed to load PDF: ${error.message}
                <button class="btn btn-outline-danger btn-sm ms-3" onclick="loadPDF('${uuid}', ${JSON.stringify(documentData)})">
                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                </button>
            </div>
        `);
    }
}
