'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_SUBMISSION_STATUS', {
      DocNum: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      UUID: {
        type: Sequelize.STRING
      },
      SubmissionUID: {
        type: Sequelize.STRING
      },
      SubmissionStatus: {
        type: Sequelize.STRING
      },
      DateTimeSent: {
        type: Sequelize.DATE
      },
      DateTimeUpdated: {
        type: Sequelize.DATE
      },
      RejectionDetails: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      FileName: {
        type: Sequelize.STRING,
        unique: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('WP_SUBMISSION_STATUS', ['DocNum'], {
      unique: true
    });
    await queryInterface.addIndex('WP_SUBMISSION_STATUS', ['FileName'], {
      unique: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_SUBMISSION_STATUS');
  }
}; 