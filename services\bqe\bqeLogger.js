const fs = require('fs');
const path = require('path');
const moment = require('moment');

class BQELogger {
    constructor() {
        this.logDirectory = path.join(__dirname, '../../logs/bqe');
        this.ensureLogDirectory();
    }

    ensureLogDirectory() {
        const directories = [
            path.join(__dirname, '../../logs'),
            this.logDirectory,
            path.join(this.logDirectory, 'processing'),
            path.join(this.logDirectory, 'mapping'),
            path.join(this.logDirectory, 'errors')
        ];

        directories.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }

    createLogEntry(type, data) {
        return {
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss.SSS'),
            type,
            data
        };
    }

    // Helper method to sanitize invoice numbers for filenames
    sanitizeInvoiceNumber(invoiceNumber) {
        // Replace slashes and other problematic characters with underscores
        return invoiceNumber.replace(/[\/\\:*?"<>|]/g, '_');
    }

    async writeProcessingLog(invoiceNumber, data) {
        try {
            // Sanitize the invoice number for use in filenames
            const safeInvoiceNumber = this.sanitizeInvoiceNumber(invoiceNumber);
            const today = moment().format('YYYY-MM-DD');
            const logFilePath = path.join(
                this.logDirectory,
                'processing',
                `${today}_${safeInvoiceNumber}.json`
            );

            let existingLogs = [];
            if (fs.existsSync(logFilePath)) {
                existingLogs = JSON.parse(fs.readFileSync(logFilePath, 'utf8'));
            }

            existingLogs.push(this.createLogEntry('processing', data));

            await fs.promises.writeFile(
                logFilePath,
                JSON.stringify(existingLogs, null, 2),
                'utf8'
            );

            console.log(`Processing log written: ${logFilePath}`);
            return logFilePath;
        } catch (error) {
            console.error('Error writing processing log:', error);
            return false;
        }
    }

    async writeMappingLog(invoiceNumber, data) {
        try {
            // Sanitize the invoice number for use in filenames
            const safeInvoiceNumber = this.sanitizeInvoiceNumber(invoiceNumber);
            const today = moment().format('YYYY-MM-DD');
            const logFilePath = path.join(
                this.logDirectory,
                'mapping',
                `${today}_${safeInvoiceNumber}.json`
            );

            let existingLogs = [];
            if (fs.existsSync(logFilePath)) {
                existingLogs = JSON.parse(fs.readFileSync(logFilePath, 'utf8'));
            }

            existingLogs.push(this.createLogEntry('mapping', data));

            await fs.promises.writeFile(
                logFilePath,
                JSON.stringify(existingLogs, null, 2),
                'utf8'
            );

            console.log(`Mapping log written: ${logFilePath}`);
            return logFilePath;
        } catch (error) {
            console.error('Error writing mapping log:', error);
            return false;
        }
    }

    async writeErrorLog(invoiceNumber, error) {
        try {
            const sanitizedInvoiceNumber = this.sanitizeInvoiceNumber(invoiceNumber || 'unknown');
            const today = moment().format('YYYY-MM-DD');
            const logFilePath = path.join(
                this.logDirectory,
                'errors',
                `${today}_${sanitizedInvoiceNumber}.json`
            );

            let existingLogs = [];
            if (fs.existsSync(logFilePath)) {
                existingLogs = JSON.parse(fs.readFileSync(logFilePath, 'utf8'));
            }

            // Format the error object for logging
            const formattedError = {
                message: error.message || 'Unknown error',
                stack: error.stack || '',
                details: error.details || {},
                timestamp: moment().format()
            };

            existingLogs.push(this.createLogEntry('error', formattedError));

            await fs.promises.writeFile(
                logFilePath,
                JSON.stringify(existingLogs, null, 2),
                'utf8'
            );

            console.log(`Error log written: ${logFilePath}`);
            return logFilePath;
        } catch (logError) {
            console.error('Error writing error log:', logError);
            return false;
        }
    }
}

module.exports = BQELogger;