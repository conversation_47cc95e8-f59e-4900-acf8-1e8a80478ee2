module.exports = (sequelize, DataTypes) => {
    
const WP_SETTINGS = sequelize.define('WP_SETTINGS', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    company_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique: true
    },
    // Company settings
    company_name: {
        type: DataTypes.STRING,
        allowNull: true
    },
    roc_number: {
        type: DataTypes.STRING,
        allowNull: true
    },
    tax_number: {
        type: DataTypes.STRING,
        allowNull: true
    },
    sst_number: {
        type: DataTypes.STRING,
        allowNull: true
    },
    business_address: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    contact_email: {
        type: DataTypes.STRING,
        allowNull: true
    },
    contact_phone: {
        type: DataTypes.STRING,
        allowNull: true
    },

    // API settings
    api_environment: {
        type: DataTypes.ENUM('sandbox', 'production'),
        defaultValue: 'sandbox'
    },
    api_key: {
        type: DataTypes.STRING,
        allowNull: true
    },
    api_secret: {
        type: DataTypes.STRING,
        allowNull: true
    },
    api_endpoint: {
        type: DataTypes.STRING,
        allowNull: true
    },
    use_custom_endpoint: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    },
    request_timeout: {
        type: DataTypes.INTEGER,
        defaultValue: 60
    },
    verify_ssl: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },

    // Invoice settings
    invoice_format: {
        type: DataTypes.ENUM('json', 'xml'),
        defaultValue: 'json'
    },
    currency: {
        type: DataTypes.STRING,
        defaultValue: 'MYR'
    },
    auto_convert: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },

    // Validation settings
    validate_invoice_no: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    validate_tax_id: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    validate_dates: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    validate_totals: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    validate_tax_calc: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    check_duplicates: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    validate_currency: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    error_action: {
        type: DataTypes.ENUM('reject', 'queue', 'auto-correct'),
        defaultValue: 'reject'
    },

    // Logging settings
    log_level: {
        type: DataTypes.ENUM('error', 'warn', 'info', 'debug'),
        defaultValue: 'error'
    },
    store_local: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    retention_days: {
        type: DataTypes.INTEGER,
        defaultValue: 30
    },
    notify_errors: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    notify_emails: {
        type: DataTypes.TEXT,
        allowNull: true,
        get() {
            const value = this.getDataValue('notify_emails');
            return value ? value.split(',') : [];
        },
        set(val) {
            if (Array.isArray(val)) {
                this.setDataValue('notify_emails', val.join(','));
            } else {
                this.setDataValue('notify_emails', val);
            }
        }
    },
    monitor_performance: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    monitor_quota: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    audit_changes: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    audit_access: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },

    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
    }
}, {
    tableName: 'wp_settings',
    timestamps: false,
    indexes: [
        {
            unique: true,
            fields: ['company_id']
        }
    ]
    });

    return WP_SETTINGS;
  };
  
