'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('AdminSettings', {
      ID: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      SettingKey: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      SettingValue: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      SettingGroup: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      Description: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      IsActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      CreatedBy: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      UpdatedBy: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      CreateTS: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('GETDATE')
      },
      UpdateTS: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });

    // Add unique constraint on SettingKey
    await queryInterface.addConstraint('AdminSettings', {
      fields: ['SettingKey'],
      type: 'unique',
      name: 'UK_AdminSettings_SettingKey'
    });

    // Add index on SettingGroup for faster lookups
    await queryInterface.addIndex('AdminSettings', {
      fields: ['SettingGroup'],
      name: 'IX_AdminSettings_SettingGroup'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('AdminSettings');
  }
}; 