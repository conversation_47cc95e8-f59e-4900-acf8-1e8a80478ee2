{% extends '../../layout.html' %}

{% block head %}
<title>User Settings - eInvoice Portal</title>
<link href="/assets/css/pages/settings.css" rel="stylesheet">
<link href="/assets/css/pages/profile.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="settings-container">
  <!-- Header -->
  <div class="profile-welcome-card">
    <h2>
      <i class="fas fa-user-cog"></i>
      User Settings
    </h2>
    <p>Manage your personal account settings and preferences</p>
  </div>

  <div class="settings-content">
    <!-- Left Sidebar - Settings Navigation -->
    <div class="settings-nav-card">
      <h6 class="settings-nav-title">
        <i class="fas fa-sliders"></i>
        User Settings Menu
      </h6>
      
      <div class="settings-nav-items">
        <a href="#profile" class="settings-nav-item active" data-section="profile">
          <div class="settings-nav-icon">
            <i class="fas fa-user"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Profile Settings</h4>
            <p>Update your personal information</p>
          </div>
        </a>

        <a href="#password" class="settings-nav-item" data-section="password">
          <div class="settings-nav-icon">
            <i class="fas fa-lock"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Password & Security</h4>
            <p>Manage your password and security options</p>
          </div>
        </a>

        <a href="#preferences" class="settings-nav-item" data-section="preferences">
          <div class="settings-nav-icon">
            <i class="fas fa-palette"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Preferences</h4>
            <p>Customize your experience</p>
          </div>
        </a>

        <a href="#notifications" class="settings-nav-item" data-section="notifications">
          <div class="settings-nav-icon">
            <i class="fas fa-bell"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Notification Preferences</h4>
            <p>Manage your notification settings</p>
          </div>
        </a>
      </div>
    </div>

    <!-- Right Content - Settings Forms -->
    <div class="settings-form-section">
      <!-- Profile Settings -->
      <div class="settings-form active" id="profile">
        <h3 class="settings-form-title">
          <i class="fas fa-user"></i>
          Profile Settings
        </h3>
        <div class="settings-form-content">
          <div class="form-group">
            <label>Profile Picture</label>
            <div class="profile-picture-upload">
              <img id="profilePicture" src="/assets/images/default-avatar.png" alt="Profile Picture">
              <button class="btn btn-secondary" onclick="uploadProfilePicture()">Change Picture</button>
            </div>
          </div>

          <div class="form-group">
            <label>Full Name</label>
            <input type="text" class="form-control" id="fullName" placeholder="Enter your full name">
          </div>

          <div class="form-group">
            <label>Job Title</label>
            <input type="text" class="form-control" id="jobTitle" placeholder="Enter your job title">
          </div>

          <div class="form-group">
            <label>Email Address</label>
            <input type="email" class="form-control" id="email" placeholder="Enter your email">
          </div>

          <div class="form-group">
            <label>Phone Number</label>
            <input type="tel" class="form-control" id="phone" placeholder="Enter your phone number">
          </div>
        </div>
      </div>

      <!-- Password Settings -->
      <div class="settings-form" id="password">
        <h3 class="settings-form-title">
          <i class="fas fa-lock"></i>
          Password & Security
        </h3>
        <div class="settings-form-content">
          <div class="form-group">
            <label>Current Password</label>
            <input type="password" class="form-control" id="currentPassword" placeholder="Enter current password">
          </div>

          <div class="form-group">
            <label>New Password</label>
            <input type="password" class="form-control" id="newPassword" placeholder="Enter new password">
          </div>

          <div class="form-group">
            <label>Confirm New Password</label>
            <input type="password" class="form-control" id="confirmPassword" placeholder="Confirm new password">
          </div>

          <div class="form-group">
            <label>Two-Factor Authentication</label>
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="enable2FA">
              <label class="form-check-label">Enable Two-Factor Authentication</label>
            </div>
          </div>
        </div>
      </div>

      <!-- Preferences -->
      <div class="settings-form" id="preferences">
        <h3 class="settings-form-title">
          <i class="fas fa-palette"></i>
          Preferences
        </h3>
        <div class="settings-form-content">
          <div class="form-group">
            <label>Language</label>
            <select class="form-control" id="userLanguage">
              <option value="en">English</option>
              <option value="ms">Bahasa Malaysia</option>
            </select>
          </div>

          <div class="form-group">
            <label>Time Zone</label>
            <select class="form-control" id="userTimezone">
              <option value="Asia/Kuala_Lumpur">Malaysia (UTC+8)</option>
            </select>
          </div>

          <div class="form-group">
            <label>Theme</label>
            <select class="form-control" id="theme">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="system">System Default</option>
            </select>
          </div>

          <div class="form-group">
            <label>Dashboard Layout</label>
            <select class="form-control" id="dashboardLayout">
              <option value="grid">Grid View</option>
              <option value="list">List View</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Notification Preferences -->
      <div class="settings-form" id="notifications">
        <h3 class="settings-form-title">
          <i class="fas fa-bell"></i>
          Notification Preferences
        </h3>
        <div class="settings-form-content">
          <div class="form-group">
            <label>Email Notifications</label>
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="emailInvoices">
              <label class="form-check-label">New Invoices</label>
            </div>
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="emailPayments">
              <label class="form-check-label">Payment Updates</label>
            </div>
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="emailSystem">
              <label class="form-check-label">System Updates</label>
            </div>
          </div>

          <div class="form-group">
            <label>Push Notifications</label>
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="pushEnabled">
              <label class="form-check-label">Enable Push Notifications</label>
            </div>
          </div>

          <div class="form-group">
            <label>Notification Frequency</label>
            <select class="form-control" id="notificationFrequency">
              <option value="immediate">Immediate</option>
              <option value="hourly">Hourly Digest</option>
              <option value="daily">Daily Digest</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Save Button -->
      <div class="settings-form-actions">
        <button class="btn btn-secondary" onclick="resetUserSettings()">Reset Changes</button>
        <button class="btn btn-primary" onclick="saveUserSettings()">Save Changes</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/assets/js/pages/user-settings.js"></script>
{% endblock %} 