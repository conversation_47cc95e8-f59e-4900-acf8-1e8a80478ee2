const testAuthentication = require('./scripts/test-credentials');

async function runTest() {
    console.log('Starting authentication test...\n');
    
    try {
        const result = await testAuthentication();
        console.log('\nTest completed.');
        console.log('Success:', result.success);
        
        if (!result.success) {
            console.log('Error:', result.error);
        }
    } catch (error) {
        console.error('Test failed:', error);
    }
}

runTest().catch(console.error);