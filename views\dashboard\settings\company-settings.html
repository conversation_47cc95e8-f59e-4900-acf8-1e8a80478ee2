{% extends '../layout.html' %}

{% block head %}
<title>Company Settings - eInvoice Portal</title>
<link href="/assets/css/pages/profile.css" rel="stylesheet">
<link href="/assets/css/components/table.css" rel="stylesheet">
<link href="/assets/css/components/tooltip.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>

  /* Custom Modal Styles */
  .branch-modal .modal-dialog {
    max-width: 1000px;
  }
  
  .branch-modal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    max-height: 85vh;
  }
  
  .branch-modal .modal-header {
    border-bottom: none;
    padding: 12px 20px;
    background: #f8f9fa;
    border-radius: 15px 15px 0 0;
  }
  
  .branch-modal .modal-body {
    padding: 15px;
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 15px;
    overflow-y: auto;
  }

  .branch-modal .left-column {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .branch-modal .right-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .branch-modal .full-width {
    grid-column: 1 / -1;
  }
  
  .branch-modal .profile-info-section {
    background: white;
    border-radius: 10px;
    padding: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }
  
  .branch-modal .profile-info-section h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .branch-modal .profile-form-group {
    margin-bottom: 8px;
  }
  
  .branch-modal .profile-form-label {
    margin-bottom: 3px;
    font-size: 0.85rem;
    color: #666;
  }
  
  .branch-modal .profile-form-control {
    padding: 5px 10px;
    font-size: 0.9rem;
    height: 32px;
  }
  
  .branch-modal textarea.profile-form-control {
    min-height: 50px;
    height: auto;
  }
  
  .branch-modal .modal-footer {
    border-top: none;
    padding: 12px 20px;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
  }
  
  .branch-modal .tooltip-icon {
    color: #6c757d;
    font-size: 12px;
  }

  .branch-modal .row {
    margin: 0 !important;
  }

  .branch-modal .col-md-6 {
    padding: 0 8px;
  }

  /* Modal width styles */
  #branchModal .modal-dialog {
    max-width: 800px;
    width: 85%;
    margin: 1.75rem auto;
  }

  @media (max-width: 768px) {
    #branchModal .modal-dialog {
      width: 95%;
      margin: 1rem auto;
    }
  }

  .nav-tabs {
    border-bottom: 1px solid #dee2e6;
  }

  .nav-tabs .nav-link {
    margin-bottom: -1px;
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.875rem;
  }

  .form-label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .form-control-sm {
    height: calc(1.5em + 0.5rem + 2px);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }

  textarea.form-control-sm {
    height: auto;
    min-height: 60px;
  }

  .tooltip-icon {
    color: #6c757d;
    font-size: 14px;
    margin-left: 4px;
    cursor: pointer;
  }

</style>
{% endblock %}

{% block content %}
<div class="profile-container">
  <!-- Welcome Card -->
  <div class="profile-welcome-card">
    <h2>Company Settings
      
    </h2>
    <p>Manage your company information and branch details</p>
  </div>

  <div class="profile-content">
    <!-- Left Sidebar - Main Company Basic Info -->
    <div class="profile-sidebar">
      <h6 class="text-center">Company Information
        <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
           title="Primary company details used for official documentation and compliance."></i>
      </h6>
      <!-- View Mode -->
      <div id="viewMode">
        <div >
          <!-- Add image upload input -->
          <div style="text-align: center; margin-bottom: 1rem;">
            <img src="/assets/img/noimage.png" alt="Company Logo" class="profile-image" id="profileImg">
          
          </div>
         
        </div>
         <div class="profile-info">
            <h3 class="profile-name username">Loading...</h3>
            <div class="divider"></div>
            <p class="profile-tin tin"><i class="bi bi-upc"></i> <span>Loading...</span></p>
            <p class="profile-brn brn"><i class="bi bi-card-text"></i> <span>Loading...</span></p>
            <p class="profile-email email"><i class="bi bi-envelope"></i> <span>Loading...</span></p>
            <p class="profile-phone phone"><i class="bi bi-telephone"></i> <span>Loading...</span></p>
            <p class="profile-address address"><i class="bi bi-geo-alt"></i> <span>Loading...</span></p>
        </div>
      </div>

      <!-- Edit Mode -->
      <div id="editMode" class="profile-edit-form">
        <form id="companyForm">
          <div class="profile-form-group">
            <label class="profile-form-label">TIN <span class="text-danger">*</span>
              <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                 title="Tax Identification Number is mandatory for eInvoice submission. Any changes will result in invalid documents."></i>
            </label>
            <div class="profile-input-group">
              <input type="text" class="profile-form-control" name="tin" id="tin" disabled>
              <button type="button" class="profile-edit-icon" onclick="handleLHDNEdit('tin')">
                <i class="fas fa-edit"></i>
              </button>
            </div>
          </div>
          <div class="profile-form-group">
            <label class="profile-form-label">BRN <span class="text-danger">*</span>
              <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                 title="Business Registration Number is required for company identification. Must match official records."></i>
            </label>
            <div class="profile-input-group">
              <input type="text" class="profile-form-control" name="brn" id="brn" disabled>
              <button type="button" class="profile-edit-icon" onclick="handleLHDNEdit('brn')">
                <i class="fas fa-edit"></i>
              </button>
            </div>
          </div>
          <div class="profile-form-group">
            <label class="profile-form-label">Company Name
              <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                 title="Your company's registered business name as it appears on official documents."></i>
            </label>
            <input type="text" class="profile-form-control" name="companyName" id="companyName">
          </div>
          <div class="profile-form-group">
            <label class="profile-form-label">Industry
              <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                 title="Select your company's primary industry sector for better service customization."></i>
            </label>
            <select class="profile-form-control" name="industry" id="industry">
              <option value="" disabled>Select Industry</option>
              <option value="Agriculture">Agriculture</option>
              <option value="Automotive">Automotive</option>
              <option value="Banking">Banking</option>
              <option value="Construction">Construction</option>
              <option value="Education">Education</option>
              <option value="Energy">Energy</option>
              <option value="Finance">Finance</option>
              <option value="Healthcare">Healthcare</option>
              <option value="Hospitality">Hospitality</option>
              <option value="Information Technology">Information Technology</option>
              <option value="Insurance">Insurance</option>
              <option value="Manufacturing">Manufacturing</option>
              <option value="Media">Media</option>
              <option value="Retail">Retail</option>
              <option value="Telecommunications">Telecommunications</option>
              <option value="Transportation">Transportation</option>
              <option value="Other">Other</option>
            </select>
          </div>
          <div class="profile-form-group">
            <label class="profile-form-label">Country
              <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                 title="Your company's primary country of operation. This affects tax and regulatory compliance."></i>
            </label>
            <select class="profile-form-control" name="country" id="country">
              <option value="" disabled>Select Country</option>
              <option value="Malaysia">Malaysia</option>
              <option value="Singapore">Singapore</option>
              <option value="Thailand">Thailand</option>
              <option value="Indonesia">Indonesia</option>
              <option value="Vietnam">Vietnam</option>
            </select>
          </div>
          <div class="profile-form-group">
            <label class="profile-form-label">Business Email
              <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                 title="Official email address for business communications and notifications."></i>
            </label>
            <input type="email" class="profile-form-control" name="email" id="email">
          </div>
          <div class="profile-form-buttons">
            <button type="button" class="btn btn-light" onclick="toggleEditMode()">
                <i class="fas fa-times"></i> Cancel
            </button>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Save
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Right Content - Branch Companies -->
    <div class="profile-details">
      <div class="profile-details-header">
        <h3 class="profile-details-title">Branch Companies
          <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
             title="Manage your company's branch offices. Each branch can have its own tax registration and contact details."></i>
        </h3>
        <button class="btn btn-primary" id="addBranchButton" onclick="addNewBranch()">
            <i class="fas fa-plus"></i> Add Branch
        </button>
      </div>

      <!-- Branch Companies Table -->
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>No</th>
              <th>Company Name</th>
              <th>TIN <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="top" 
                        title="Tax Identification Number unique to each branch"></i></th>
              <th>BRN <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="top" 
                        title="Business Registration Number for branch identification"></i></th>
              <th>Location</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="branchTableBody">
            <!-- Branch list will be populated here -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Branch Modal -->
<div class="modal fade" id="branchModal" tabindex="-1" aria-labelledby="branchModalLabel" aria-hidden="true" data-bs-backdrop="static">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="branchModalLabel">Add Branch Company
          <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
             title="Add a new branch office with its own registration and contact details."></i>
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-0">
        <form id="branchForm" onsubmit="return false;">
          <input type="hidden" id="branchId" name="branchId">
          
          <!-- Logo Upload -->
          <div class="text-center p-4 border-bottom bg-light">
            <img id="branchLogo" src="/assets/img/noimage.png" alt="Branch Logo" class="profile-image mb-3">
            <div>
              <input type="file" id="branchLogoInput" name="branchLogo" accept="image/*" style="display: none;">
              <button type="button" class="btn btn-light" onclick="document.getElementById('branchLogoInput').click()">
                <i class="fas fa-upload"></i> Upload Logo
              </button>
            </div>
          </div>

          <!-- Tabs -->
          <ul class="nav nav-tabs nav-fill" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active px-4" data-bs-toggle="tab" data-bs-target="#registration" type="button" role="tab" aria-controls="registration" aria-selected="true">Registration</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link px-4" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="false">Details</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link px-4" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">Contact</button>
            </li>
          </ul>

          <!-- Tab Content -->
          <div class="tab-content p-4">
            <!-- Registration Tab -->
            <div class="tab-pane fade show active" id="registration" role="tabpanel" aria-labelledby="registration-tab">
              <div class="row g-3">
                <div class="col-12">
                  <label class="form-label" for="branchTin">Tax Identification Number (TIN) <span class="text-danger">*</span>
                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                       title="Branch's unique tax identification number. Required for tax compliance."></i>
                  </label>
                  <input type="text" class="form-control" id="branchTin" name="branchTin" required>
                </div>
                <div class="col-12">
                  <label class="form-label" for="branchBrn">Business Registration Number (BRN) <span class="text-danger">*</span>
                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                       title="Branch's business registration number. Must match official records."></i>
                  </label>
                  <input type="text" class="form-control" id="branchBrn" name="branchBrn" required>
                </div>
              </div>
            </div>

            <!-- Details Tab -->
            <div class="tab-pane fade" id="details" role="tabpanel" aria-labelledby="details-tab">
              <div class="row g-3">
                <div class="col-12">
                  <label class="form-label" for="branchName">Branch Name <span class="text-danger">*</span>
                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                       title="Official name of the branch office as registered."></i>
                  </label>
                  <input type="text" class="form-control" id="branchName" name="branchName" required>
                </div>
                <div class="col-12">
                  <label class="form-label" for="branchLocation">Location <span class="text-danger">*</span>
                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                       title="Country where this branch operates. Affects tax calculations and compliance."></i>
                  </label>
                  <select class="form-control" id="branchLocation" name="branchLocation" required>
                    <option value="">Select Location</option>
                    <option value="Malaysia">Malaysia</option>
                    <option value="Singapore">Singapore</option>
                    <option value="Thailand">Thailand</option>
                    <option value="Indonesia">Indonesia</option>
                    <option value="Vietnam">Vietnam</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Contact Tab -->
            <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
              <div class="row g-3">
                <div class="col-12">
                  <label class="form-label" for="branchContact">Contact Person
                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                       title="Primary contact person for this branch office."></i>
                  </label>
                  <input type="text" class="form-control" id="branchContact" name="branchContact">
                </div>
                <div class="col-12">
                  <label class="form-label" for="branchEmail">Contact Email
                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                       title="Official email address for branch communications."></i>
                  </label>
                  <input type="email" class="form-control" id="branchEmail" name="branchEmail">
                </div>
                <div class="col-12">
                  <label class="form-label" for="branchAddress">Branch Address
                    <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" 
                       title="Physical address of the branch office."></i>
                  </label>
                  <textarea class="form-control" id="branchAddress" name="branchAddress" rows="3"></textarea>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light" data-bs-dismiss="modal">
            <i class="fas fa-times"></i> Cancel
        </button>
        <button type="button" class="btn btn-primary" onclick="saveBranch()">
            <i class="fas fa-save"></i> Save Branch
        </button>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block scripts %}
<script src="/assets/js/company-settings.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock %} 