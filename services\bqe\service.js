const axios = require('axios');
const qs = require('querystring');
const path = require('path');
const fs = require('fs');
const forge = require('node-forge');
const CryptoJS = require('crypto-js');
const crypto = require('crypto');
require('dotenv').config();
const { WP_CONFIGURATION } = require('../../models');

async function getConfig() {
  const config = await WP_CONFIGURATION.findOne({
    where: {
      Type: 'LHDN',
      IsActive: 1
    },
    order: [['CreateTS', 'DESC']]
  });

  if (!config) {
    throw new Error('LHDN configuration not found');
  }

  let settings = config.Settings;
  if (typeof settings === 'string') {
    settings = JSON.parse(settings);
  }

  return settings;
}


let httpOptions = {
  client_id: process.env.CLIENT_ID,
  client_secret: process.env.CLIENT_SECRET,
  grant_type: 'client_credentials',
  scope: 'InvoicingAPI'
};

async function getTokenAsTaxPayer(req) {
  try {
    console.log('[BQE Service] Session token:', req.session?.accessToken ? 'Present' : 'Missing');
    console.log('[BQE Service] Token expiry:', req.session?.tokenExpiryTime);

    if (req.session?.accessToken && req.session?.tokenExpiryTime > Date.now()) {
      console.log('[BQE Service] Using existing session token');
      return { access_token: req.session.accessToken };
    }

    // Get TIN from session or database
    let userTin = null;

    // Get TIN from session if available
    if (req?.session?.user?.TIN) {
      userTin = req.session.user.TIN;
      console.log('[BQE Service] Using TIN from session:', userTin);
    } else if (req?.session?.user?.id) {
      // Try to get TIN from database if not in session
      try {
        const { WP_USER_REGISTRATION } = require('../../models');
        const user = await WP_USER_REGISTRATION.findOne({
          where: { ID: req.session.user.id },
          attributes: ['TIN']
        });
        if (user && user.TIN) {
          userTin = user.TIN;
          console.log('[BQE Service] Using TIN from database:', userTin);

          // Update session with TIN for future use
          if (req.session?.user) {
            req.session.user.TIN = user.TIN;
            console.log('[BQE Service] Updated session with TIN from database');
          }
        }
      } catch (dbError) {
        console.warn('[BQE Service] Could not retrieve TIN from database:', dbError.message);
      }
    }

    // Validate TIN is available
    if (!userTin) {
      console.error('[BQE Service] No TIN available for token generation');
      throw new Error('TIN is required for authentication but not found in session or database');
    }

    // Try to get TIN from LHDN configuration as fallback
    if (!userTin) {
      try {
        const settings = await getConfig();
        if (settings.tin) {
          userTin = settings.tin;
          console.log('[BQE Service] Using TIN from LHDN configuration:', userTin);
        }
      } catch (configError) {
        console.warn('[BQE Service] Could not retrieve TIN from configuration:', configError.message);
      }
    }

    // Prepare headers
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded'
    };

    // Add onbehalfof header if TIN is available
    if (userTin) {
      headers['onbehalfof'] = userTin;
      console.log('[BQE Service] Using TIN in onbehalfof header:', userTin);
    } else {
      console.log('[BQE Service] No TIN available for onbehalfof header');
    }

    console.log('[BQE Service] Getting new token with options:', httpOptions);
    console.log('[BQE Service] Request headers:', headers);
    console.log('[BQE Service] Authentication TIN being used:', userTin);

    const response = await axios.post(
      `${process.env.PREPROD_BASE_URL}/connect/token`,
      qs.stringify(httpOptions),
      {
        headers
      }
    );

    if (response.status === 200) {
      console.log('[BQE Service] Successfully got new token');
      console.log('[BQE Service] Token will authenticate as TIN:', userTin);
      return response.data;
    }

  } catch (err) {
    console.error('[BQE Service] Error getting token:', err.message);
    if (err.response?.status === 429) {
      console.log('[BQE Service] Current iteration hitting Rate Limit 429 of LHDN Taxpayer Token API, retrying...')
      const rateLimitReset = err.response.headers["x-rate-limit-reset"];

      if (rateLimitReset) {
        const resetTime = new Date(rateLimitReset).getTime();
        const currentTime = Date.now();
        const waitTime = resetTime - currentTime;

        if (waitTime > 0) {
          console.log('=======================================================================================');
          console.log('              LHDN Taxpayer Token API hitting rate limit HTTP 429                  ');
          console.log(`              Refetching................. (Waiting time: ${waitTime} ms)                  `);
          console.log('=======================================================================================');
          await new Promise(resolve => setTimeout(resolve, waitTime));
          return await getTokenAsTaxPayer(req);
        }
      }
    }
    throw new Error(`Failed to get token: ${err.message}`);
  }
}

const handleSubmissionError = (err) => {
  if (err.response?.data) {
    const errorData = err.response.data;

    // Handle structured error response
    if (errorData.error && typeof errorData.error === 'object') {
      return {
        code: errorData.error.code || 'UNKNOWN_ERROR',
        message: errorData.error.message || 'Unknown error occurred',
        details: errorData.error.details || []
      };
    }

    // Handle string error response
    if (typeof errorData.error === 'string') {
      return {
        code: 'API_ERROR',
        message: errorData.error,
        details: []
      };
    }
  }

  // Handle unexpected errors
  return {
    code: 'UNKNOWN_ERROR',
    message: err.message || 'An unexpected error occurred',
    details: []
  };
};

async function submitDocument(docs, token) {
  try {
    console.log('[BQE Service] Submitting document with token:', token ? 'Present' : 'Missing');
    console.log('[BQE Service] Number of documents:', docs?.length || 0);

    // Extract and log TIN from document for debugging
    if (docs && docs.length > 0 && docs[0].document) {
      try {
        const docJson = JSON.parse(Buffer.from(docs[0].document, 'base64').toString());
        const docTin = docJson?.Invoice?.[0]?.AccountingSupplierParty?.[0]?.Party?.[0]?.PartyTaxScheme?.[0]?.CompanyID?.[0]?._ || 'TIN not found';
        console.log('[BQE Service] Document Supplier TIN:', docTin);
        console.log('[BQE Service] Document Code Number:', docs[0].codeNumber || 'Not specified');
        console.log('[BQE Service] Document Format:', docs[0].format || 'Not specified');
      } catch (parseError) {
        console.error('[BQE Service] Error parsing document to extract TIN:', parseError.message);
      }
    }

    console.log('[BQE Service] Document payload structure:', {
      documentsCount: docs?.length || 0,
      firstDocumentKeys: docs?.[0] ? Object.keys(docs[0]) : [],
      hasDocument: !!(docs?.[0]?.document),
      documentLength: docs?.[0]?.document?.length || 0
    });

    const response = await axios.post(
      `${process.env.PREPROD_BASE_URL}/api/v1.0/documentsubmissions`,
      { documents: docs },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }
    );

    // LHDN returns validation errors in the response even with 200 status
    if (response.data.rejectedDocuments?.length > 0) {
      const rejectedDoc = response.data.rejectedDocuments[0];
      throw {
        response: {
          data: {
            error: {
              code: rejectedDoc.error?.code || 'VALIDATION_ERROR',
              message: rejectedDoc.error?.message || 'Document validation failed',
              details: rejectedDoc.error?.details || []
            }
          }
        }
      };
    }

    console.log('LHDN API Response:', response.data);
    return { status: 'success', data: response.data };

  } catch (err) {
    console.error('[BQE Service] Error in submitDocument:', err.message);
    console.error('[BQE Service] Error response:', err.response?.data);

    // Handle LHDN API specific error responses
    if (err.response?.data?.error) {
      const error = err.response.data.error;

      // Check for TIN mismatch errors
      if (error.details && Array.isArray(error.details)) {
        const tinMismatchError = error.details.find(detail =>
          detail.message && detail.message.includes('authenticated TIN and documents TIN is not matching')
        );

        if (tinMismatchError) {
          console.log('[BQE Service] TIN MISMATCH DETECTED:');
          console.log('[BQE Service] Error details:', JSON.stringify(error.details, null, 2));

          // Try to extract TINs for debugging
          if (docs && docs.length > 0 && docs[0].document) {
            try {
              const docJson = JSON.parse(Buffer.from(docs[0].document, 'base64').toString());
              const docTin = docJson?.Invoice?.[0]?.AccountingSupplierParty?.[0]?.Party?.[0]?.PartyTaxScheme?.[0]?.CompanyID?.[0]?._ || 'TIN not found';
              console.log('[BQE Service] Document TIN from payload:', docTin);
            } catch (parseError) {
              console.error('[BQE Service] Could not parse document TIN:', parseError.message);
            }
          }

          return {
            status: 'failed',
            error: {
              code: 'TIN_MISMATCH',
              message: 'The authenticated TIN and documents TIN is not matching',
              details: error.details
            }
          };
        }
      }

      // Map LHDN error codes to appropriate messages
      const errorMapping = {
        'ValidationError': 'Document validation failed',
        'DuplicateDocument': 'This document has already been submitted',
        'InvalidDocumentType': 'Invalid document type',
        'InvalidStructure': 'Invalid document structure',
        'BusinessRuleViolation': 'Business rule violation',
        'SystemError': 'System error occurred'
      };

      return {
        status: 'failed',
        error: {
          code: error.code,
          message: errorMapping[error.code] || error.message,
          details: error.details || []
        }
      };
    }

    // Handle network or other errors
    return {
      status: 'failed',
      error: {
        code: 'SUBMISSION_ERROR',
        message: err.message,
        details: []
      }
    };
  }
}





async function getDocumentDetails(irb_uuid, token) {
  try {
    const response = await axios.get(
      `${process.env.PREPROD_BASE_URL}/api/v1.0/documents/${irb_uuid}/details`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    return { status: 'success', data: response.data };
  } catch (err) {
    if (err.response?.status === 429) {
      const rateLimitReset = err.response.headers["x-rate-limit-reset"];
      if (rateLimitReset) {
        const resetTime = new Date(rateLimitReset).getTime();
        const currentTime = Date.now();
        const waitTime = resetTime - currentTime;

        console.log('=======================================================================================');
        console.log('              LHDN DocumentDetails API hitting rate limit HTTP 429                      ');
        console.log('                 Retrying for current iteration.................                       ');
        console.log(`                     (Waiting time: ${waitTime} ms)                                       `);
        console.log('=======================================================================================');

        if (waitTime > 0) {
          await new Promise(resolve => setTimeout(resolve, waitTime));
          return await getDocumentDetails(irb_uuid, token);
        }
      }
    }
    console.error(`Failed to get IRB document details for document UUID ${irb_uuid}:`, err.message);
    throw err;
  }
}

async function cancelValidDocumentBySupplier(irb_uuid, cancellation_reason, token) {
  try {
    const settings = await getConfig();
    const baseUrl = settings.environment === 'production' ?
      settings.middlewareUrl : settings.middlewareUrl;

    const payload = {
      status: 'cancelled',
      reason: cancellation_reason || 'NA'
    };

    const response = await axios.put(
      `${baseUrl}/api/v1.0/documents/state/${irb_uuid}/state`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }
    );

    return { status: 'success', data: response.data };
  } catch (err) {
    if (err.response?.status === 429) {
      const rateLimitReset = err.response.headers["x-rate-limit-reset"];
      if (rateLimitReset) {
        const resetTime = new Date(rateLimitReset).getTime();
        const currentTime = Date.now();
        const waitTime = resetTime - currentTime;

        console.log('=======================================================================================');
        console.log('              LHDN Cancel Document API hitting rate limit HTTP 429                      ');
        console.log('                 Retrying for current iteration.................                       ');
        console.log(`                     (Waiting time: ${waitTime} ms)                                       `);
        console.log('=======================================================================================');

        if (waitTime > 0) {
          await new Promise(resolve => setTimeout(resolve, waitTime));
          return await cancelValidDocumentBySupplier(irb_uuid, cancellation_reason, token);
        }
      }
    }
    console.error(`Failed to cancel document for IRB UUID ${irb_uuid}:`, err.message);
    throw err;
  }
}

function jsonToBase64(jsonObj) {
  const jsonString = JSON.stringify(jsonObj);
  const base64String = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(jsonString));
  return base64String;
}

function calculateSHA256(jsonObj) {
  const jsonString = JSON.stringify(jsonObj);
  const hash = CryptoJS.SHA256(jsonString);
  return hash.toString(CryptoJS.enc.Hex);
}

function getCertificatesHashedParams(documentJson) {
  try {
    const privateKeyPath = path.join(__dirname, process.env.PRIVATE_KEY_FILE_PATH);
    const certificatePath = path.join(__dirname, process.env.PRIVATE_CERT_FILE_PATH);

    const privateKeyPem = fs.readFileSync(privateKeyPath, 'utf8');
    const p12Buffer = fs.readFileSync(certificatePath);

    const p12Asn1 = forge.asn1.fromDer(forge.util.createBuffer(p12Buffer));
    const p12 = forge.pkcs12.pkcs12FromAsn1(p12Asn1, process.env.CERT_PASSWORD || '');

    const certBags = p12.getBags({ bagType: forge.pki.oids.certBag })[forge.pki.oids.certBag];
    const certificate = certBags[0].cert;
    const privateKey = forge.pki.privateKeyFromPem(privateKeyPem);

    // Get full issuer name and serial number
    const issuerName = certificate.issuer.attributes
      .map(attr => {
        const type = attr.shortName.toUpperCase();
        const value = attr.value.replace(/['"]/g, '').trim();
        return `${type}=${value}`;
      })
      .reverse()
      .join(',');

    const serialNumber = certificate.serialNumber;

    // Calculate document digest
    const documentWithoutSignature = { ...documentJson };
    delete documentWithoutSignature.Signature;
    delete documentWithoutSignature.UBLExtensions;

    // Sort keys to ensure consistent ordering
    const sortKeys = (obj) => {
      if (Array.isArray(obj)) {
        return obj.map(sortKeys);
      }
      if (obj && typeof obj === 'object') {
        return Object.keys(obj).sort().reduce((acc, key) => {
          acc[key] = sortKeys(obj[key]);
          return acc;
        }, {});
      }
      return obj;
    };

    const sortedDoc = sortKeys(documentWithoutSignature);
    const cleanDocJson = JSON.stringify(sortedDoc);
    const docDigest = crypto.createHash('sha256')
      .update(cleanDocJson, 'utf8')
      .digest('base64');

    // Calculate certificate digest
    const derBytes = forge.asn1.toDer(forge.pki.certificateToAsn1(certificate)).getBytes();
    const certDigest = crypto.createHash('sha256')
      .update(derBytes, 'binary')
      .digest('base64');

    const signingTime = new Date().toISOString();

    // Create signed properties
    const signedProperties = {
      "Target": "signature",
      "SignedProperties": [
        {
          "Id": "id-xades-signed-props",
          "SignedSignatureProperties": [
            {
              "SigningTime": [{ "_": signingTime }],
              "SigningCertificate": [
                {
                  "Cert": [
                    {
                      "CertDigest": [
                        {
                          "DigestMethod": [{
                            "_": "",
                            "Algorithm": "http://www.w3.org/2001/04/xmlenc#sha256"
                          }],
                          "DigestValue": [{ "_": certDigest }]
                        }
                      ],
                      "IssuerSerial": [
                        {
                          "X509IssuerName": [{ "_": issuerName }],
                          "X509SerialNumber": [{ "_": serialNumber }]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    };

    // Calculate signed properties hash
    const sortedProps = sortKeys(signedProperties);
    const signedpropsString = JSON.stringify(sortedProps);
    const signedpropsHash = crypto.createHash('sha256')
      .update(signedpropsString, 'utf8')
      .digest('base64');

    // Create signature
    const dataToSign = cleanDocJson + signedpropsString; // Sign the actual data
    const md = forge.md.sha256.create();
    md.update(dataToSign, 'utf8');
    const signature = privateKey.sign(md);
    const signatureBase64 = forge.util.encode64(signature);

    // Create UBL structure
    let certificateJsonPortion_Signature = [
      {
        "ID": [{ "_": "urn:oasis:names:specification:ubl:signature:Invoice" }],
        "SignatureMethod": [{ "_": "urn:oasis:names:specification:ubl:dsig:enveloped:xades" }]
      }
    ];

    let certificateJsonPortion_UBLExtensions = [
      {
        "UBLExtension": [
          {
            "ExtensionURI": [{ "_": "urn:oasis:names:specification:ubl:dsig:enveloped:xades" }],
            "ExtensionContent": [
              {
                "UBLDocumentSignatures": [
                  {
                    "SignatureInformation": [
                      {
                        "ID": [{ "_": "urn:oasis:names:specification:ubl:signature:1" }],
                        "ReferencedSignatureID": [{ "_": "urn:oasis:names:specification:ubl:signature:Invoice" }],
                        "Signature": [
                          {
                            "Id": "signature",
                            "SignedInfo": [
                              {
                                "CanonicalizationMethod": [{
                                  "_": "",
                                  "Algorithm": "https://www.w3.org/TR/xml-c14n11/#"
                                }],
                                "SignatureMethod": [{
                                  "_": "",
                                  "Algorithm": "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"
                                }],
                                "Reference": [
                                  {
                                    "Id": "id-doc-signed-data",
                                    "URI": "",
                                    "Transforms": [
                                      {
                                        "Transform": [
                                          {
                                            "_": "",
                                            "Algorithm": "http://www.w3.org/TR/1999/REC-xpath-19991116",
                                            "XPath": "not(//ancestor-or-self::ext:UBLExtensions)"
                                          },
                                          {
                                            "_": "",
                                            "Algorithm": "http://www.w3.org/TR/1999/REC-xpath-19991116",
                                            "XPath": "not(//ancestor-or-self::cac:Signature)"
                                          },
                                          {
                                            "_": "",
                                            "Algorithm": "http://www.w3.org/2006/12/xml-c14n11"
                                          }
                                        ]
                                      }
                                    ],
                                    "DigestMethod": [{
                                      "_": "",
                                      "Algorithm": "http://www.w3.org/2001/04/xmlenc#sha256"
                                    }],
                                    "DigestValue": [{ "_": docDigest }]
                                  },
                                  {
                                    "Id": "id-xades-signed-props",
                                    "Type": "http://uri.etsi.org/01903/v1.3.2#SignedProperties",
                                    "URI": "#id-xades-signed-props",
                                    "DigestMethod": [{
                                      "_": "",
                                      "Algorithm": "http://www.w3.org/2001/04/xmlenc#sha256"
                                    }],
                                    "DigestValue": [{ "_": signedpropsHash }]
                                  }
                                ]
                              }
                            ],
                            "SignatureValue": [{ "_": signatureBase64 }],
                            "KeyInfo": [
                              {
                                "X509Data": [
                                  {
                                    "X509Certificate": [{ "_": forge.util.encode64(derBytes) }],
                                    "X509SubjectName": [{ "_": issuerName }],
                                    "X509IssuerSerial": [
                                      {
                                        "X509IssuerName": [{ "_": issuerName }],
                                        "X509SerialNumber": [{ "_": serialNumber }]
                                      }
                                    ]
                                  }
                                ]
                              }
                            ],
                            "Object": [
                              {
                                "QualifyingProperties": [signedProperties]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ];

    return {
      certificateJsonPortion_Signature,
      certificateJsonPortion_UBLExtensions
    };

  } catch (error) {
    console.error('Error in getCertificatesHashedParams:', error);
    throw new Error(`Failed to process digital signature: ${error.message}`);
  }
}

module.exports = {
  getTokenAsTaxPayer,
  submitDocument,
  getDocumentDetails,
  cancelValidDocumentBySupplier,
  jsonToBase64,
  calculateSHA256,
  getCertificatesHashedParams,
  getConfig
};