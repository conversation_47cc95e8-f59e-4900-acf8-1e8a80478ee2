/* Submission Error Dialog Styles */
.error-dialog {
    text-align: center;
    padding: 1.5rem;
}

.error-icon {
    margin-bottom: 1rem;
}

.error-icon i {
    color: #f7b84b !important;
}

.error-title {
    color: #405189;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.file-info {
    background: rgba(64, 81, 137, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.file-name {
    color: #405189;
    font-weight: 500;
    margin-bottom: 0.25rem;
    word-break: break-all;
}

.validation-errors {
    text-align: left;
}

.alert-warning {
    background-color: #FFF8E6;
    border: 1px solid #FFEEBA;
    border-radius: 8px;
    padding: 1rem;
}

.alert-heading {
    color: #856404;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.error-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.error-item {
    padding: 0.75rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.error-item:last-child {
    border-bottom: none;
}

.error-item strong {
    color: #DC2626;
    font-size: 0.875rem;
    display: block;
    margin-bottom: 0.25rem;
}

.error-description {
    color: #6B7280;
    font-size: 0.875rem;
    line-height: 1.4;
}

.error-message {
    color: #4B5563;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

/* Reference Number Section */
.error-dialog code {
    background: #F3F4F6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.875rem;
    color: #374151;
}

/* SweetAlert2 Customization */
.swal2-icon {
    border: none !important;
    margin: 1.5rem auto !important;
}

.swal2-icon.swal2-error {
    color: #DC2626 !important;
}

.swal2-title {
    color: #1F2937 !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    padding: 0 !important;
    margin: 0 0 1rem 0 !important;
}

.swal2-html-container {
    margin: 0 !important;
    padding: 0 1rem !important;
}

.swal2-confirm {
    padding: 0.625rem 1.5rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

.swal2-confirm:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(64, 81, 137, 0.1) !important;
}

/* Loading State */
.error-dialog .loading {
    display: inline-block;
    margin-top: 1rem;
}

.error-dialog .loading-dots {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.error-dialog .loading-dots span {
    width: 4px;
    height: 4px;
    background-color: #6B7280;
    border-radius: 50%;
    animation: loading-dots 1s infinite;
}

.error-dialog .loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.error-dialog .loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes loading-dots {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(0.7); opacity: 0.5; }
}

/* Responsive Adjustments */
@media (max-width: 640px) {
    .error-dialog {
        max-width: 100%;
        padding: 1rem;
    }

    .error-message {
        font-size: 0.875rem;
    }

    .error-description {
        font-size: 0.8125rem;
    }
}

/* Error Dialog Container */
.error-dialog-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.error-dialog-popup {
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.error-dialog-content {
    padding: 1.5rem;
} 