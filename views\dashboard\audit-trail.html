{% extends 'layout.html' %}

{% block head %}
<title>Audit Trail - eInvoice Portal</title>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link href="/assets/css/components/outbound-invoice-modal.css" rel="stylesheet">
<link href="/assets/css/pages/outbound/card.css" rel="stylesheet">
<link href="/assets/css/pages/outbound/outbound-table.css" rel="stylesheet">
<link href="/assets/css/pages/profile.css" rel="stylesheet">
<!-- SDK Updates style for modern card layout -->
<style>
  .sdk-updates-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  .sdk-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
  .sdk-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
  }
  .sdk-actions {
    display: flex;
    gap: 1rem;
  }
  .sdk-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #4361ee;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  .sdk-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #2ec4b6;
  }
  .sdk-date {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
    padding-right: 100px;
  }
  .sdk-section {
    margin-bottom: 1.5rem;
  }
  .sdk-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #4361ee;
    margin-bottom: 0.75rem;
  }
  .sdk-section-items {
    list-style-type: none;
    padding-left: 0;
  }
  .sdk-section-items li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.5;
  }
  .sdk-section-items li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: #4361ee;
    font-weight: bold;
  }
  .sdk-alert {
    background-color: rgba(67, 97, 238, 0.1);
    border-left: 4px solid #4361ee;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
  }
  .sdk-alert p {
    margin-bottom: 0;
  }
  .sdk-filters {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
  }
  .sdk-filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  .sdk-filter-btn:hover {
    background-color: #e9ecef;
  }
  .sdk-filter-btn.active {
    background-color: #4361ee;
    color: #fff;
    border-color: #4361ee;
  }
  .error-container {
    text-align: center;
    padding: 2rem;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  .error-icon {
    font-size: 4rem;
    color: #dc3545;
    margin-bottom: 1rem;
  }
  .retry-btn {
    margin-top: 1rem;
  }
</style>
<script src="/assets/js/shared/utils.js"></script>
{% endblock %}

{% block content %}
<div class="sdk-updates-container">
  <!-- Welcome Card -->
  <div class="profile-welcome-card mb-4">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <div class="welcome-icon">
          <i class="bi bi-shield-check"></i>
        </div>
        <div class="welcome-content">
          <h4 class="mb-1">Audit Trail</h4>
          <p class="mb-0">Monitor and track system activities and user actions.</p>
        </div>
      </div>
      <div class="welcome-datetime">
        <div class="current-time">
          <i class="bi bi-clock"></i>
          <span id="currentTime">00:00:00 AM</span>
        </div>
        <div class="current-date">
          <i class="bi bi-calendar3"></i>
          <span id="currentDate">Loading...</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Info Alert -->
  <div class="sdk-alert">
    <p><strong>Note:</strong> This page shows the audit trail of all activities in the system. Use the filters to narrow down your search.</p>
  </div>

  <!-- Filter Card -->
  <div class="sdk-card">
    <div class="sdk-section">
      <div class="sdk-section-title">Filters</div>
      <div class="sdk-filters">
        <input type="date" id="startDate" class="form-control" placeholder="Start Date">
        <input type="date" id="endDate" class="form-control" placeholder="End Date">
        <select id="actionType" class="form-select">
          <option value="">All Actions</option>
          <option value="LOGIN">Login</option>
          <option value="LOGOUT">Logout</option>
          <option value="CREATE">Create</option>
          <option value="UPDATE">Update</option>
          <option value="DELETE">Delete</option>
          <option value="VIEW">View</option>
        </select>
        <select id="moduleType" class="form-select">
          <option value="">All Modules</option>
          <option value="AUTH">Authentication</option>
          <option value="USER">User Management</option>
          <option value="INVOICE">Invoice</option>
          <option value="SETTINGS">Settings</option>
        </select>
        <input type="text" id="userFilter" class="form-control" placeholder="Filter by user">
      </div>
    </div>
  </div>

  <!-- Stats Card -->
  <div class="sdk-card">
    <div class="sdk-section">
      <div class="row text-center">
        <div class="col-md-4 mb-2">
          <div class="sdk-section-title">Total Activities</div>
          <h5 id="totalActivities">0</h5>
        </div>
        <div class="col-md-4 mb-2">
          <div class="sdk-section-title">Today's Activities</div>
          <h5 id="todayActivities">0</h5>
        </div>
        <div class="col-md-4 mb-2">
          <div class="sdk-section-title">Active Users</div>
          <h5 id="activeUsers">0</h5>
        </div>
      </div>
    </div>
  </div>

  <!-- Table Card -->
  <div class="sdk-card">
    <div class="sdk-section">
      <div class="table-responsive">
        <table id="logTable" class="outbound-table table">
          <thead>
            <tr>
              <th>TIMESTAMP</th>
              <th>USER</th>
              <th>MODULE</th>
              <th>ACTION</th>
              <th>DESCRIPTION</th>
              <th>STATUS</th>
              <th>IP ADDRESS</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>
                       
<!-- Alert Template -->
<template id="alertTemplate">
    <div class="custom-alert">
        <i class="alert-icon"></i>
        <div class="alert-content">
            <div class="alert-title"></div>
            <div class="alert-message"></div>
        </div>
        <button class="close-btn" onclick="this.parentElement.remove()">×</button>
    </div>
</template>
{% endblock %}

{% block scripts %}
<script src="/assets/js/audit-trail.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize LogTableManager
    const logManager = LogTableManager.getInstance();
});
</script>
{% endblock %}