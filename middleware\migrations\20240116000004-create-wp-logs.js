'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_LOGS', {
      ID: { 
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      Description: Sequelize.STRING,
      CreateTS: Sequelize.DATE,
      LoggedUser: Sequelize.STRING,
      IPAddress: Sequelize.STRING
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_LOGS');
  }
}; 