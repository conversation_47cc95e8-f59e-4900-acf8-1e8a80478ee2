const { getTokenAsTaxPayer, submitDocument, getCertificatesHashedParams } = require('./service');
const { WP_SUBMISSION_STATUS } = require('../../models');
const moment = require('moment');
const { Op } = require('sequelize');
const BQEDataProcessor = require('./dataProcessor');
const { mapBQEToLHDNFormat } = require('./mapper');
const BQELogger = require('./bqeLogger');

class SubmitToBQE {
    constructor(req = null) {
        this.baseUrl = process.env.PREPROD_BASE_URL;
        this.req = req;
        this.logger = new BQELogger();
    }

    async submitInvoice(invoiceData, version) {
        try {
            console.log('Starting BQE invoice submission with version:', version);
            console.log('Raw invoice data received:', invoiceData);

            // 1. Process BQE data into standard format
            const processor = new BQEDataProcessor();
            const processedData = await processor.processRawBQEData(invoiceData);
            console.log('Processed BQE data:', processedData);

            // 2. Set issuance date properly
            const now = moment().utc();
            if (!processedData._rawInvoice) {
                processedData._rawInvoice = {};
            }
            processedData._rawInvoice.issuanceDate = now.format();
            processedData._rawInvoice.issuanceTime = now.format('HH:mm:ss');

            // Log processed data with issuance date
            await this.logger.writeProcessingLog(
                processedData._rawInvoice.invoiceNumber || 'unknown',
                {
                    stage: 'pre_mapping',
                    timestamp: moment().format(),
                    data: {
                        processedData,
                        version,
                        issuanceDate: processedData._rawInvoice.issuanceDate,
                        issuanceTime: processedData._rawInvoice.issuanceTime
                    }
                }
            );

            // Validate tax data before mapping
            if (!processedData.tax_info) {
                throw new Error('Missing tax information');
            }

            // Log key data before mapping
            console.log('Data before mapping:', {
                hasRawInvoice: !!processedData._rawInvoice,
                rawInvoiceNumber: processedData._rawInvoice?.invoiceNumber,
                hasSupplier: !!processedData.supplier,
                hasBuyer: !!processedData.buyer,
                hasTaxInfo: !!processedData.tax_info,
                taxInfo: processedData.tax_info,
                hasLineItems: processedData.line_items && processedData.line_items.length > 0,
                lineItemsCount: processedData.line_items?.length || 0
            });

            // 3. Map to LHDN format using bqeMapper
            const lhdnFormat = mapBQEToLHDNFormat(processedData, version);

            // Check if lhdnFormat has values or is empty
            const hasValues = lhdnFormat.Invoice &&
                              lhdnFormat.Invoice[0] &&
                              lhdnFormat.Invoice[0].ID &&
                              lhdnFormat.Invoice[0].ID[0] &&
                              lhdnFormat.Invoice[0].ID[0]._ ? true : false;

            console.log('Mapped to LHDN format - has values:', hasValues);
            console.log('Invoice ID value:', lhdnFormat.Invoice?.[0]?.ID?.[0]?._);

            // Log mapped data
            await this.logger.writeMappingLog(
                processedData._rawInvoice.invoiceNumber,
                {
                    stage: 'mapped',
                    timestamp: moment().format(),
                    data: lhdnFormat
                }
            );

            // 4. Handle digital signature for v1.1
            if (version === '1.1') {
                console.log('Adding digital signature for v1.1');
                const { certificateJsonPortion_Signature, certificateJsonPortion_UBLExtensions } =
                    getCertificatesHashedParams(lhdnFormat);

                lhdnFormat.Invoice[0].Signature = certificateJsonPortion_Signature;
                lhdnFormat.Invoice[0].UBLExtensions = certificateJsonPortion_UBLExtensions;
            }

            // 5. Create submission payload
            const documentHash = require('crypto')
                .createHash('sha256')
                .update(JSON.stringify(lhdnFormat))
                .digest('hex');

            // Ensure we have a valid invoice number
            const invoiceNumber = lhdnFormat.Invoice[0].ID[0]._ || processedData._rawInvoice?.invoiceNumber || "UNKNOWN";
            console.log('Using invoice number for submission:', invoiceNumber);

            const documents = [{
                format: "JSON",
                documentHash: documentHash,
                codeNumber: invoiceNumber,
                document: Buffer.from(JSON.stringify(lhdnFormat)).toString('base64')
            }];

            // 6. Get token and submit
            const tokenResponse = await getTokenAsTaxPayer(this.req);
            if (!tokenResponse?.access_token) {
                throw new Error('Failed to get access token');
            }

            // 7. Submit to LHDN
            const result = await submitDocument(documents, tokenResponse.access_token);

            // Handle submission result
            if (!result || !result.data) {
                throw new Error('Invalid response from LHDN');
            }

            // Return appropriate response
            return this.handleSubmissionResult(result, processedData._rawInvoice.invoiceNumber);

        } catch (error) {
            console.error('Error in BQE submission:', error);
            throw this.formatError(error);
        }
    }

    handleSubmissionResult(result, invoiceNumber) {
        // Check for rejected documents
        if (result.data.rejectedDocuments?.length > 0) {
            const rejectedDoc = result.data.rejectedDocuments[0];

            // Log the rejected document structure for debugging
            console.log('Rejected document structure:', JSON.stringify(rejectedDoc, null, 2));

            // The validation error structure can be in different places
            let errorDetails = rejectedDoc;

            // If there's an error property, use that
            if (rejectedDoc.error) {
                errorDetails = rejectedDoc.error;
            }

            throw {
                code: errorDetails.code || rejectedDoc.error?.code || 'VALIDATION_ERROR',
                message: errorDetails.message || rejectedDoc.error?.message || 'Document rejected by LHDN',
                details: errorDetails // Pass the entire error structure
            };
        }

        // Check for accepted documents
        if (result.data.acceptedDocuments?.length > 0) {
            const acceptedDoc = result.data.acceptedDocuments[0];

            // Log the complete accepted document data for debugging
            console.log('============= LHDN ACCEPTED DOCUMENT =============');
            console.log(JSON.stringify(acceptedDoc, null, 2));
            console.log('=================================================');

            // CRITICAL FIX: Extract UUIDs from the correct location in the response
            // The actual UUIDs are in the acceptedDoc fields, not in some nested property
            const uuid = acceptedDoc.id || acceptedDoc.uuid || acceptedDoc.documentId || '';
            const submissionUid = result.data.submissionUid || acceptedDoc.submissionUid || acceptedDoc.submissionId || '';

            // Extra logging for debugging
            console.log('============= UUID EXTRACTION =============');
            console.log('acceptedDoc.id:', acceptedDoc.id);
            console.log('acceptedDoc.uuid:', acceptedDoc.uuid);
            console.log('acceptedDoc.documentId:', acceptedDoc.documentId);
            console.log('result.data.submissionUid:', result.data.submissionUid);
            console.log('acceptedDoc.submissionUid:', acceptedDoc.submissionUid);
            console.log('Final extracted uuid:', uuid);
            console.log('Final extracted submissionUid:', submissionUid);
            console.log('===========================================');

            // Handle missing UUIDs
            if (!uuid) {
                console.warn('WARNING: No UUID found in LHDN response for invoice:', invoiceNumber);
                console.warn('This will cause cancellation to fail later.');
            }

            if (!submissionUid) {
                console.warn('WARNING: No submission UUID found in LHDN response for invoice:', invoiceNumber);
            }

            // Return the response with the extracted UUIDs
            return {
                success: true,
                message: 'Document submitted successfully',
                data: {
                    ...acceptedDoc,
                    documentUuid: uuid,
                    submissionUuid: submissionUid,
                    uuid: uuid
                },
                uuid: uuid,
                submissionUid: submissionUid,
                submissionTime: moment().format('YYYY-MM-DD HH:mm:ss')
            };
        }

        throw new Error('Unexpected submission result: No accepted documents found in response');
    }

    formatError(error) {
        return {
            success: false,
            code: error.code || 'SUBMISSION_ERROR',
            message: error.message || 'Failed to submit document',
            details: error.details || []
        };
    }
}

module.exports = SubmitToBQE;