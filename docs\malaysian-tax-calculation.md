# Malaysian Service Tax Calculation

## Overview

This document explains how Malaysian Service Tax is calculated in the eInvoice system, and how the recent fixes ensure the correct tax calculation approach is implemented.

## Malaysian Service Tax Approach

Malaysian Service Tax uses what is called a "tax-on-top" approach, which is different from tax-inclusive pricing models:

1. **Tax-on-top (Malaysian Service Tax)**:
   - The tax is calculated as a percentage of the base amount (tax-exclusive)
   - Formula: `invoiceAmount = taxableAmount + taxAmount` where `taxAmount = taxableAmount * (taxRate / 100)`
   - If you know the total amount: `taxableAmount = invoiceAmount / (1 + taxRate/100)`

2. **Tax-inclusive (like some GST/VAT models)**:
   - The tax is included in the final price
   - Formula: `invoiceAmount = taxableAmount` where `taxAmount = taxableAmount * (taxRate / (100 + taxRate))`

## Examples

### Example 1: Tax-on-top Calculation (8% Service Tax)

Starting with a taxable amount of RM 18,518.52:
- Tax rate: 8%
- Tax amount: 18,518.52 × (8/100) = RM 1,481.48
- Total invoice amount: 18,518.52 + 1,481.48 = RM 20,000.00

Or, if we know the total amount of RM 20,000.00:
- Tax rate: 8%
- Taxable amount: 20,000.00 / (1 + 8/100) = 20,000.00 / 1.08 = RM 18,518.52
- Tax amount: 20,000.00 - 18,518.52 = RM 1,481.48

### Example 2: Tax-exempt Calculation

For tax-exempt invoices:
- Taxable amount: RM 20,000.00
- Tax rate: 0%
- Tax amount: RM 0.00
- Total invoice amount: RM 20,000.00

## Implementation

### Key Changes in `dataProcessor.js`

1. **Correct Tax Calculation Approach**:
   ```javascript
   if (isExempted) {
       taxableAmount = totalAmount; // For exempt cases, the entire amount is taxable but tax rate is 0
       taxAmount = 0;
   } else {
       // Tax-on-top approach for Malaysian Service Tax
       taxableAmount = totalAmount / (1 + (taxRate/100));
       taxAmount = totalAmount - taxableAmount;
   }
   ```

2. **Consistent Line Item Processing**:
   ```javascript
   // Calculate the taxable amount (ex-tax) and tax amount
   let lineItemAmount;
   let taxAmount;
   
   if (isExempted) {
       lineItemAmount = totalAmount;
       taxAmount = 0;
   } else {
       lineItemAmount = totalAmount / (1 + (taxRate / 100));
       taxAmount = totalAmount - lineItemAmount;
   }
   ```

### Key Changes in `mapper.js`

1. **Using Pre-calculated Values**:
   ```javascript
   // Use the values calculated in dataProcessor.js
   const invoiceAmount = taxInfo.taxInclusiveAmount;  // Total amount (with tax)
   const taxableAmount = taxInfo.taxableAmount;       // Amount excluding tax
   const taxAmount = taxInfo.taxAmount;               // Tax amount only
   ```

2. **Consistent Line Item Mapping**:
   ```javascript
   // Get line item taxable amount and tax amount
   const lineItemAmount = parseFloat(item.amount || 0);  // Already tax-exclusive from dataProcessor
   const lineItemTaxAmount = parseFloat(item.tax?.types?.[0]?.amount || 0);
   ```

## Why This Matters

The correct tax calculation approach ensures:

1. **Accurate Tax Reporting**: Ensures compliance with Malaysian tax regulations.
2. **Consistency**: Total tax amounts match the sum of line item taxes.
3. **Proper Display**: Invoices show the right breakdown between taxable amount and tax amount.

## Conclusion

By implementing the correct tax-on-top approach for Malaysian Service Tax, the system now correctly calculates and displays tax amounts across the invoice, ensuring compliance with Malaysian tax regulations and providing accurate tax information to all parties. 