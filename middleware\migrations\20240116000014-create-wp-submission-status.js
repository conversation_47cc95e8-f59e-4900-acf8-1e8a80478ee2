'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_SUBMISSION_STATUS', {
      ID: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      DocNum: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      UUID: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true
      },
      SubmissionStatus: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'PENDING'
      },
      SubmissionDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      SubmissionID: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      ErrorMessage: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      RetryCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      LastRetryDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      UserID: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WP_USER_REGISTRATION',
          key: 'ID'
        }
      },
      CreatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      UpdatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });

    // Add indexes for common search fields
    await queryInterface.addIndex('WP_SUBMISSION_STATUS', ['DocNum']);
    await queryInterface.addIndex('WP_SUBMISSION_STATUS', ['SubmissionStatus']);
    await queryInterface.addIndex('WP_SUBMISSION_STATUS', ['SubmissionID']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_SUBMISSION_STATUS');
  }
}; 