'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_SETTINGS', {
      ID: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      CompanyImage: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      CompanyName: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      Industry: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      Country: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      TIN: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      BRN: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      About: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      Address: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      Phone: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      Email: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      ValidStatus: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      UserID: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WP_USER_REGISTRATION',
          key: 'ID'
        }
      },
      CreatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      UpdatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });

    // Add unique constraint for UserID
    await queryInterface.addConstraint('WP_SETTINGS', {
      fields: ['UserID'],
      type: 'unique',
      name: 'unique_user_settings'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_SETTINGS');
  }
}; 