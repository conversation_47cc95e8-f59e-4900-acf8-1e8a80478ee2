/* Modern Table Container */
.table-section {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    width: 100%;
    max-width: 100%;
    margin: 1.5rem auto 0;
    overflow: hidden;
}


/* Modern Controls Section */
.modern-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
}

.modern-length {
    display: flex;
    align-items: center;
}

.modern-length select {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    background-color: #fff;
    color: #475569;
    font-size: 0.875rem;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23475569' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
}

.modern-search {
    flex: 1;
}

.modern-search input {
    width: 100%;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    background-color: #fff;
    color: #475569;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.modern-search input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Table Styles */
.outbound-table-responsive {
    width: 100%;
    max-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1rem;
}

.outbound-table-responsive thead th {
    background: #f8fafc;
    padding: 0.6rem;
    font-weight: 800;
    color: #475569;
    font-size: 12px;
    text-transform: uppercase;
    border-bottom: 2px solid #e2e8f0;
    white-space: nowrap;
    position: sticky;
    z-index: 1;
    vertical-align: middle;
}

.outbound-table-responsive tbody td {
    padding: 0.6rem;
    color: #475569;
    font-size: 12px;
    border-bottom: 1px solid #e2e8f0;
    white-space: normal;
    word-wrap: break-word;
    vertical-align: middle;
}

.outbound-table-responsive tbody tr:hover {
    background-color: #f8fafc;
}

/* Modern Pagination */
.modern-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    margin-top: 1.5rem;
}

.modern-pagination .paginate_button {
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    background: #fff;
    color: #475569;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.modern-pagination .paginate_button:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

.modern-pagination .paginate_button.current {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #fff;
}

/* Status Badges */
.outbound-status {
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.outbound-status i {
    font-size: 0.875rem;
}

.outbound-status.valid {
    background-color: #dcfce7;
    color: #166534;
}

.outbound-status.invalid {
    background-color: #fee2e2;
    color: #991b1b;
}

.outbound-status.cancelled {
    background-color: #fef3c7;
    color: #92400e;
}

/* Action Buttons */
.outbound-action-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
}

.outbound-action-btn.submit {
    background-color: #405189;
    color: #fff;
}

.outbound-action-btn.submit:hover {
    background-color: #0a3d8a;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(10, 61, 138, 0.2);
}

.outbound-action-btn.submit:active {
    background-color: #0a3d8a;
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(10, 61, 138, 0.1);
}

.outbound-action-btn.submit:focus {
    background-color: #0a3d8a;
    outline: none;
    box-shadow: 0 0 0 3px rgba(64, 81, 137, 0.3);
}

.outbound-action-btn.submit.active {
    background-color: #0a3d8a;
    color: white;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
}

.outbound-action-btn.cancel {
    background-color: #dc3545;
    color: #fff;
}

.outbound-action-btn.cancel:hover {
    background-color: #c82333;
}

.cell-main {
    font-weight: 500;
    color: #405189;
    display: flex;
    align-items: center;
    gap: 6px;
    width: 80%;
    max-width: 180px;
    justify-content: flex-start;
    white-space: no-wrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-wrap: no-wrap;
    font-size: 0.875rem;
}

.cell-main i {
    flex-shrink: 0;
    font-size: 1rem;
    width: 16px;
}


.invoice-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: no-wrap;
    font-weight: 600;
}

.cell-sub {
    font-size: 0.75rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 6px;
    padding-left: 2px;
}

.cell-sub i {
    flex-shrink: 0;
    font-size: 0.875rem;
    width: 14px;
}

.doc-type-text,
.file-name-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: no-wrap;
    max-width: calc(100% - 10px);
}

/* Date Info Styles */
.date-info {
    display: flex;
    flex-direction: column;
    gap: 6px;

    white-space: no-wrap;
}

.date-row {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8125rem;
    color: #495057;
    cursor: pointer;
}

.date-row i {
    font-size: 0.875rem;
    width: 16px;
    text-align: center;
}

.date-label {
    color: #6c757d;
    font-size: 0.7rem;
}

.date-value {
    color: #495057;
    font-weight: 500;
}

.time-remaining {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8125rem;
    color: #dc3545;
    font-weight: 500;
    cursor: pointer;
}

.time-remaining i {
    font-size: 0.875rem;
}

.time-text {
    white-space: nowrap;
}

.time-not-applicable {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8125rem;
    color: #6c757d;
}

.time-not-applicable i {
    font-size: 0.875rem;
}

/* Controls Styles */
.outbound-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
    padding: 1rem;
    background-color: #fff;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.outbound-length-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.outbound-length-control select {
    padding: 0.375rem 2rem 0.375rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background-color: #fff;
    color: #4b5563;
    font-size: 0.875rem;
    min-width: 80px;
}

.outbound-search-control {
    display: flex;
    align-items: center;
}

.outbound-search-control input {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background-color: #fff;
    color: #4b5563;
    font-size: 0.875rem;
    width: 250px;
}

.outbound-checkbox {
    width: 1.125rem;
    height: 1.125rem;
    border-radius: 0.25rem;
    border: 2px solid #d1d5db;
    appearance: none;
    cursor: pointer;
    position: relative;
    margin: 0;
    padding: 0;
    background-color: #fff;
    vertical-align: middle;
}

.outbound-checkbox:checked {
    background-color: #405189;
    border-color: #405189;
}

.outbound-checkbox:checked::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.outbound-checkbox:hover:not(:checked) {
    border-color: #405189;
}

/* Badge Styles */
.badge-source {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
}

.badge-source.manual {
    background-color: #e0f2fe;
    color: #0369a1;
}

.badge-source.schedule {
    background-color: #f0fdf4;
    color: #166534;
}

/* Status Badge Styles */
.outbound-status {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    gap: 0.25rem;
}

.outbound-status.pending {
    background-color: #fff8dd;
    color: #997404;
}

.outbound-status.submitted {
    background-color: #dcfce7;
    color: #166534;
}

.outbound-status.cancelled {
    background-color: #fee2e2;
    color: #991b1b;
}

.outbound-status.rejected {
    background-color: #fef2f2;
    color: #b91c1c;
}

.outbound-status.invalid {
    background-color: #fef2f2;
    color: #b91c1c;
}

/* Export Button Styles */
.outbound-export-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    background-color: #405189;
    color: #fff;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.2s ease;
    margin-left: 1rem;
}

.outbound-export-btn:hover {
    background-color: #364574;
}

.outbound-export-btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* File Name Styles */
.outbound-file-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 100%;
}

.outbound-file-name i {
    color: #16a34a;
    flex-shrink: 0;
    font-size: 1rem;
    width: 16px;
}

.outbound-file-name-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 24px);
    font-size: 0.8125rem;
}

/* Bottom Controls Container */
.outbound-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

/* Pagination Styles */
.outbound-pagination {
    margin-left: auto;
}

.dataTables_wrapper .dataTables_paginate {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background: #fff !important;
    color: #4b5563 !important;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #f3f4f6 !important;
    border-color: #9ca3af;
    color: #1f2937 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #405189 !important;
    border-color: #405189;
    color: #fff !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Info Text */
.outbound-info {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Document Version Selection Modal */
.swal2-container.document-version-container {
    padding: 0;
    z-index: 9999;
}

.swal2-backdrop-show {
    background: rgba(0, 0, 0, 0.5) !important;
}

.document-version-backdrop {
    backdrop-filter: blur(4px);
}

.swal2-popup.document-version-popup {
    padding: 0;
    border-radius: 1rem;
    width: 450px;
    max-width: 100%;
    position: relative;
    z-index: 10000;
}

.swal2-content.document-version-content {
    padding: 0;
    margin: 0;
}

.document-version-modal {
    padding: 2rem;
    text-align: center;
}

.document-version-header {
    margin-bottom: 2rem;
}

.document-version-header h5 {
    color: #364574;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.document-version-header p {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

.document-version-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.document-version-option {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 0.75rem;
    padding: 1.25rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
    text-align: left;
}

.document-version-option:hover:not(.disabled) {
    border-color: #405189;
    box-shadow: 0 0 0 2px rgba(64, 81, 137, 0.1);
}

.document-version-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

.document-version-option .icon-wrapper {
    width: 48px;
    height: 48px;
    background: #e8f0fe;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.document-version-option .icon-wrapper i {
    font-size: 1.5rem;
    color: #405189;
}

.document-version-option .form-check {
    margin: 0;
    padding: 0;
    flex: 1;
}

.document-version-option .form-check-input {
    float: right;
    margin: 0;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 0.25rem;
    cursor: pointer;
}

.document-version-option .form-check-input:checked {
    background-color: #405189;
    border-color: #405189;
}

.document-version-option .form-check-label {
    cursor: pointer;
    display: block;
    padding-right: 2rem;
}

.document-version-option .version-name {
    font-weight: 600;
    color: #364574;
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.document-version-option .version-description {
    font-size: 0.875rem;
    color: #6c757d;
}

.document-version-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.document-version-actions button {
    padding: 0.625rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    min-width: 120px;
    transition: all 0.2s ease;
}

.document-version-actions .btn-proceed {
    background-color: #405189;
    color: #fff;
    border: none;
}

.document-version-actions .btn-proceed:hover {
    background-color: #364574;
}

.document-version-actions .btn-back {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #e9ecef;
}

.document-version-actions .btn-back:hover {
    background-color: #e9ecef;
}

/* Prevent scrollbar jumps */
body.swal2-shown {
    padding-right: 0 !important;
}

.modal-open {
    padding-right: 0 !important;
    overflow: hidden !important;
}

.modal-backdrop {
    width: 100vw !important;
    height: 100vh !important;
}

/* Prevent content shift */
.swal2-container {
    padding: 0 !important;
}

/* Add styles for company and buyer info */
.supplier-info-wrapper,
.buyer-info-wrapper {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.supplier-name,
.buyer-name {
    font-weight: 500;
    color: #405189;
    display: flex;
    align-items: center;
    gap: 2px;
    line-height: 1.2;
}

.supplier-details,
.buyer-details {
    font-size: 0.85rem;
    color: #6c757d;
    padding-left: 1px;
    line-height: 1.2;
}

.supplier-details small,
.buyer-details small {
    align-items: center;
    gap: 4px;
    line-height: 1.2;
}

.supplier-details i,
.buyer-details i {
    font-size: 0.75rem;
    color: #878a99;
    line-height: 1;
}

.reg-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 24px);
    line-height: 1.2;
}

/* Table cell padding adjustments */
.outbound-supplier-column,
.outbound-buyer-column {
    padding: 8px 16px !important;
}

/* Hover effects */
.supplier-info-wrapper:hover .supplier-name,
.buyer-info-wrapper:hover .buyer-name {
    color: #299cdb;
}

/* Cursor pointer for tooltips */
.cursor-pointer {
    cursor: pointer;
}

/* Tooltip enhancements */
.tooltip {
    pointer-events: none;
}

.tooltip-inner {
    max-width: 300px;
    padding: 8px 12px;
    background-color: #405189;
    font-size: 0.875rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before,
.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #405189;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .company-text,
    .buyer-text {
        max-width: 120px;
    }

    .reg-text {
        max-width: 100px;
    }
}

/* Empty State Styles */
.empty-state-container {
    text-align: center;
    padding: 2rem;
}

.empty-state-icon {
    color: #9ca3af;
    margin-bottom: 1rem;
}

.empty-state-title {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-state-description {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.empty-state-actions {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}


/* Invoice Column Styles */
.invoice-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.invoice-main {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.invoice-number {
    display: flex;
    align-items: center;
    gap: 6px;
}

.invoice-number i {
    font-size: 1rem;
    width: 16px;
    flex-shrink: 0;
}

.invoice-text {
    font-weight: 600;
    color: #405189;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.document-type {
    padding-left: 22px;
}

.badge-document-type {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    background-color: #e0f2fe;
    color: #0369a1;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-document-type i {
    font-size: 0.75rem;
}

/* File Info Styles */
.file-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    color: #6c757d;
    position: relative;
    padding-left: 20px;  /* Reduced padding */
    width: 220px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-info i {
    position: absolute;
    left: 0;
    color: #198754;
    font-size: 0.875rem;  /* Slightly reduced size */
    top: 50%;
    transform: translateY(-50%);
}

.file-name-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 20px);
    color: #6c757d;
    padding-left: 2px;  /* Small padding for better spacing */
}

/* Table Container Wrapper */
.table-container-wrapper {
    margin-top: 2rem;
    padding: 0 1rem;
}

/* Table Card */
.table-card {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* Table Header Section */
.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: #fff;
}

.table-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* .table-title {
    font-size: 0.5rem;
    font-weight: 600;
    color: #1e293b;
} */

/* Table Controls */
.table-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

/* Table Body Container */
.table-body-container {
    overflow-x: auto;
    min-height: 400px;
    position: relative;
}


/* This style is now handled by .outbound-action-btn.submit.active */

/* Filter Tags */
.active-filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px 0;
}

/* Table Footer */
.table-footer {
    padding: 1rem 1.5rem;
    background: #fff;
    border-top: 1px solid #e5e7eb;
}

/* Pagination Container */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

/* Loading Overlay */
.table-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Empty State */
.table-empty-state {
    padding: 3rem 1.5rem;
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    color: #94a3b8;
    margin-bottom: 1rem;
}

.empty-state-text {
    color: #64748b;
    font-size: 1rem;
}

/* Enhanced Cell Designs */
.invoice-cell,
.type-cell,
.supplier-cell,
.buyer-cell,
.date-cell,
.filepath-cell,
.amount-cell {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;
}

.supplier-meta,
.buyer-meta {
    margin-top: 0.25rem;
}

.supplier-meta small,
.buyer-meta small {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    opacity: 0.8;
}

.date-cell .date-primary {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #374151;
}

.date-cell .date-secondary {
    margin-top: 0.25rem;
}

.date-cell .date-secondary small {
    font-size: 0.75rem;
    line-height: 1.2;
}

/* File Path Cell Styles */
.filepath-cell .filepath-main {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #374151;
}

.filepath-cell .filepath-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.filepath-cell .filepath-meta {
    margin-top: 0.25rem;
}

.filepath-cell .filepath-meta small {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Amount Cell Styles */
.amount-cell .amount-main {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1rem;
}

.amount-cell .amount-value {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.5px;
}

.amount-cell .amount-meta {
    margin-top: 0.25rem;
}

.amount-cell .amount-meta small {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Enhanced Customer Name with Icons */
.customer-name {
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    color: #374151;
    padding: 0.25rem 0;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.customer-name:hover {
    color: #3b82f6;
    transform: translateX(2px);
}

/* Remove the emoji icon since we're using FontAwesome icons now */
.customer-name::before {
    display: none;
}

/* Enhanced tooltips */
.client-tooltip {
    text-align: left;
    max-width: 300px;
}

.client-tooltip div {
    margin-bottom: 0.25rem;
    padding: 0.125rem 0;
}

.client-tooltip div:last-child {
    margin-bottom: 0;
}

/* Icon colors for different contexts */
.text-primary {
    color: #3b82f6 !important;
}

.text-success {
    color: #10b981 !important;
}

.text-info {
    color: #06b6d4 !important;
}

.text-warning {
    color: #f59e0b !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .supplier-cell,
    .buyer-cell,
    .date-cell,
    .filepath-cell,
    .amount-cell {
        gap: 0.125rem;
    }

    .supplier-meta,
    .buyer-meta,
    .filepath-meta,
    .amount-meta {
        margin-top: 0.125rem;
    }

    .supplier-meta small,
    .buyer-meta small,
    .filepath-meta small,
    .amount-meta small {
        font-size: 0.7rem;
    }

    .filepath-cell .filepath-text {
        max-width: 150px;
    }

    .amount-cell .amount-main {
        font-size: 0.9rem;
    }
}

