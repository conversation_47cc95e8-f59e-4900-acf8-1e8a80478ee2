const isAuthenticated = (req, res, next) => {
    if (req.session && req.session.user) {
        if (req.session.cookie.expires && Date.now() > req.session.cookie.expires.getTime()) {
            req.session.destroy((err) => {
                if (err) {
                    console.error('Error destroying expired session:', err);
                }
                return res.redirect('/login?expired=true');
            });
        } else {
            return next();
        }
    }
    res.redirect('/login');
};

const isBQEAuthorized = async (req, res, next) => {
    try {
        const response = await fetch('/bqe/check-auth');
        const data = await response.json();
        
        if (!data.isAuthorized) {
            return res.status(401).json({
                success: false,
                message: 'BQE authorization required'
            });
        }
        next();
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error checking BQE authorization'
        });
    }
};

const isAdmin = (req, res, next) => {
    if (!req.session?.user?.admin) {
        return res.status(403).json({
            success: false,
            message: 'Admin access required'
        });
    }
    next();
};

module.exports = {
    isAuthenticated,
    isBQEAuthorized,
    isAdmin
};
