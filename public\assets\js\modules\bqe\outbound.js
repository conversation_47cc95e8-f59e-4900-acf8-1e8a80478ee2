class InvoiceTableManager {
    constructor() {
        this.authManager = new BQEAuthManager(); // Use BQEAuthManager instead of AuthManager
        this.tooltips = new Set(); // Track active tooltips
        this._currentInvoice = null; // Add this line to store current invoice
        this.isSubmitting = false; // Flag to prevent multiple submissions
        this.currentDataSource = 'bqe'; // Track current data source: 'bqe' or 'manual'

        // Bind methods to preserve 'this' context
        this.initializeTable = this.initializeTable.bind(this);
        this.initializeEventListeners = this.initializeEventListeners.bind(this);
        this.refreshInvoiceTable = this.refreshInvoiceTable.bind(this);
        this.submitToLHDN = this.submitToLHDN.bind(this);
        this.updateTotalInvoicesFetched = this.updateTotalInvoicesFetched.bind(this);
        this.updateCardCounts = this.updateCardCounts.bind(this);
        this.isTableInitialized = this.isTableInitialized.bind(this);
        this.saveInvoicesInBatches = this.saveInvoicesInBatches.bind(this);
        this.formatRelativeTime = this.formatRelativeTime.bind(this);
        this.handleDataSourceChange = this.handleDataSourceChange.bind(this);
        this.initializeExcelUpload = this.initializeExcelUpload.bind(this);

        // Don't initialize immediately - let the HTML script handle initialization
        // this.initializeTable();
        // this.initializeEventListeners();

        this.cardCounts = {
            total: 0,
            submitted: 0,
            pending: 0,
            cancelled: 0
        };

        this.loadingDotsInterval = null;
        this.currentDots = '';
        this.lastSubmissionTime = 0;
        this.submissionCooldown = 2000; // 2 seconds cooldown between submissions
        this.initializeCooldownModal();

        // Set initial UI state after a short delay to ensure DOM is ready
        setTimeout(() => {
            this.toggleUIElements();
        }, 100);
    }

    initializeCooldownModal() {
        // Create cooldown modal HTML
        const modalHtml = `
            <div class="modal fade" id="cooldownModal" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Please Wait</h5>
                        </div>
                        <div class="modal-body">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                <p>Waiting for submission cooldown...</p>
                                <div class="progress">
                                    <div id="cooldownProgress" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <p id="cooldownTimer" class="mt-2">Time remaining: <span>2</span> seconds</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;

        // Append modal to body if it doesn't exist
        if (!document.getElementById('cooldownModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }
    }

    async showCooldownModal(waitTime) {
        const modal = $('#cooldownModal');
        const progressBar = $('#cooldownProgress');
        const timerSpan = $('#cooldownTimer span');

        modal.modal('show');

        const startTime = Date.now();
        const updateInterval = 100; // Update every 100ms for smooth progress

        return new Promise(resolve => {
            const updateProgress = () => {
                const elapsed = Date.now() - startTime;
                const remaining = Math.max(0, waitTime - elapsed);
                const progress = (elapsed / waitTime) * 100;

                progressBar.css('width', `${Math.min(100, progress)}%`);
                timerSpan.text((remaining / 1000).toFixed(1));

                if (elapsed < waitTime) {
                    setTimeout(updateProgress, updateInterval);
                } else {
                    modal.modal('hide');
                    resolve();
                }
            };

            updateProgress();
        });
    }

    initializeTable() {
        try {
            // Make sure the table element exists first
            const tableElement = $('#reportsTable');
            if (!tableElement.length) {
                console.error('Table element not found in DOM');
                return false;
            }

            if ($.fn.DataTable.isDataTable('#reportsTable')) {
                $('#reportsTable').DataTable().destroy();
            }
            // Initialize DataTable
            this.table = $('#reportsTable').DataTable({
                processing: false,
                deferRender: false,
                serverSide: false,
                pageLength: 10,
                dom: '<"outbound-controls"<"outbound-length-control"l>>rt<"outbound-bottom"<"outbound-info"i><"outbound-pagination"p>>',
                columns: [
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'row-index',
                        render: function (data, type, row, meta) {
                            // Calculate the correct index based on the current page and page length
                            const pageInfo = meta.settings._iDisplayStart;
                            const index = pageInfo + meta.row + 1;
                            return `<span class="row-index">${index}</span>`;
                        }
                    },
                    {
                        data: null,
                        title: 'INVOICE NO.',
                        render: function(data, type, row) {
                            // For manual submissions, use invoiceNumber field
                            const invoiceNo = row.source === 'manual' ? row.invoiceNumber : row.invoice_number;
                            if (!invoiceNo) return '<span class="text-muted bold">-</span>';

                            return `
                                <div class="invoice-cell">
                                    <span class="badge-invoice" data-bs-toggle="tooltip" title="Invoice Number: ${invoiceNo}">
                                        ${invoiceNo}
                                    </span>
                                </div>
                            `;
                        }
                    },
                    {
                        data: null,
                        title: 'TYPE',
                        render: function(data, type, row) {
                            // For manual submissions, use documentType field
                            const typeValue = row.source === 'manual' ? (row.documentType || 'Invoice') : (row.type || 'Invoice');
                            const typeClass = typeValue.toLowerCase().replace(/\s+/g, '-');

                            // Get appropriate icon for document type
                            let icon = 'fas fa-file-alt';
                            let badgeClass = 'primary';

                            if (typeValue.toLowerCase().includes('credit')) {
                                icon = 'fas fa-file-invoice-dollar';
                                badgeClass = 'danger';
                            } else if (typeValue.toLowerCase().includes('debit')) {
                                icon = 'fas fa-file-plus';
                                badgeClass = 'warning';
                            } else if (typeValue.toLowerCase().includes('invoice')) {
                                icon = 'fas fa-file-invoice-dollar';
                                badgeClass = 'success';
                            }

                            return `
                                <div class="type-cell">
                                    <span class="badge-type ${typeClass} bg-${badgeClass}-subtle text-${badgeClass}" 
                                          style="font-size: 0.85rem;"
                                          data-bs-toggle="tooltip"
                                          title="Document Type: ${typeValue}">
                                        <i class="${icon}"></i>
                                        ${typeValue}
                                    </span>
                                </div>
                            `;
                        }
                    },
                    {
                        data: null,
                        title: 'SUPPLIER NAME',
                        render: function(data, type, row) {
                            // Check if this is manual submission
                            if (row.source === 'manual') {
                                // For manual submissions, show supplier name from supplierInfo
                                const supplierName = row.supplierInfo?.registrationName || 'Unknown Supplier';
                                return `
                                    <div class="supplier-cell">
                                        <span class="customer-name">
                                            <i class="fas fa-building me-2 text-primary"></i>
                                            ${supplierName}
                                        </span>
                                    </div>
                                `;
                            } else {
                                const company_name = 'HSS ENGINEERING SDN BHD';
                                return `
                                  <div class="supplier-cell">
                                        <span class="customer-name">
                                            <i class="fas fa-building me-2 text-primary"></i>
                                            ${company_name}
                                        </span>
                                    </div>
                                `;
                            }
                        }
                    },
                    {
                        data: null,
                        title: 'BUYER NAME',
                        render: function(data, type, row) {
                            // Check if this is manual submission
                            if (row.source === 'manual') {
                                // For manual submissions, show buyer name from buyerInfo
                                const buyerName = row.buyerInfo?.registrationName || 'Unknown Buyer';
                                return `
                                    <div class="buyer-cell">
                                        <span class="customer-name">
                                            <i class="fas fa-user-check me-2 text-info"></i>
                                            ${buyerName}
                                        </span>
                                    </div>
                                `;
                            } else {
                                const buyerName = row.customer_name || data || 'Unknown Buyer';
                                return `
                                  <div class="buyer-cell">
                                        <span class="customer-name">
                                            <i class="fas fa-user-check me-2 text-info"></i>
                                            ${buyerName}
                                        </span>
                                    </div>
                                `;
                            }
                        }
                    },
                    {
                        data: null,
                        title: 'FILE PATH',
                        render: function(data, type, row) {
                            // Only show for manual submissions
                            if (row.source === 'manual') {
                                const filePath = row.filePath || 'Unknown Path';
                                const fileName = row.fileName || 'Unknown File';
                                const fileSize = row.size ? `${(row.size / 1024).toFixed(1)} KB` : 'Unknown Size';

                                return `
                                    <div class="filepath-cell">
                                        <div class="filepath-main">
                                            <i class="fas fa-folder-open me-2 text-warning"></i>
                                            <span class="filepath-text" title="${filePath}">${filePath}</span>
                                        </div>
                                        <div class="filepath-meta">
                                            <small class="text-muted">
                                                <i class="fas fa-file me-1"></i>
                                                ${fileName} (${fileSize})
                                            </small>
                                        </div>
                                    </div>
                                `;
                            } else {
                                // For BQE submissions, show BQE Core with pending badge
                                return `
                                    <div class="">
                                        <span class="status-badge pending" style="font-size: 0.85rem;">
                                           <i class="bi bi-globe"></i>
                                            BQE
                                        </span>
                                    </div>
                                `;
                            }
                        }
                    },
                    {
                        data: null,
                        title: 'UPLOADED DATE/TIME',
                        render: (data, type, row) => {
                            if (type === 'display') {
                                // For manual submissions, use uploadedDate field
                                const dateValue = row.source === 'manual' ? row.uploadedDate : row.date_sync;
                                if (!dateValue) return '-';

                                if (row.source === 'manual') {
                                    // For manual submissions, show formatted date/time
                                    const formattedDate = moment(dateValue).format('MM-DD-YYYY');
                                    const formattedTime = moment(dateValue).format('HH:mm:ss');
                                    const fullDateTime = moment(dateValue).format('YYYY-MM-DD HH:mm:ss');
                                    return `
                                        <div>
                                            <span data-bs-toggle="tooltip" title="Uploaded: ${fullDateTime}">${formattedDate}</span>
                                            <div class="text-muted small">${formattedTime}</div>
                                        </div>
                                    `;
                                } else {
                                    // For BQE submissions, use relative time
                                    return this.formatRelativeTime(dateValue);
                                }
                            }
                            return data;
                        }
                    },
                    {
                        data: 'date_submitted',
                        render: function(data, type, row) {
                            if (type === 'display') {
                                if (row.status === 'Submitted' && data) {
                                    // Convert SQL timestamp to local date by removing Z suffix
                                    const localDate = moment(data.replace('Z', ''));
                                    const formattedDate = localDate.format('MM-DD-YYYY');
                                    const fullDateTime = localDate.format('YYYY-MM-DD HH:mm:ss');
                                    const textFormat = localDate.format('dddd, MMMM D, YYYY');
                                    const timeFormat = localDate.format('hh:mm:ss A'); // Changed to 12-hour format with AM/PM
                                    return `
                                        <div>
                                            <span data-bs-toggle="tooltip" title="Full Date: ${fullDateTime}">${formattedDate}</span>
                                            <div class="text-muted small">${textFormat}</div>
                                            <div class="text-muted small">${timeFormat}</div>
                                        </div>
                                    `;
                                }
                                return '-';
                            }
                            return data;
                        }
                    },
                    {
                        data: null, // Change from 'cancellation_period' to null since we calculate it
                        render: (data, type, row) => {
                            // Only render for display type to prevent multiple calls
                            if (type !== 'display') {
                                return '';
                            }

                            // For cancelled or pending status, show Not Applicable
                            if (row.status === 'Cancelled' || row.status === 'Pending') {
                                return `<span class="badge-cancellation not-applicable">
                                    <i class="bi bi-dash-circle"></i>
                                    Not Applicable
                                </span>`;
                            }

                            // For submitted status, show cancellation countdown
                            if (row.status === 'Submitted' && row.date_submitted) {
                                const time = InvoiceHelper.calculateCancellationTime(row.date_submitted, row.status);
                                row.cancellationTime = time.text;
                                row.cancellationStatus = time.expired ? 'expired' : time.isUrgent ? 'urgent' : 'success';

                                if (time.expired) {
                                    return `<span class="badge-cancellation expired">
                                        <i class="bi bi-x-circle"></i>
                                        Expired
                                    </span>`;
                                }

                                if (time.isUrgent) {
                                    return `<span class="badge-cancellation urgent">
                                        <i class="bi bi-clock-fill"></i>
                                        ${time.text}
                                    </span>`;
                                }

                                return `<span class="badge-cancellation success">
                                    <i class="bi bi-clock me-1"></i>
                                    ${time.text}
                                </span>`;
                            }

                            // For any other status, show Not Applicable
                            return `<span class="badge-cancellation not-applicable">
                                <i class="bi bi-dash-circle"></i>
                                Not Applicable
                            </span>`;
                        }
                    },
                    {
                        data: 'status',
                        render: function(data, type, row) {
                            if (type === 'display') {
                                const status = data || 'Pending';
                                const statusClass = status.toLowerCase();
                                const customClass = status.toLowerCase() === 'cancelled' ? 'cancelled' : statusClass;

                                // Add icon based on status
                                let icon = '';
                                switch(status.toLowerCase()) {
                                    case 'pending':
                                        icon = '<i class="bi bi-hourglass-split me-1"></i>';
                                        break;
                                    case 'submitted':
                                        icon = '<i class="bi bi-check-circle me-1"></i>';
                                        break;
                                         case 'failed':
                                        icon = '<i class="bi bi-slash-circle me-1"></i>';
                                        break;
                                         case 'processing':
                                        icon = '<i class="bi bi-check-circle me-1"></i>';
                                        break;
                                    case 'cancelled':
                                        icon = '<i class="bi bi-x-circle me-1"></i>';
                                        break;
                                }

                                return `<span class="status-badge ${customClass}">${icon}${status}</span>`;
                            }
                            return data;
                        }
                    },
                    {
                        data: null,
                        title: 'AMOUNT',
                        render: function(data, type, row) {
                            if (type === 'display') {
                                // For manual submissions, use totalAmount field
                                if (row.source === 'manual') {
                                    const amount = row.totalAmount;
                                    if (!amount || amount === 'N/A') {
                                        return '<span class="text-muted">N/A</span>';
                                    }

                                    const amountValue = parseFloat(amount);
                                    let amountClass = 'text-success';
                                    let amountIcon = 'fas fa-dollar-sign';
                                    if (amountValue < 0) {
                                        amountClass = 'text-danger';
                                        amountIcon = 'fas fa-minus-circle';
                                        amountType = 'Debit';
                                    } else if (amountValue === 0) {
                                        amountClass = 'text-muted';
                                        amountIcon = 'fas fa-equals';
                                        amountType = 'Zero';
                                    }

                                    return `
                                        <div class="amount-cell">
                                            <div class="amount-main">
                                                <i class="${amountIcon} me-2 ${amountClass}"></i>
                                                <span class="amount-value ${amountClass}" data-bs-toggle="tooltip" title="Total Amount: ${amount}">
                                                    ${amount}
                                                </span>
                                            </div>
                                        </div>
                                    `;
                                } else {
                                    // For BQE submissions, calculate from invoice data
                                    const invoiceAmount = row._rawInvoice?.invoiceAmount || row.amount || 0;
                                    const amountValue = parseFloat(invoiceAmount);

                                    const formattedAmount = amountValue.toLocaleString('en-MY', {
                                        style: 'currency',
                                        currency: 'MYR',
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    });

                                    let amountClass = 'text-success';
                                    let amountIcon = 'fas fa-dollar-sign';

                                    if (amountValue < 0) {
                                        amountClass = 'text-danger';
                                        amountIcon = 'fas fa-minus-circle';
                                        amountType = 'Debit';
                                    } else if (amountValue === 0) {
                                        amountClass = 'text-muted';
                                        amountIcon = 'fas fa-equals';
                                        amountType = 'Zero';
                                    }

                                    return `
                                        <div class="amount-cell">
                                            <div class="amount-main">
                                                <i class="${amountIcon} me-2 ${amountClass}"></i>
                                                <span class="amount-value ${amountClass}" data-bs-toggle="tooltip" title="Invoice Amount: ${formattedAmount}">
                                                    ${formattedAmount}
                                                </span>
                                            </div>
                                        </div>
                                    `;
                                }
                            }
                            return data;
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: (data, type, row) => {
                            // Check if this is manual submission mode
                            if (this.currentDataSource === 'manual') {
                                // For manual submissions, show Submit button
                                const fileName = row.fileName;
                                if (!fileName) {
                                    console.warn('No file name found for manual submission row:', row);
                                    return '<button class="outbound-action-btn submit" disabled><i class="bi bi-send"></i><span>Submit</span></button>';
                                }

                                // Check if already submitted
                                if (row.status === 'Submitted') {
                                    return '<button class="outbound-action-btn submit" disabled><i class="bi bi-check-circle"></i><span>Submitted</span></button>';
                                }

                                return `<button class="outbound-action-btn submit manual-submit" data-file-name="${fileName}" data-file-path="${row.filePath}" data-row-data='${JSON.stringify(row)}'>
                                    <i class="bi bi-send"></i>
                                    <span>Submit</span>
                                </button>`;
                            } else {
                                // For BQE submissions, show View Details button
                                const invoiceId = row.id;
                                if (!invoiceId) {
                                    console.warn('No invoice ID found for row:', row);
                                    return '<button class="outbound-action-btn submit" disabled><i class="bi bi-eye"></i><span>View Details</span></button>';
                                }
                                return `<button class="outbound-action-btn submit" data-invoice-id="${invoiceId}">
                                    <i class="bi bi-eye"></i>
                                    <span>View Details</span>
                                </button>`;
                            }
                        }
                    }
                ],
                language: {
                    emptyTable: 'No data available in table',
                    zeroRecords: 'No matching records found',
                    loadingRecords: 'Loading...',
                    processing: 'Processing...',
                    search: '',
                    searchPlaceholder: 'Search...',
                    lengthMenu: 'Show _MENU_ entries',
                    info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                    infoEmpty: 'Showing 0 to 0 of 0 entries',
                    infoFiltered: '(filtered from _MAX_ total entries)',
                    paginate: {
                        first: '<i class="bi bi-chevron-double-left"></i>',
                        previous: '<i class="bi bi-chevron-left"></i>',
                        next: '<i class="bi bi-chevron-right"></i>',
                        last: '<i class="bi bi-chevron-double-right"></i>'
                    }
                },
                initComplete: (settings, json) => {
                    //console.log('Table initialization complete');
                    // Show filters section only if we're in BQE mode
                    if (this.currentDataSource === 'bqe') {
                        document.querySelector('.filters-section')?.classList.add('d-none');
                    }
                    // Initialize tooltips after table is ready
                    this.initializeTooltips();
                    // Update card counts after table is initialized
                    this.updateCardCounts();
                },
                // Add search event handler to update card counts when searching
                drawCallback: (settings) => {
                    // Update card counts whenever the table is redrawn (after search, sort, etc.)
                    this.updateCardCounts();
                }
            });

            // Store the DataTable instance explicitly
            this.dataTableInstance = this.table;

            return true;
        } catch (error) {
            console.error('Error initializing table:', error);
            return false;
        }
    }


    initializeEventListeners() {
        const searchButton = document.getElementById('searchBqeBtn');
        const periodSelect = document.querySelector('.filters-section .form-select');
        const fromDateInput = document.getElementById('fromDate');
        const toDateInput = document.getElementById('toDate');

        // Initialize global search functionality
        const globalSearchInput = document.getElementById('globalSearch');
        if (globalSearchInput) {
            globalSearchInput.addEventListener('input', () => {
                if (this.table) {
                    this.table.search(globalSearchInput.value).draw();
                }
            });
        }

        // Initialize search button handler if it exists
        if (searchButton) {
            searchButton.addEventListener('click', async () => {
                try {
                    // Validate date range
                    const fromDate = fromDateInput?.value;
                    const toDate = toDateInput?.value;

                    if (!fromDate || !toDate) {
                        await Swal.fire({
                            icon: 'warning',
                            title: 'Invalid Date Range',
                            text: 'Please select both From and To dates',
                            confirmButtonText: 'OK'
                        });
                        return;
                    }

                    // Check if date range is valid
                    if (moment(fromDate).isAfter(moment(toDate))) {
                        await Swal.fire({
                            icon: 'warning',
                            title: 'Invalid Date Range',
                            text: 'From date cannot be after To date',
                            confirmButtonText: 'OK'
                        });
                        this.hideLoading();
                        return;
                    }

                    // Show loading state on button
                    const originalContent = searchButton.innerHTML;
                    searchButton.disabled = true;
                    searchButton.innerHTML = `
                        <span class="spinner-border spinner-border-sm me-2"></span>
                        Searching...
                    `;

                    // Refresh the table with current filters
                    await this.refreshInvoiceTable();

                } catch (error) {
                    console.error('Error during search:', error);
                    await Swal.fire({
                        icon: 'error',
                        title: 'Search Failed',
                        text: 'Failed to fetch invoices. Please try again.',
                        confirmButtonText: 'OK'
                    });
                } finally {
                    // Reset button state
                    if (searchButton) {
                        searchButton.disabled = false;
                        searchButton.innerHTML = `
                            <i class="bi bi-search me-1"></i>
                            Search BQE Invoice
                        `;
                    }
                }
            });
        }

        // Initialize period handler
        if (periodSelect) {
            //console.log('Period select found:', periodSelect);
            periodSelect.addEventListener('change', async (e) => {
                //console.log('Period changed to:', e.target.value);
                const period = e.target.value.toLowerCase();
                const dates = this.getDateRangeForPeriod(period);

                // Show/hide search button based on period
                if (searchButton) {
                    searchButton.style.display = period === 'custom' ? 'block' : 'none';
                }

                if (fromDateInput && toDateInput) {
                    fromDateInput.value = dates.fromDate;
                    toDateInput.value = dates.toDate;
                    fromDateInput.disabled = period !== 'custom';
                    toDateInput.disabled = period !== 'custom';

                    // Automatically trigger search for non-custom periods
                    if (period !== 'custom') {
                        await this.refreshInvoiceTable();
                    }
                }
            });

            // Set initial state
            const initialPeriod = periodSelect.value.toLowerCase();
            if (searchButton) {
                searchButton.style.display = initialPeriod === 'custom' ? 'block' : 'none';
            }
            if (fromDateInput && toDateInput) {
                fromDateInput.disabled = initialPeriod !== 'custom';
                toDateInput.disabled = initialPeriod !== 'custom';
            }
        }

        // Add view details and manual submit event listeners
        $('#reportsTable').on('click', '.outbound-action-btn', async (e) => {
            e.preventDefault();

            // Check if this is a manual submit button
            if (e.currentTarget.classList.contains('manual-submit')) {
                const fileName = e.currentTarget.dataset.fileName;
                const filePath = e.currentTarget.dataset.filePath;
                const rowData = JSON.parse(e.currentTarget.dataset.rowData);
                await this.submitManualFile(fileName, filePath, rowData, e.currentTarget);
            } else {
                // Handle BQE view details
                const invoiceId = e.currentTarget.dataset.invoiceId;
                await this.viewInvoiceDetails(invoiceId);
            }
        });

        // Add BQE Auth button initialization
        const bqeAuthBtn = document.getElementById('bqeAuthBtn');
        if (bqeAuthBtn) {
            bqeAuthBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                try {
                    const button = e.currentTarget;
                    const originalContent = button.innerHTML;
                    const bqeAuthBtnText = document.getElementById('bqeAuthBtnText');

                    if (bqeAuthBtnText && bqeAuthBtnText.textContent === 'Disconnect BQE') {
                        // Show confirmation dialog
                        const result = await Swal.fire({
                            icon: 'warning',
                            title: 'Disconnect BQE?',
                            text: 'Are you sure you want to disconnect from BQE?',
                            showCancelButton: true,
                            confirmButtonText: 'Yes, disconnect',
                            cancelButtonText: 'No, keep connected',
                            confirmButtonColor: '#dc3545',
                            cancelButtonColor: '#6c757d'
                        });

                        if (result.isConfirmed) {
                            button.disabled = true;
                            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Disconnecting...';

                            const response = await fetch('/bqe/disconnect', {
                                method: 'POST'
                            });
                            const data = await response.json();

                            if (data.success) {
                                // Update button state
                                button.classList.add('auth-btn');
                                button.classList.remove('disconnected');
                                if (bqeAuthBtnText) {
                                    bqeAuthBtnText.textContent = 'Authorize BQE';
                                }

                                const icon = button.querySelector('i');
                                if (icon) {
                                    icon.classList.remove('bi-shield-x');
                                    icon.classList.add('bi-shield-lock');
                                }

                                // Hide filters section
                                const filtersSection = document.querySelector('.filters-section');
                                if (filtersSection) {
                                    filtersSection.classList.add('d-none');
                                }

                                // Show success message
                                await Swal.fire({
                                    icon: 'success',
                                    title: 'Disconnected',
                                    text: 'Successfully disconnected from BQE'
                                });
                            } else {
                                throw new Error(data.error || 'Failed to disconnect');
                            }
                        }
                    } else {
                        // Show loading state
                        button.disabled = true;
                        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Connecting...';

                        // Get authorization URL
                        const response = await fetch('/bqe/auth');
                        const data = await response.json();

                        if (data.success && data.redirectUrl) {
                            // Show connecting dialog
                            await Swal.fire({
                                title: 'Connecting to BQE',
                                html: `
                                    <div class="text-start">
                                        <div class="alert alert-info mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-info-circle-fill me-2"></i>
                                                <div>
                                                    <h6 class="mb-1">Authorization Process</h6>
                                                    <small>Please wait while we establish a secure connection...</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center text-info">
                                            <i class="bi bi-shield-check me-2"></i>
                                            <small class="status-message">Preparing authentication request...</small>
                                        </div>
                                        <div class="mt-2">
                                            <p class="small text-muted mb-1">
                                                <i class="bi bi-clock-history me-1"></i>
                                                Redirecting in <b class="timer">3</b> seconds
                                            </p>
                                        </div>
                                    </div>
                                `,
                                timer: 3000,
                                timerProgressBar: true,
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                    const timer = Swal.getPopup().querySelector('b.timer');
                                    const statusMsg = Swal.getPopup().querySelector('.status-message');

                                    const timerInterval = setInterval(() => {
                                        const timeLeft = Math.ceil(Swal.getTimerLeft() / 1000);
                                        if (timer) timer.textContent = timeLeft;

                                        // Update status message
                                        if (timeLeft <= 1) {
                                            statusMsg.textContent = 'Ready to connect...';
                                        } else if (timeLeft <= 2) {
                                            statusMsg.textContent = 'Validating security tokens...';
                                        }
                                    }, 100);

                                    Swal.getPopup().addEventListener('close', () => {
                                        clearInterval(timerInterval);
                                    });
                                }
                            });

                            // Redirect to BQE auth page
                            window.location.href = data.redirectUrl;
                        } else {
                            throw new Error(data.error || 'Failed to get authorization URL');
                        }
                    }
                } catch (error) {
                    console.error('BQE Auth Error:', error);
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = originalContent;
                    }

                    await Swal.fire({
                        icon: 'error',
                        title: 'Authorization Failed',
                        text: error.message || 'Failed to process BQE authorization',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }

        // Add Submit to LHDN button event listener if it exists
        const submitToLhdnBtn = document.getElementById('submitToLhdnBtn');
        if (submitToLhdnBtn) {
            submitToLhdnBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                const modalElement = document.getElementById('viewDetailsModal');
                const invoiceId = modalElement?.dataset?.invoiceId;
                const button = e.currentTarget;

                if (!invoiceId) {
                    console.error('No invoice ID found');
                    return;
                }

                // Check if it's a cancel action
                if (button.classList.contains('btn-cancel')) {
                    await this.cancelInvoice(invoiceId);
                } else {
                    await this.submitToLHDN(invoiceId);
                }
            });
        }

        // Add event listener for modal close to reset state
        const detailsModal = document.getElementById('invoice-details-modal');
        if (detailsModal) {
            detailsModal.addEventListener('hidden.bs.modal', () => {
                this.resetCurrentInvoice();
            });

            // Also add for manual close button clicks
            const closeButtons = detailsModal.querySelectorAll('.btn-close, .close, button[data-dismiss="modal"]');
            closeButtons.forEach(button => {
                button.addEventListener('click', () => {
                    this.resetCurrentInvoice();
                });
            });
        }

        // Add debug button for developers
        const debugButton = document.getElementById('debug-uuid-button');
        if (debugButton) {
            debugButton.addEventListener('click', () => this.showManualUUIDDialog());
        }

        // Add event listeners for quick filter buttons
        const filterButtons = document.querySelectorAll('.quick-filters .outbound-action-btn');
        if (filterButtons.length > 0) {
            filterButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    e.currentTarget.classList.add('active');

                    // Get filter value
                    const filterValue = e.currentTarget.dataset.filter;

                    // Apply filter to DataTable
                    if (this.table) {
                        if (filterValue === 'all') {
                            // Clear the search/filter
                            this.table.search('').columns().search('').draw();
                        } else {
                            // Apply filter to the status column (assuming status is in column index 8)
                            this.table.column(8).search(filterValue, true, false).draw();
                        }
                    }
                });
            });
        }

        // Add data source toggle event listeners
        const dataSourceRadios = document.querySelectorAll('input[name="dataSource"]');
        console.log('Found data source radios:', dataSourceRadios.length);
        dataSourceRadios.forEach((radio, index) => {
            console.log(`Radio ${index}: id=${radio.id}, checked=${radio.checked}`);
            radio.addEventListener('change', async (event) => await this.handleDataSourceChange(event));
        });

        // Initialize Excel upload functionality
        this.initializeExcelUpload();

        // Check initial state and set UI accordingly
        const manualRadio = document.getElementById('manual');
        if (manualRadio && manualRadio.checked) {
            console.log('Manual tab is initially selected, switching to manual mode');
            this.currentDataSource = 'manual';
                 // Hide filters section
                 const filtersSection = document.querySelector('.filters-section');
                 if (filtersSection) {
                     filtersSection.classList.add('d-none');
                 }
            this.toggleUIElements();
            // Load manual submission data
            setTimeout(async () => {
                await this.refreshManualSubmissionTable();
            }, 500);
        }

        // Add refresh button event listener
        const refreshButton = document.getElementById('refreshDataSource');
        if (refreshButton) {
            refreshButton.addEventListener('click', async () => {
                console.log('Refresh button clicked, current data source:', this.currentDataSource);
                if (this.currentDataSource === 'bqe') {
                    await this.refreshInvoiceTable();
                } else {
                    await this.refreshManualSubmissionTable();
                }
            });
        }
    }


    getDateRangeForPeriod(period) {
        const now = moment();
        let fromDate, toDate;

        switch (period.toLowerCase()) {
            case 'today':
                fromDate = now.clone().startOf('day');
                toDate = now.clone().endOf('day');
                break;
            case 'yesterday':
                fromDate = now.clone().subtract(1, 'days').startOf('day');
                toDate = now.clone().subtract(1, 'days').endOf('day');
                break;
            case 'this week':
                fromDate = now.clone().startOf('week');
                toDate = now.clone().endOf('week');
                break;
            case 'last week':
                fromDate = now.clone().subtract(1, 'week').startOf('week');
                toDate = now.clone().subtract(1, 'week').endOf('week');
                break;
            case 'this month':
                fromDate = now.clone().startOf('month');
                toDate = now.clone().endOf('month');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'last month':
                fromDate = now.clone().subtract(1, 'month').startOf('month');
                toDate = now.clone().subtract(1, 'month').endOf('month');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'this year':
                fromDate = now.clone().startOf('year');
                toDate = now.clone().endOf('year');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'last year':
                fromDate = now.clone().subtract(1, 'year').startOf('year');
                toDate = now.clone().subtract(1, 'year').endOf('year');
                // Add warning for potential API limit
                this.checkDateRangeSize(fromDate, toDate);
                break;
            case 'custom':
                // Return empty dates for custom
                return {
                    fromDate: '',
                    toDate: ''
                };
            default:
                fromDate = null;
                toDate = null;
        }

        return {
            fromDate: fromDate ? fromDate.format('YYYY-MM-DD') : '',
            toDate: toDate ? toDate.format('YYYY-MM-DD') : ''
        };
    }

    // Add this new method to check date range size and show warning if needed
    checkDateRangeSize(fromDate, toDate) {
        const daysDifference = toDate.diff(fromDate, 'days');
        const estimatedRecords = daysDifference * 10; // Rough estimate of records per day

        if (estimatedRecords > 2500) {
            Swal.fire({
                icon: 'warning',
                title: 'Large Date Range Selected',
                html: `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        The selected date range is quite large and might exceed BQE API limits.
                        <hr>
                        <small>
                            • BQE API can only return up to 2,500 records (25 pages × 100 records)<br>
                            • Consider using a smaller date range for more accurate results<br>
                            • Selected range: ${daysDifference} days
                        </small>
                    </div>
                `,
                confirmButtonText: 'I Understand'
            });
        }
    }


    showLoadingIndicator() {
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-0 text-muted">Loading invoice details...</p>
                </div>
            `;
        }
    }

    async fetchInvoicesFromBQE() {
        //console.log('Starting fetchInvoicesFromBQE...');

        const authResponse = await fetch('/bqe/check-auth');
        const authData = await authResponse.json();

        if (!authData.isAuthorized || !authData.authResponse) {
            throw new Error('No valid auth response available. Please authorize BQE first.');
        }

        const accessToken = authData.authResponse.access_token;
        const baseUrl = authData.authResponse.endpoint;

        const fromDateInput = document.getElementById('fromDate');
        const toDateInput = document.getElementById('toDate');

        const fromDate = fromDateInput?.value || moment().startOf('year').format('YYYY-MM-DD');
        const toDate = toDateInput?.value || moment().endOf('year').format('YYYY-MM-DD');

        //console.log('Fetching with date range:', { fromDate, toDate });

        let allInvoices = [];
        let currentPage = 0;
        const pageSize = 100;
        const maxPages = 25;

        try {
            while (currentPage < maxPages) {
                    // Format dates according to BQE API format
                    const formattedFromDate = moment(fromDate).format('YYYY-MM-DD');
                    const formattedToDate = moment(toDate).format('YYYY-MM-DD');

                    const url = new URL(`${baseUrl}/invoice`);
                    const params = new URLSearchParams({
                        'where': `date >= '${formattedFromDate}' AND date <= '${formattedToDate}'`,
                        '$orderby': 'date desc',
                        '$top': pageSize.toString(),
                        '$skip': (currentPage * pageSize).toString(),
                        '$count': 'true',
                        'expand': 'customFields,workflow,lineItems,accountSplits,extendedAccountSplit,invoiceDetails'
                    });

                    url.search = params.toString();
                   // //console.log('Request URL:', url.toString());

                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${accessToken}`,
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('API Error:', errorText);
                        throw new Error(`Failed to fetch invoices: ${response.status}`);
                    }

                    const data = await response.json();
                    ////console.log('Page response:', data);

                    // Check if data.value exists and is an array
                    const invoices = Array.isArray(data.value) ? data.value :
                                   Array.isArray(data) ? data : [];

                    if (!invoices.length) {
                        break;
                    }

                    allInvoices = allInvoices.concat(invoices);
                    this.updateTotalInvoicesFetched(allInvoices.length);

                    // Check if we have more records
                    const totalCount = data['@odata.count'] || data.count || 0;
                    if (!totalCount || allInvoices.length >= totalCount) {
                        break;
                    }

                    currentPage++;
                }

                ////console.log('Total invoices fetched:', allInvoices.length);

                // Map the invoices before returning
                if (allInvoices.length > 0) {
                    const mappedInvoices = await this.mapBQEInvoices(allInvoices);
                   // //console.log('Mapped invoices:', mappedInvoices);
                    return mappedInvoices;
                }

                return [];

            } catch (error) {
                console.error('Error fetching invoices:', error);
                this.hideLoading();
                await this.showEnhancedErrorModal(error);
                return [];
            } finally {
                this.hideLoading();
            }
    }

    async fetchClientDetails(clientId) {
        if (!clientId) {
            //console.log('No client ID provided for fetchClientDetails');
            return null;
        }

        try {
            const authToken = await this.authManager.getAccessToken();
            if (!authToken) {
                throw new Error('No valid BQE authentication found');
            }

            const response = await fetch(`/bqe/client/${clientId}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch client details');
            }

            const clientData = await response.json();
            ////console.log('Raw client data:', clientData);

            // Format address properly
            const formattedAddress = clientData.address ? {
                street1: clientData.address.street1 || '',
                street2: clientData.address.street2 || '',
                city: clientData.address.city?.trim() || '',
                state: clientData.address.state?.trim() || '',
                zip: Utils.cleanPostcode(clientData.address.zip?.trim() || ''), // Clean postcode for LHDN compliance
                country: clientData.address.country || 'MYS',
                // Create a clean formatted address string
                formattedAddress: [
                    clientData.address.street1,
                    clientData.address.street2,
                    clientData.address.city?.trim(),
                    clientData.address.state?.trim(),
                    Utils.cleanPostcode(clientData.address.zip?.trim() || ''), // Clean postcode here too
                    clientData.address.country || 'MYS'
                ].filter(part => part && part.trim() !== '').join(', ')
            } : {
                street1: 'NA',
                street2: '',
                city: 'NA',
                state: 'NA',
                zip: 'NA',
                country: 'MYS',
                formattedAddress: 'NA'
            };

            // Get communications
            const communications = [
                ...(clientData.communications || []),
                ...(clientData.address?.communications || [])
            ].filter(comm => comm.value && comm.typeName);

            // Map client data
            const mappedClient = {
                ...clientData,
                company: clientData.company || clientData.formattedName || clientData.name,
                taxId: clientData.taxId || clientData.customFields?.find(f =>
                    f.label === "Buyer's Tax ID" ||
                    f.label === "Tax ID"
                )?.value,
                registrationNumber: clientData.customFields?.find(f =>
                    f.label === "Buyer's Registration No" ||
                    f.label === "Registration Number"
                )?.value,
                address: formattedAddress,
                communications: communications,
                // Add additional fields that might be needed
                msicCode: clientData.customFields?.find(f => f.label === "Buyer's MSIC Code")?.value,
                businessActivity: clientData.customFields?.find(f => f.label === "Buyer's Business Activity")?.value,
                countryCode: clientData.customFields?.find(f => f.label === "BUYER'S COUNTRY CODE")?.value,
                stateCode: clientData.customFields?.find(f => f.label === "BUYER'S ADDRESS STATE CODE")?.value
            };

            ////console.log('Mapped client data:', mappedClient);
            return mappedClient;

        } catch (error) {
            console.error('Error fetching client details:', error);
            return null;
        }
    }

    async fetchProjectDetails(projectId) {
        if (!projectId) {
            //console.log('No Project ID provided for fetchProjectDetails');
            return null;
        }

        try {
            const authToken = await this.authManager.getAccessToken();
            if (!authToken) {
                throw new Error('No valid BQE authentication found');
            }

            const response = await fetch(`/bqe/project/${projectId}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch client details');
            }

            const projectData = await response.json();
            //console.log('Project Data:', projectData);

            return projectData;

        } catch (error) {
            console.error('Error fetching client details:', error);
            return null;
        }
    }

    async fetchCurrencyDetails(currencyId) {
        if (!currencyId) {
            console.log('No currency ID provided for fetchCurrencyDetails');
            return null;
        }

        try {
            const authToken = await this.authManager.getAccessToken();
            if (!authToken) {
                throw new Error('No valid BQE authentication found');
            }

            const response = await fetch(`/bqe/currency/${currencyId}`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch currency details');
            }

            const currencyData = await response.json();
            console.log('Currency Data:', currencyData);

            return currencyData;

        } catch (error) {
            console.error('Error fetching currency details:', error);
            return null;
        }
    }

    async mapBQEInvoices(invoices) {
        if (!invoices || !Array.isArray(invoices)) {
            console.warn('Invalid invoices format:', invoices);
            return [];
        }

        // Get auth data first for client detail fetching
        const authResponse = await fetch('/bqe/check-auth');
        const authData = await authResponse.json();

        if (!authData.isAuthorized || !authData.authResponse) {
            throw new Error('No valid auth response available');
        }

        return await Promise.all(invoices.map(async (invoice, index) => {
            try {
                if (!invoice) {
                    console.warn('Received null or undefined invoice');
                    return null;
                }

                // Map type from number to string
                let type = 'Invoice';
                if (invoice.type === 14) {
                    type = 'Credit Note';
                }

                // Initialize arrays to store project details for each line item
                let projectDetailsArray = [];
                let currencyDetailsArray = [];
                let clientInfo = '';
                let clientDetails = null;
                let company = null;

                // First try invoice details
                if (invoice.invoiceDetails?.length > 0) {
                    const clientId = invoice.invoiceDetails[0].clientId;

                    // Fetch client details
                    if (clientId) {
                        clientDetails = await this.fetchClientDetails(clientId);
                        if (clientDetails) {
                            clientInfo = clientDetails.company || clientDetails.formattedName;
                        }
                    }

                    // Fetch project details for each invoice detail
                    projectDetailsArray = await Promise.all(
                        invoice.invoiceDetails.map(async detail => {
                            if (detail.projectId) {
                                const projectDetail = await this.fetchProjectDetails(detail.projectId);
                                return {
                                    projectId: detail.projectId,
                                    details: projectDetail,
                                    amount: detail.amount,
                                    memo: detail.memo1,
                                    incomeAccount: detail.incomeAccount,
                                    currencyMultiplierId: projectDetail.currencyMultiplierId,
                                    currencyMultiplier: projectDetail.currencyMultiplier,
                                    currencyCode: projectDetail.currencyCode,
                                    cultureCode: projectDetail.cultureCode
                                };
                            }
                            return null;
                        })
                    );

                     // Fetch currency details for each invoice detail
                     currencyDetailsArray = await Promise.all(
                        invoice.invoiceDetails.map(async detail => {
                            if (detail.currencyId) {
                                const currencyDetail = await this.fetchCurrencyDetails(detail.currencyId);
                                return {
                                    currencyId: detail.currencyId,
                                    details: currencyDetail,
                                    country: detail.country,
                                    name: detail.name,
                                    cultureCode: detail.cultureCode,
                                    currencyMultiplierId: currencyDetail.currencyMultiplierId,
                                    currencyCode: currencyDetail.currencyCode,
                                };
                            }
                            return null;
                        })
                    );
                }
                // Fallback to invoice level clientId
                else if (invoice.clientId) {
                    clientDetails = await this.fetchClientDetails(invoice.clientId);
                    if (clientDetails) {
                        clientInfo = clientDetails.company || clientDetails.formattedName;
                    }
                }

                // Format amount with validation
                const amount = parseFloat(invoice.invoiceAmount || 0);
                const formattedAmount = `MYR ${(amount || 0).toFixed(2)}`;

                // Fetch invoice-level currency details using currencyMultiplierId from first project if available
                let currency_details = null;
                let currencyMultiplierId = null;
                if (projectDetailsArray && projectDetailsArray.length > 0) {
                    currencyMultiplierId = projectDetailsArray[0].currencyMultiplierId;
                }
                if (currencyMultiplierId) {
                    currency_details = await this.fetchCurrencyDetails(currencyMultiplierId);
                }

                // Create the processed invoice object
                const processedInvoice = {
                    checkbox: '',
                    id: invoice.id || '',
                    invoice_number: invoice.invoiceNumber || '',
                    type: type,
                    customer_name: clientInfo || 'Unknown Client',
                    bqe_date: moment(invoice.date).format('YYYY-MM-DD'),
                    status: invoice.status === 1 ? 'Submitted' : 'Pending',
                    amount: formattedAmount,
                    date_submitted: null,
                    submission_timestamp: null,
                    _rawInvoice: invoice,
                    _clientDetails: clientDetails,
                    _companyDetails: company,
                    _currentInvoice: invoice,
                    _projectDetailsArray: projectDetailsArray,
                    _invoiceDetails: invoice.invoiceDetails || [],
                    version: invoice.version || '1.0',
                    currency: (invoice.currency || (currency_details && currency_details.currencyCode) || 'MYR'),
                    currency_details: currency_details,
                    due_date: invoice.dueDate ? moment(invoice.dueDate).format('YYYY-MM-DD') : null,
                    uuid: invoice.uuid || null,  // Include UUID if available
                    submissionUuid: invoice.submissionUuid || null,  // Include submission UUID if available
                };
           
                return processedInvoice;

            } catch (error) {
                console.error(`Error mapping invoice ${invoice?.invoiceNumber || 'Unknown'}:`, error);
                console.debug('Problematic invoice data:', JSON.stringify(invoice, null, 2));
                return null;
            }
        })).then(results => {
            const validResults = results.filter(Boolean);
            return validResults;
        });
    }


    isTableInitialized() {
        return this.dataTable && $.fn.DataTable.isDataTable('#reportsTable');
    }


        showLoading(initialState = 'checking_staging') {
            try {
                const modal = document.getElementById('loadingModal');
                if (!modal) {
                    console.error('Loading modal not found');
                    return;
                }

                // Initialize or get Bootstrap modal instance
                let bsModal = bootstrap.Modal.getInstance(modal);
                if (!bsModal) {
                    bsModal = new bootstrap.Modal(modal, {
                        backdrop: 'static',
                        keyboard: false
                    });
                }

                // Define steps and their configurations
                const steps = [
                    { id: 'checking_staging', label: 'Checking Database', icon: 'bi-database-check' },
                    { id: 'retrieving', label: 'Retrieving Invoices', icon: 'bi-cloud-download' },
                    { id: 'saving', label: 'Saving Records', icon: 'bi-save' },
                    { id: 'submitting', label: 'Submitting to LHDN', icon: 'bi-arrow-up-circle' },
                    { id: 'completed', label: 'Completed', icon: 'bi-check-circle' }
                ];

                // Reset all steps to waiting state
                const stepElements = modal.querySelectorAll('.step');
                stepElements.forEach((stepEl, index) => {
                    const step = steps[index];
                    stepEl.innerHTML = `
                        <div class="step-icon">
                            <i class="bi ${step.icon}"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-label">${step.label}</div>
                            <span class="step-status waiting">Waiting...</span>
                        </div>
                    `;
                    stepEl.setAttribute('data-status', 'waiting');
                });

                // Show modal
                bsModal.show();

                // Update to initial state after a brief delay
                setTimeout(() => {
                    this.updateLoadingState(initialState);
                }, 100);

            } catch (error) {
                console.error('Error showing loading overlay:', error);
            }
        }


    // Update the refreshInvoiceTable method
    async refreshInvoiceTable() {
        let mappedInvoices = [];
        try {
            this.startTime = Date.now(); // Add start time tracking
            this.showLoading();

            // Step 1: Check staging database
            this.updateLoadingState('checking_staging');
            const { stagingMap } = await this.checkStagingDatabase();
            await this.delay(300); // Add small delay for visual feedback

            // Step 2: Retrieve invoices
            this.updateLoadingState('retrieving', 'Fetching invoices from BQE... This may take a few moments depending on the number of invoices.');
            const invoices = await this.fetchInvoicesFromBQE();
            if (!invoices?.length) {
                this.updateLoadingState('retrieving', 'Fetching invoices from BQE... This may take a few moments depending on the number of invoices.');
                throw new Error('');
            }
            // Step 3: Save and update UI
            this.updateLoadingState('saving', 'Saving records to database...');
            mappedInvoices = this.mapInvoicesWithStagingData(invoices, { stagingMap });
            await this.saveAndUpdateUI(mappedInvoices);
            await this.delay(300);

            this.updateLoadingState('completed');
            this.hideLoading();


        } catch (error) {
            console.error('Error refreshing table:', error);

            // Show error in the current step
            const loadingMessage = document.querySelector('.loading-message');


            if (loadingMessage) {
                loadingMessage.innerHTML = `
                    <div class="text-danger">
                        ${error.message || ''}
                    </div>
                `;
            }

            // Show enhanced error modal
            await this.showEnhancedErrorModal(error);

            // Hide loading modal after error
            const modal = document.getElementById('loadingModal');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
        }
    }

    updateLoadingState(state, message = '') {
        try {
            const modal = document.getElementById('loadingModal');
            if (!modal) return;

            const steps = ['checking_staging', 'retrieving', 'saving', 'submitting', 'completed'];
            const currentStepIndex = steps.indexOf(state);

            if (currentStepIndex === -1) {
                console.warn('Invalid state:', state);
                return;
            }

            // Update progress bar
            const progressBar = modal.querySelector('.progress-bar');
            if (progressBar) {
                const progress = ((currentStepIndex + 1) / steps.length) * 100;
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
            }

            // Update loading message and time
            const loadingMessage = modal.querySelector('.loading-message');
            const loadingTimeLeft = modal.querySelector('.loading-time-left');

            if (loadingMessage && loadingTimeLeft) {
                if (state === 'completed') {
                    const completedMessages = [
                        'Great success! Invoice submitted successfully.',
                        'Successfully submitted to LHDN!',
                        'All done! Your invoice has been submitted.',
                        'Submission completed successfully!',
                        'Mission accomplished! Invoice submitted.'
                    ];
                    const randomMessage = completedMessages[Math.floor(Math.random() * completedMessages.length)];
                    loadingMessage.textContent = randomMessage;

                    const duration = this.calculateDuration();
                    loadingTimeLeft.textContent = duration ? `Completed in ${duration}` : '';
                    this.hideLoading();
                } else {
                    loadingMessage.textContent = message || 'Processing...';
                    const estimatedTimeLeft = this.calculateEstimatedTimeLeft(currentStepIndex + 1, steps.length);
                    loadingTimeLeft.textContent = estimatedTimeLeft;
                }
            }

            // Update steps
            modal.querySelectorAll('.step').forEach((stepEl, index) => {
                const stepIcon = stepEl.querySelector('.step-icon i');
                const stepStatus = stepEl.querySelector('.step-status');

                if (index < currentStepIndex) {
                    // Completed steps
                    stepEl.setAttribute('data-status', 'completed');
                    if (stepIcon) stepIcon.className = 'bi bi-check-circle-fill';
                    if (stepStatus) stepStatus.textContent = 'Completed';
                } else if (index === currentStepIndex) {
                    // Current step
                    stepEl.setAttribute('data-status', 'in-progress');
                    if (stepIcon) stepIcon.className = 'bi bi-arrow-repeat spin';
                    if (stepStatus) stepStatus.textContent = message || 'Processing...';
                    this.updateLoadingDots(stepStatus);
                } else {
                    // Upcoming steps
                    stepEl.setAttribute('data-status', 'waiting');
                    if (stepIcon) stepIcon.className = 'bi bi-circle';
                    if (stepStatus) stepStatus.textContent = 'Waiting...';
                }
            });

        } catch (error) {
            console.error('Error updating loading state:', error);
        }
    }

        // Add this back near the top of the class with other helper methods
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }


        // Hide loading overlay
        hideLoading() {
            try {
                // Clear loading dots interval if it exists
                if (this.loadingDotsInterval) {
                    clearInterval(this.loadingDotsInterval);
                    this.loadingDotsInterval = null;
                }

                const modal = document.getElementById('loadingModal');
                if (!modal) return;

                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    // Hide modal directly
                    bsModal.hide();

                    // Reset progress bar if it exists
                    const progressBar = modal.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = '0%';
                        progressBar.setAttribute('aria-valuenow', '0');
                    }
                }
            } catch (error) {
                console.error('Error hiding loading overlay:', error);
            }
        }
    // Add cleanup method
    cleanup() {
        try {
            // Safely dispose tooltips
            this.tooltips.forEach(tooltip => {
                try {
                    if (tooltip && typeof tooltip.dispose === 'function') {
                        tooltip.dispose();
                    }
                } catch (error) {
                    console.warn('Error disposing tooltip during cleanup:', error);
                }
            });
            this.tooltips.clear();

            // Destroy DataTable
            if (this.dataTable) {
                try {
                    this.dataTable.destroy();
                } catch (error) {
                    console.warn('Error destroying DataTable:', error);
                }
            }

            // Clean up BQE auth manager
            if (this.authManager) {
                try {
                    this.authManager.cleanup();
                } catch (error) {
                    console.warn('Error cleaning up BQE auth manager:', error);
                }
            }

            // Remove event listeners
            try {
                $('#reportsTable').off('click', '.btn-lhdn');
            } catch (error) {
                console.warn('Error removing event listeners:', error);
            }
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }

    // Add new method to update card counts
    updateCardCounts() {
        // Use this.table instead of this.dataTable to access the DataTable instance
        // Also check for this.dataTableInstance as a fallback
        const dataTable = this.table || this.dataTableInstance;

        if (!dataTable) {
            console.warn('DataTable instance not found in updateCardCounts');
            return;
        }

        // Reset counts
        this.cardCounts = {
            total: 0,
            submitted: 0,
            pending: 0,
            cancelled: 0
        };

        try {
            // Get all VISIBLE data from table (respecting search/filter)
            // Use rows().data() to get all data, or rows({ search: 'applied' }).data() to get filtered data
            const data = dataTable.rows({ search: 'applied' }).data();

            // Count totals
            data.each(row => {
                // Increment total count
                this.cardCounts.total++;

                // Check status and increment appropriate counter
                const status = (row.status || '').toLowerCase();
                switch(status) {
                    case 'submitted':
                        this.cardCounts.submitted++;
                        break;
                    case 'pending':
                        this.cardCounts.pending++;
                        break;
                    case 'cancelled':
                        this.cardCounts.cancelled++;
                        break;
                }
            });

            console.log('Updated card counts:', this.cardCounts);

            // Update UI immediately after counting
            this.updateCardUI();
        } catch (error) {
            console.error('Error updating card counts:', error);
        }
    }

    // Add method to update card UI
    updateCardUI() {
        // Update total invoices card
        const totalCard = document.querySelector('.invoices-card .count-info h6');
        if (totalCard) {
            totalCard.textContent = this.cardCounts.total;
        }

        // Update submitted card
        const submittedCard = document.querySelector('.submitted-card .count-info h6');
        if (submittedCard) {
            submittedCard.textContent = this.cardCounts.submitted;
        }

        // Update pending card
        const pendingCard = document.querySelector('.pending-card .count-info h6');
        if (pendingCard) {
            pendingCard.textContent = this.cardCounts.pending;
        }

        // Update cancelled card
        const cancelledCard = document.querySelector('.cancelled-card .count-info h6');
        if (cancelledCard) {
            cancelledCard.textContent = this.cardCounts.cancelled;
        }
    }

    // Add method to get current counts
    getCardCounts() {
        return { ...this.cardCounts };
    }


async viewInvoiceDetails(invoiceId) {
    try {
        if (!invoiceId) {
            throw new Error('Invalid invoice ID');
        }

        // Show modal first
        const modalElement = document.getElementById('viewDetailsModal');
        if (!modalElement) {
            throw new Error('Modal element not found');
        }

        // Show loading overlay and add skeleton classes
        const loadingOverlay = document.getElementById('modalLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('d-none');
            // Add skeleton loading to key elements
            document.querySelectorAll('.modal-info-value').forEach(el => {
                el.innerHTML = '<div class="skeleton-loading"></div>';
            });
            document.querySelectorAll('.modal-info-section').forEach(el => {
                el.innerHTML = '<div class="skeleton-loading"></div>';
            });
            // Add skeleton for line items table
            const lineItemsContainer = document.querySelector('.line-items-container');
            if (lineItemsContainer) {
                lineItemsContainer.innerHTML = Array(3).fill(`
                    <div class="skeleton-row d-flex mb-2">
                        <div class="skeleton-loading" style="width: 40%"></div>
                        <div class="skeleton-loading ml-2" style="width: 20%"></div>
                        <div class="skeleton-loading ml-2" style="width: 20%"></div>
                        <div class="skeleton-loading ml-2" style="width: 20%"></div>
                    </div>
                `).join('');
            }
        }

        // Store the invoice ID in the modal's data attribute
        modalElement.dataset.invoiceId = invoiceId;

        // Initialize and show modal
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Get the invoice data from the DataTable - Fixed this part
        let invoice = null;
        if (this.table && typeof this.table.rows === 'function') {
            const data = this.table.rows().data();
            invoice = Array.from(data).find(row => row.id === invoiceId);
        }

        if (!invoice) {
            console.warn('Invoice not found in table data, attempting to fetch directly');
            // Fetch invoice data directly if not found in table
            const invoiceResponse = await fetch(`/bqe/invoice/${invoiceId}`);
            if (!invoiceResponse.ok) {
                throw new Error('Failed to fetch invoice details');
            }
            invoice = await invoiceResponse.json();
        }

        if (!invoice) {
            throw new Error('Invoice not found');
        }

        // Fetch company (supplier) details
        try {
            const companyResponse = await fetch('/bqe/company');
            if (!companyResponse.ok) {
                throw new Error('Failed to fetch company details');
            }

            const companyData = await companyResponse.json();
            invoice.company = companyData;

            // Process company custom fields for supplier information
            invoice.supplier = {
                tin: this.getCustomFieldValue(companyData.customFields, "Supplier's TIN"),
                registrationNumber: this.getCustomFieldValue(companyData.customFields, "Supplier's Registration No"),
                sstId: this.getCustomFieldValue(companyData.customFields, "Supplier's SST No"),
                msicCode: this.getCustomFieldValue(companyData.customFields, "Supplier's MSIC Code"),
                businessActivity: this.getCustomFieldValue(companyData.customFields, "Supplier's Business Activity")
            };
        } catch (error) {
            console.warn('Error fetching company details:', error);
        }

        // Update modal content
        await this.updateModalContent(invoice);

    } catch (error) {
        console.error('Error viewing invoice details:', error);
        // Hide loading overlay
        const loadingOverlay = document.getElementById('modalLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('d-none');
        }

        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to load invoice details. Please try again.'
        });
    }
}

    // Add formatDate function
    formatDate(date) {
        if (!date) return '-';
        return moment(date).format('DD-MM-YYYY');
    }

async updateModalContent(invoice) {
    //console.log('Updating modal content with invoice:', invoice);

    // Fetch UUIDs from the database if missing
    if (!invoice.uuid || !invoice.submission_uuid) {
        //console.log('UUIDs missing, fetching from database...');

        try {
            // First try the direct lookup endpoint
            const response = await fetch('/bqe/get-invoice-uuids', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    invoice_number: invoice.invoice_number,
                    invoice_id: invoice.id
                })
            });

            if (response.ok) {
            const data = await response.json();
                //console.log('UUID lookup result:', data);

                if (data.success && data.invoice) {
                    // Update the invoice object with UUID values
                    invoice.uuid = data.invoice.uuid;
                    invoice.submission_uuid = data.invoice.submission_uuid;
                    //console.log('Updated invoice with UUIDs from database:', {
                        //uuid: invoice.uuid,
                        //submission_uuid: invoice.submission_uuid
                    //});
                }
            }
        } catch (error) {
            console.error('Error fetching UUIDs:', error);
        }
    }

    // Proceed with updating modal content
    try {
        // Hide loading spinner at the start
        const loadingSpinner = document.getElementById('loadingSpinner');
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }

        // Store the current invoice
        this._currentInvoice = invoice;
        console.log('Current Invoice: ', invoice);

        // Update currency indicator in the modal header
        const currencyIndicator = document.getElementById('currency-indicator');
        if (currencyIndicator) {
            // Check if this is a foreign currency invoice
            const isForeign = this.isForeignCurrencyInvoice(invoice);
            const currencyCode = isForeign ? invoice.currency_details?.data?.currencyCode : 'MYR';

            // Update the badge class based on currency type
            currencyIndicator.textContent = currencyCode || 'MYR';
            currencyIndicator.className = isForeign ?
                'ms-2 badge bg-warning text-dark cursor-help' :
                'ms-2 badge bg-secondary';

            // Add information cursor style
            currencyIndicator.style.cursor = 'help';

            // Add tooltip with appropriate information
            currencyIndicator.setAttribute('data-bs-toggle', 'tooltip');
            currencyIndicator.setAttribute('data-bs-placement', 'right');

            if (isForeign && invoice.currency_details?.data?.multiplier) {
                // Foreign currency tooltip with exchange rate
                currencyIndicator.setAttribute('title', `Exchange Rate: 1 ${currencyCode} = ${invoice.currency_details.data.multiplier} MYR`);
            } else {
                // Local currency tooltip
                currencyIndicator.setAttribute('title', 'Malaysian Ringgit (Local Currency)');
            }

            // Initialize tooltip
            new bootstrap.Tooltip(currencyIndicator);
        }

        // Get version from custom fields
        const version = invoice._rawInvoice?.custom_fields?.find(f =>
            f.label === 'Invoice Version' ||
            f.label === 'Version'
        )?.value || '1.0';

        // Update invoice information with null checks
        const updateElement = (id, label, value, defaultValue = '-') => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = `
                    <div class="info-label">${label}</div>
                    <div class="info-value">${value || defaultValue}</div>
                `;
            }
        };

        updateElement('modalInvoiceNumber', 'e-Invoice Number', invoice.invoice_number);
        updateElement('modalInvoiceDate', 'e-Invoice Date', this.formatDate(invoice._rawInvoice?.date));
        updateElement('modalInvoiceVersion', 'e-Invoice Version', version);
        updateElement('modalInvoiceType', 'e-Invoice Type', invoice.type);
        // Store invoice number in hidden element for easier reference
        const invoiceNumberElement = document.getElementById('invoice-number');
        if (invoiceNumberElement) {
            invoiceNumberElement.textContent = invoice.invoice_number || '';
        }

        // Update status with badge - using status from table data
        const statusElementBody = document.getElementById('modalInvoiceStatus');
        if (statusElementBody) {
            const status = invoice.status?.toLowerCase() || 'pending';
            statusElementBody.innerHTML = `
                <div class="info-label">e-Invoice Status</div>
                <div class="status-badge ${status}">
                    <i class="bi bi-${this.getStatusIcon(status)}"></i>
                    ${invoice.status || 'Pending'}
                </div>
            `;
        }



        // Update supplier information with improved layout and null checks
        const updateInfoSection = (id, label, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = `
                    <div class="info-label">${label}</div>
                    <div class="info-value">${value || '-'}</div>
                `;
            }
        };

        updateInfoSection('modalSupplierName', 'Company Name', invoice.company?.name);
        updateInfoSection('modalSupplierTin', 'Tax ID Number (TIN)', invoice.supplier?.tin);
        updateInfoSection('modalSupplierBrn', 'Business Registration No.', invoice.supplier?.registrationNumber);
        updateInfoSection('modalSupplierSst', 'SST Registration No.', invoice.supplier?.sstId);

        // Update buyer information with improved layout and null checks
        updateInfoSection('modalBuyerName', 'Company Name', invoice._clientDetails?.company);
        updateInfoSection('modalBuyerTin', 'Tax ID Number (TIN)', invoice._clientDetails?.taxId);
        updateInfoSection('modalBuyerBrn', 'Business Registration No.', invoice._clientDetails?.registrationNumber);
        updateInfoSection('modalBuyerAddress', 'Address', invoice._clientDetails?.address?.formattedAddress);
        updateInfoSection('modalZipCode', 'Postal Code', invoice._clientDetails?.address?.zip);

      // Render line items
      const lineItemsContainer = document.getElementById('lineItemsBody');
      if (lineItemsContainer) {
          const lineItemsHtml = this.renderLineItems();
          lineItemsContainer.innerHTML = lineItemsHtml;

          // Check if this is a foreign currency invoice
          const cd = this._currentInvoice.currency_details?.data;
          const useForeign = cd && cd.multiplier && cd.currencyCode && cd.currencyCode !== 'MYR';

          // Add foreign currency warning if applicable
          if (useForeign) {
              // Add foreign currency indicator at the top of the line items
              const foreignCurrencyWarning = document.createElement('div');
              foreignCurrencyWarning.className = 'alert alert-info mt-3 mb-3';
              foreignCurrencyWarning.innerHTML = `
                  <div class="d-flex align-items-center">
                      <div class="currency-badge me-2">${cd.currencyCode}</div>
                      <div>
                          <strong>Foreign Currency Invoice</strong>
                          <div class="small">Exchange Rate: 1 ${cd.currencyCode} = ${cd.multiplier} MYR</div>
                      </div>
                  </div>
              `;

              // Add LHDN submission warning
              const lhdnWarning = document.createElement('div');
              lhdnWarning.className = 'alert alert-warning mt-3';
              lhdnWarning.innerHTML = `
                  <div class="d-flex">
                      <i class="bi bi-exclamation-triangle-fill me-2 fs-4"></i>
                      <div>
                          <strong>Important Note for LHDN Submission</strong>
                          <p class="mb-0">This invoice uses foreign currency (${cd.currencyCode}). When submitting to LHDN, both the original currency and MYR values will be included. Tax calculations will be based on the MYR equivalent amounts as required by Malaysian tax regulations.</p>
                      </div>
                  </div>
              `;

              // Insert the warnings
              const lineItemsSection = lineItemsContainer.closest('.line-items-section');
              if (lineItemsSection) {
                  lineItemsSection.insertBefore(foreignCurrencyWarning, lineItemsSection.firstChild);
                  lineItemsContainer.parentNode.insertBefore(lhdnWarning, lineItemsContainer.nextSibling);
              }
          }
      }


        // Get the submit button and update its state if it exists
        const submitButton = document.getElementById('submitToLhdnBtn');
        if (submitButton) {
            const status = invoice.status?.toLowerCase() || 'pending';

            if (status === 'submitted') {
                // Show Cancel button for submitted invoices
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="bi bi-x-circle"></i> Cancel';
                submitButton.classList.remove('btn-lhdn');
                submitButton.classList.add('btn-lhdn', 'btn-cancel');
                submitButton.onclick = () => this.cancelInvoice(invoice);
            } else if (status === 'cancelled') {
                // Show disabled Cancelled button for cancelled invoices
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-x-circle"></i> Cancelled';
                submitButton.classList.remove('btn-lhdn', 'btn-cancel');
                submitButton.classList.add('btn-lhdn', 'btn-disabled');
            } else {
                // Not submitted - show Submit to LHDN button
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="bi bi-send"></i> Submit to LHDN';
                submitButton.classList.remove('btn-lhdn', 'btn-cancel', 'btn-disabled');
                submitButton.classList.add('btn-lhdn');
                submitButton.onclick = () => this.submitToLHDN(invoice.id);
            }
        }

        // Update payment information with proper labeling
        const projectCustomFields = invoice._projectDetailsArray?.[0]?.details?.customFields || [];
        const useRemitAcct2 = projectCustomFields.find(f => f.label === 'Use Remit Acct 2')?.value === 'YES';
        const paymentLabel = useRemitAcct2 ? 'Remit Payment To (ALT)' : 'Remit Payment To';
        updateElement('payment-info', paymentLabel, this.getPaymentInfo(invoice));

    } catch (error) {
        console.error('Error updating modal content:', error);
        // Make sure to hide spinner even if there's an error
        const loadingSpinner = document.getElementById('loadingSpinner');
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }
        throw error;
    }
}

    // Add helper function for status icons
    getStatusIcon(status) {
        const icons = {
            'pending': 'hourglass-split',
            'submitted': 'check-circle',
            'cancelled': 'x-circle'
        };
        return icons[status] || 'question-circle';
    }


    renderLineItems() {
        // Check if we have invoice data
        if (!this._currentInvoice || !this._currentInvoice._invoiceDetails?.length) {
            return `<tr><td colspan="8" class="text-center">No items found</td></tr>`;
        }

        let subtotal = 0;
        let taxTotal = 0;
        let total = 0;
        let exemptedTaxTotal = 0;

        // Get tax information first
        const taxInfo = this.getTaxInfo(this._currentInvoice);
        const taxRate = taxInfo.rate; // This will be 0 for exempt invoices
        const taxType = taxInfo.type;
        const isTaxExempt = this.isTaxExempt(this._currentInvoice);

        // Get the applicable tax rate that would have been applied if not exempt
        let applicableTaxRate = taxInfo.applicableTaxRate;
        if (isTaxExempt && applicableTaxRate === 0) {
            // If no applicable tax rate was found in getTaxInfo, try to get it from other sources
            const projectCustomFields = this._currentInvoice?._projectDetailsArray?.[0]?.details?.customFields || [];
            const taxRateField = projectCustomFields.find(f =>
                f.label === 'SERVICE TAX RATE' ||
                f.label === 'Tax Rate'
            );

            if (taxRateField?.value) {
                applicableTaxRate = parseFloat(taxRateField.value);
            } else {
                // Look for mainServiceTax
                const projectDetailsTaxRate = this._currentInvoice?._projectDetailsArray?.[0]?.details?.mainServiceTax;
                if (typeof projectDetailsTaxRate === 'number' && !isNaN(projectDetailsTaxRate)) {
                    applicableTaxRate = projectDetailsTaxRate;
                } else {
                    // Default to standard rate of 6%
                    applicableTaxRate = 6.00;
                }
            }
        }

        // Calculate what the tax would have been if not exempt
        const taxExemptionAmount = taxInfo.exemption;
        exemptedTaxTotal = taxExemptionAmount;

        // Add line items
        const cd = this._currentInvoice.currency_details?.data;
        const useForeign = cd && cd.multiplier && cd.currencyCode && cd.currencyCode !== 'MYR';
        const fx = useForeign
            ? (val) => `${cd.currencyCode} ${(val * cd.multiplier).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`
            : (val) => this.formatCurrency(val);

        let rows = this._currentInvoice._invoiceDetails.map((item, index) => {
            // Get the corresponding project details
            const projectDetails = this._currentInvoice._projectDetailsArray?.[index]?.details || {};

            // Get classification from project custom fields
            const classificationField = projectDetails?.customFields?.find(f =>
                f.label === 'Invoice Classification'
            );
            const classification = classificationField?.description || projectDetails?.code || '022';

            // Get description from memo1, clean any HTML tags
            const description = item.memo1?.replace(/<[^>]*>/g, '') || item.description || 'NA';

            // Parse numbers with validation
            const quantity = parseFloat(item.quantity || 1);
            const amount = parseFloat(item.serviceAmount || item.amount || 0);
            const rate = amount; // For single quantity, rate equals amount

            // Calculate tax based on the tax rate
            let taxAmount = 0;
            if (!isTaxExempt) {
                taxAmount = (amount * taxRate) / 100;
            }

            // Calculate what the tax would have been if not exempt (for display only)
            let exemptedTaxAmount = 0;
            if (isTaxExempt) {
                exemptedTaxAmount = (amount * applicableTaxRate) / 100;
            }

            const itemTotal = amount + taxAmount;

            // Add to totals
            subtotal += amount;
            taxTotal += taxAmount;
            total += itemTotal;

            return `
                <tr>
                    <td style="white-space: normal; word-wrap: break-word;">${classification}</td>
                    <td class="description-cell" style="white-space: normal; word-wrap: break-word;">${description}</td>
                    <td class="text-end">${quantity}</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${fx(rate)}</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${fx(amount)}</td>
                    <td class="text-end">${this.formatNumber(applicableTaxRate, true)}%</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${isTaxExempt ? fx(exemptedTaxAmount) : fx(taxAmount)}</td>
                    <td class="text-end" style="white-space: normal; word-wrap: break-word;">${fx(itemTotal)}</td>
                </tr>
            `;
        }).join('');

        // Get tax exemption details from project custom fields
        const projectCustomFields = this._currentInvoice._projectDetailsArray?.[0]?.details?.customFields || [];
        const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
        const taxExemptionField = projectCustomFields.find(f => f.label === 'Details of Tax Exemption');

        // Extract just the code from the tax type description
        const taxTypeCode = taxTypeField?.description?.split('|')?.[0]?.trim() || '02';

        // Add tax information section
        rows += `
            <tr>
                <td colspan="8" class="border-0 pt-3">
                    <div class="details-section border rounded p-3 bg-light">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2"><strong>Tax Information</strong></div>
                                <div class="mb-2">Tax Type: ${taxTypeCode}${isTaxExempt ? ' (Tax Exempt)' : ''}</div>
                                <div class="mb-2">Tax Rate: ${isTaxExempt ?
                                    `${this.formatNumber(applicableTaxRate, true)}% <span class="text-muted small">(Exempt - 0.00% applied)</span>` :
                                    this.formatNumber(taxRate, true) + '%'}</div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">Amount Tax Exemption: ${isTaxExempt ? this.formatCurrency(exemptedTaxTotal) : '0.00'}</div>
                                <div class="mb-2">Details of Tax Exemption: ${isTaxExempt ? (taxExemptionField?.description || 'Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018') : 'Not Applicable'}</div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Add summary rows
        if (useForeign) {
            rows += `
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold">Subtotal (Excl. Tax):</td>
                    <td colspan="4" class="text-end fw-bold">${fx(subtotal)}</td>
                </tr>
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold">Total Tax Amount:</td>
                    <td colspan="4" class="text-end fw-bold">${isTaxExempt ? fx(0) : fx(taxTotal)}</td>
                </tr>
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold">Total Amount (Incl. Tax):</td>
                    <td colspan="4" class="text-end fw-bold">${fx(total)}</td>
                </tr>
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold text-muted small">Subtotal (Excl. Tax) in MYR:</td>
                    <td colspan="4" class="text-end fw-bold text-muted small">${this.formatCurrency(subtotal)}</td>
                </tr>
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold text-muted small">Total Tax Amount in MYR:</td>
                    <td colspan="4" class="text-end fw-bold text-muted small">${isTaxExempt ? '0.00' : this.formatCurrency(taxTotal)}</td>
                </tr>
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold text-muted small">Total Amount (Incl. Tax) in MYR:</td>
                    <td colspan="4" class="text-end fw-bold text-muted small">${this.formatCurrency(total)}</td>
                </tr>
            `;
        } else {
            rows += `
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold">Subtotal (Excluding Tax):</td>
                    <td colspan="4" class="text-end fw-bold">${this.formatCurrency(subtotal)}</td>
                </tr>
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold">Total Tax Amount:</td>
                    <td colspan="4" class="text-end fw-bold">${isTaxExempt ? '0.00' : this.formatCurrency(taxTotal)}</td>
                </tr>
                <tr class="summary-row">
                    <td colspan="4" class="text-end fw-bold">Total Amount (Including Tax):</td>
                    <td colspan="4" class="text-end fw-bold">${this.formatCurrency(total)}</td>
                </tr>
            `;
        }

        const paymentInfo = this.getPaymentInfo(this._currentInvoice);
        ////console.log('Payment info:', paymentInfo);
        if (paymentInfo && paymentInfo !== '-') {
            rows += `
                <tr>
                    <td colspan="8" class="border-0 pt-4">
                        <div class="fw-bold mb-2">Please remit the payment to:</div>
                        <div>${Array.isArray(paymentInfo) ? paymentInfo.join('<br>') : paymentInfo}</div>
                    </td>
                </tr>
            `;
        }

        return rows;
    }

    // Helper method to check if an invoice is tax exempt
    isTaxExempt(invoice) {
        // Check if tax type starts with 'E' (Exempt)
        const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
        const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
        return taxTypeField?.description?.startsWith('E') || false;
    }

    formatCurrency(amount) {
        if (amount === null || amount === undefined || isNaN(amount)) {
            return '0.00';
        }
        return parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    // Helper function to format numbers with thousand separators
    formatNumber(number, isRate = false) {
        if (number === null || number === undefined || isNaN(number)) {
            return '0';
        }

        // For tax rates, always show 2 decimal places
        if (isRate) {
            return number.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // For other numbers, follow existing logic
        if (Number.isInteger(number)) {
            return number.toLocaleString('en-US');
        }
        return number.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    // helper method to get payment info from company custom fields
    getPaymentInfo(invoice) {
        if (!invoice || !invoice.company || !invoice.company.customFields) {
            return '-';
        }

        // Get project custom fields from the first project in the array
        const projectCustomFields = invoice._projectDetailsArray?.[0]?.details?.customFields || [];

        // Check Use Remit Acct 2 field from project custom fields - exact case match
        const useRemitAcct2Field = projectCustomFields.find(f => f.label === 'Use Remit Acct 2');
        const useRemitAcct2 = useRemitAcct2Field?.value === 'YES' || useRemitAcct2Field?.description === 'YES';

        const companyCustomFields = invoice.company.customFields;

        // Log for debugging
        ////console.log('Project custom fields:', projectCustomFields);
        ////console.log('USE PAYMENT ACCT 2:', useRemitAcct2);
        ////console.log('Company custom fields:', companyCustomFields);

        // Get payment info based on the flag
        // If useRemitAcct2 is true, use the alternate account
        // If useRemitAcct2 is false or not set, use the default account
        const paymentField = companyCustomFields.find(f => {
            // Log each field for debugging
            //console.log('Checking field:', f.label);
            return f.label === (useRemitAcct2 ? 'Remit Payment To (ALT)' : 'Remit Payment To');
        });

        const paymentInfo = paymentField?.value;

        // Log the selected payment info
        //console.log('Selected payment field:', paymentField);
        //console.log('Selected payment info:', paymentInfo);
        //console.log('Use alternate account:', useRemitAcct2);

        // Split the payment information into lines and return as array
        return paymentInfo ? paymentInfo.split('*').map(line => line.trim()) : '-';
    }

    getTaxRate(invoice) {
        try {
            // Check if tax type is 'E' (Exempt) - if exempt, return 0 immediately
            const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
            const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');
            if (taxTypeField?.description?.includes('E')) {
                return 0;
            }

            // PRIORITY 1: Check project level tax rate first (from project details or project custom fields)

            // 1.1: Check project details array (most reliable source for project tax rate)
            const projectDetailsTaxRate = invoice?._projectDetailsArray?.[0]?.details?.mainServiceTax;
            if (typeof projectDetailsTaxRate === 'number' && !isNaN(projectDetailsTaxRate) && projectDetailsTaxRate !== 0) {
                //console.log('Using project details tax rate:', projectDetailsTaxRate);
                return projectDetailsTaxRate;
            }

            // 1.2: Check direct project tax rate
            const projectTaxRate = invoice?.project?.mainServiceTax;
            if (typeof projectTaxRate === 'number' && !isNaN(projectTaxRate) && projectTaxRate !== 0) {
                //console.log('Using project tax rate:', projectTaxRate);
                return projectTaxRate;
            }

            // 1.3: Check project custom fields
            const projectTaxRateField = projectCustomFields.find(f =>
                f.label === 'Tax Rate' ||
                f.label === 'SERVICE TAX RATE'
            );
            if (projectTaxRateField?.value && parseFloat(projectTaxRateField.value) !== 0) {
                const rate = parseFloat(projectTaxRateField.value);
                //console.log('Using project custom field tax rate:', rate);
                return rate;
            }

            // PRIORITY 2: Check invoice custom fields (raw invoice)
            const invoiceCustomFields = invoice?._rawInvoice?.customFields || [];
            const taxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');
            if (taxRateField?.value) {
                const rate = parseFloat(taxRateField.value);
                //console.log('Using invoice custom field tax rate from _rawInvoice:', rate);
                return rate;
            }

            // PRIORITY 3: Fall back to other invoice level checks

            // 3.1: Check invoice custom fields with alternative labels
            const altTaxRateField = invoice?._rawInvoice?.customFields?.find(f =>
                f.label === 'Tax rate' ||
                f.label === 'Tax Rate'
            );
            if (altTaxRateField?.value) {
                const rate = parseFloat(altTaxRateField.value);
                //console.log('Using invoice custom field tax rate (alt labels):', rate);
                return rate;
            }

            // 3.2: Check raw invoice tax rate as last resort
            if (typeof invoice?._rawInvoice?.mainServiceTax === 'number' &&
                !isNaN(invoice._rawInvoice.mainServiceTax)) {
                //console.log('Using raw invoice tax rate:', invoice._rawInvoice.mainServiceTax);
                return invoice._rawInvoice.mainServiceTax;
            }

            // Default to 0 if no tax rate is found
            //console.log('No tax rate found, defaulting to 0');
            return 0;
        } catch (error) {
            console.error('Error getting tax rate:', error);
            return 0;
        }
    }

        calculateTaxExemption(invoice) {
            try {
                // Get the tax type from project custom fields
                const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
                const taxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');

                // If tax type is 'E' (Exempt), calculate what the tax would have been
                if (taxTypeField?.description?.includes('E')) {
                    // Get the tax rate that would apply if not exempt
                    let applicableTaxRate = 0;

                    // First check invoice custom fields for tax rate
                    const invoiceCustomFields = invoice?._rawInvoice?.customFields || [];
                    const invoiceTaxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');

                    if (invoiceTaxRateField?.value) {
                        applicableTaxRate = parseFloat(invoiceTaxRateField.value);
                        //console.log('Using invoice custom field tax rate for exemption calculation:', applicableTaxRate);
                    } else {
                        // Try to get the actual tax rate from project details (ignoring exempt status)
                        const projectDetailsTaxRate = invoice?._projectDetailsArray?.[0]?.details?.mainServiceTax;
                        if (typeof projectDetailsTaxRate === 'number' && !isNaN(projectDetailsTaxRate)) {
                            applicableTaxRate = projectDetailsTaxRate;
                        } else {
                            // If not found, try standard rate or service tax rate from fields
                            const taxRateField = projectCustomFields.find(f =>
                                f.label === 'SERVICE TAX RATE' ||
                                f.label === 'Tax Rate'
                            );

                            if (taxRateField?.value) {
                                applicableTaxRate = parseFloat(taxRateField.value);
                            } else {
                                // Use standard rate of 8% if no specific rate is found
                                applicableTaxRate = 8.00;
                            }
                        }
                    }

                    // Calculate total amount from invoice amount or invoice details
                    let totalAmount = 0;

                    // First try to get amount from raw invoice
                    if (invoice?._rawInvoice?.invoiceAmount) {
                        totalAmount = parseFloat(invoice._rawInvoice.invoiceAmount);
                    }
                    // If not found, calculate from invoice details
                    else if (invoice?._invoiceDetails?.length > 0) {
                        totalAmount = invoice._invoiceDetails.reduce((sum, item) => {
                            const serviceAmount = parseFloat(item.serviceAmount || 0);
                            const expenseAmount = parseFloat(item.expenseAmount || 0);
                            return sum + serviceAmount + expenseAmount;
                        }, 0);
                    }

                    // Calculate the exempted tax amount
                    const exemptedTaxAmount = (totalAmount * (applicableTaxRate / 100));
                    //console.log('Tax exemption calculation:', totalAmount, '*', applicableTaxRate, '% =', exemptedTaxAmount);

                    return exemptedTaxAmount;
                }

                return 0;
            } catch (error) {
                console.error('Error calculating tax exemption:', error);
                return 0;
            }
        }
// Helper function to get custom field value
getCustomFieldValue(customFields, label) {
    if (!customFields || !Array.isArray(customFields)) return '';
    const field = customFields.find(f => f.label === label);
    return field ? field.value : '';
}

getTaxInfo(invoice) {
    // First try to get tax info from company custom fields
    const companyCustomFields = invoice?.company?.customFields || [];
    const taxTypeField = companyCustomFields.find(f => f.label === 'TAX TYPE (CODE)');

    // Get the tax rate - this will be 0 for exempt invoices
    const taxRate = this.getTaxRate(invoice);

    // Get the applicable tax rate that would have been applied if not exempt
    let applicableTaxRate = 0;
    const invoiceCustomFields = invoice?._rawInvoice?.customFields || [];
    const invoiceTaxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');

    if (invoiceTaxRateField?.value) {
        applicableTaxRate = parseFloat(invoiceTaxRateField.value);
        //console.log('Using invoice custom field tax rate for tax info:', applicableTaxRate);
    }

    // Calculate tax exemption amount
    const exemption = this.calculateTaxExemption(invoice);

    if (taxTypeField?.value) {
        return {
            type: taxTypeField.value,
            rate: taxRate,
            exemption: exemption,
            applicableTaxRate: applicableTaxRate
        };
    }

    // Fallback to project custom fields if company fields not found
    const projectCustomFields = invoice?._projectDetailsArray?.[0]?.details?.customFields || [];
    const projectTaxTypeField = projectCustomFields.find(f => f.label === 'TAX TYPE (CODE)');

    if (projectTaxTypeField?.description) {
        // Extract just the code from the tax type description
        const taxTypeCode = projectTaxTypeField.description.split('|')[0].trim();
        return {
            type: taxTypeCode,
            rate: taxRate,
            exemption: exemption,
            applicableTaxRate: applicableTaxRate
        };
    }

    // Default values if no tax info found
    return {
        type: 'SR',
        rate: taxRate,
        exemption: exemption,
        applicableTaxRate: applicableTaxRate
    };
}

    // Add helper method to initialize tooltips
    initializeTooltips() {
        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(element => {
            new bootstrap.Tooltip(element);
        });
    }

    // Helper method to check if an invoice uses foreign currency
    isForeignCurrencyInvoice(invoice) {
        // Check currency_details first (preferred method)
        if (invoice.currency_details?.data) {
            const cd = invoice.currency_details.data;
            return cd.currencyCode && cd.currencyCode !== 'MYR' && cd.multiplier;
        }

        // If no currency_details with valid multiplier, it's not a foreign currency invoice
        return false;
    }

    // Add this helper method for showing confirmation dialog
    async showSubmissionConfirmDialog(invoice) {
        try {
            // Safely extract invoice details with fallbacks
            const invoiceNumber = invoice.invoiceNumber ||
                                invoice._rawInvoice?.invoiceNumber ||
                                'Unknown';
            const invoiceDate = invoice.date ||
                              invoice._rawInvoice?.date ||
                              new Date().toISOString();
            const invoiceAmount = invoice.amount ||
                                invoice._rawInvoice?.amount ||
                                0;

            return Swal.fire({
                title: 'Submit Invoice to LHDN?',
                html: `
                    <div class="confirmation-dialog">
                        <div class="d-flex align-items-center mb-3">
                            <div class="invoice-icon">
                                <i class="bi bi-file-earmark-text text-primary"></i>
                            </div>
                            <div class="ms-3">
                                <div class="invoice-number">${invoiceNumber}</div>
                                <div class="invoice-details">
                                    ${moment(invoiceDate).format('DD-MM-YYYY')}
                                </div>
                            </div>
                            <span class="ms-auto badge bg-warning">Pending</span>
                        </div>

                        <div class="verification-list">
                            <div class="form-check" style="justify-content: flex-start">
                                <input type="checkbox" class="form-check-input" id="check1" required>
                                <label class="form-check-label" for="check1">
                                    I confirm all invoice details are accurate
                                </label>
                            </div>
                            <div class="form-check" style="justify-content: flex-start">
                                <input type="checkbox" class="form-check-input" id="check2" required>
                                <label class="form-check-label" for="check2">
                                    I verify the tax information is correct
                                </label>
                            </div>
                            <div class="form-check" style="justify-content: flex-start">
                                <input type="checkbox" class="form-check-input" id="check3" required>
                                <label class="form-check-label" for="check3">
                                    I understand this action cannot be undone
                                </label>
                            </div>
                            ${(invoice.currency_details?.data?.currencyCode &&
                               invoice.currency_details?.data?.currencyCode !== 'MYR' &&
                               invoice.currency_details?.data?.multiplier) ? `
                            <div class="form-check" style="justify-content: flex-start">
                                <input type="checkbox" class="form-check-input" id="check4" required>
                                <label class="form-check-label" for="check4">
                                    I confirm the foreign currency (${invoice.currency_details?.data?.currencyCode}) exchange rate is correct
                                </label>
                            </div>
                            ` : ''}
                        </div>

                        <div class="alert alert-warning mt-3 mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            You'll be asked to select document version in the next step
                        </div>

                        ${(invoice.currency_details?.data?.currencyCode &&
                           invoice.currency_details?.data?.currencyCode !== 'MYR' &&
                           invoice.currency_details?.data?.multiplier) ? `
                        <div class="alert alert-info mt-3 mb-0">
                            <i class="bi bi-currency-exchange me-2"></i>
                            <strong>Foreign Currency Invoice:</strong> This invoice will be submitted with both <strong>${invoice.currency_details?.data?.currencyCode}</strong> and MYR values as required by LHDN.
                        </div>
                        ` : ''}
                    </div>
                `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '<i class="bi bi-arrow-right me-2"></i>Proceed',
                cancelButtonText: '<i class="bi bi-x me-2"></i>Cancel',
                confirmButtonColor: '#0d6efd',
                cancelButtonColor: '#6c757d',
                customClass: {
                    confirmButton: 'outbound-action-btn submit',
                    cancelButton: 'outbound-action-btn cancel'
                },
                preConfirm: () => {
                    const check1 = document.getElementById('check1')?.checked;
                    const check2 = document.getElementById('check2')?.checked;
                    const check3 = document.getElementById('check3')?.checked;

                    // Check if foreign currency checkbox exists and is checked
                    // Use a direct check for foreign currency instead of the method to avoid 'this' scope issues
                    const isForeignCurrency = invoice.currency_details?.data?.currencyCode &&
                                             invoice.currency_details?.data?.currencyCode !== 'MYR' &&
                                             invoice.currency_details?.data?.multiplier;

                    const check4 = isForeignCurrency ? document.getElementById('check4')?.checked : true;

                    if (!check1 || !check2 || !check3 || !check4) {
                        Swal.showValidationMessage('Please complete the verification checklist');
                        return false;
                    }
                    return true;
                }
            });
        } catch (error) {
            console.error('Error showing confirmation dialog:', error);
            throw error;
        }
    }

    async submitToLHDN(invoiceId) {
        try {
            // Prevent multiple submissions
            if (this.isSubmitting) {
                ////console.log('Submission already in progress, ignoring duplicate call');
                return;
            }

            this.isSubmitting = true;

            const now = Date.now();
            const timeSinceLastSubmission = now - this.lastSubmissionTime;

            if (timeSinceLastSubmission < this.submissionCooldown) {
                const waitTime = this.submissionCooldown - timeSinceLastSubmission;
                await this.showCooldownModal(waitTime);
            }

            this.lastSubmissionTime = Date.now();

            ////console.log('Starting submission to LHDN for invoice:', invoiceId);

            // Show loading indicator
            this.showLoading('submitting');

            // Get current invoice data from the modal
            const currentInvoice = this._currentInvoice;

            if (!currentInvoice) {
                console.error('No current invoice found for submission');
                Swal.fire({
                    icon: 'error',
                    title: 'Submission Error',
                    text: 'Could not find invoice data for submission. Please try again.',
                    confirmButtonColor: '#dc3545'
                });
                this.hideLoading();
                return;
            }

            ////console.log('Current invoice for submission:', currentInvoice);

            // Format invoice data for confirmation dialog
            const formattedInvoice = {
                invoiceNumber: currentInvoice.invoice_number ||
                              currentInvoice._rawInvoice?.invoiceNumber ||
                              'Unknown',
                date: currentInvoice.bqe_date ||
                      currentInvoice._rawInvoice?.date ||
                      new Date().toISOString(),
                invoiceAmount: currentInvoice.amount ||
                             currentInvoice._rawInvoice?.invoiceAmount ||
                             0
            };

            //console.log('Formatted invoice for confirmation:', formattedInvoice);

            // Show confirmation dialog
            const confirmResult = await this.showSubmissionConfirmDialog(formattedInvoice);

            if (!confirmResult.isConfirmed) {
                //console.log('Submission cancelled by user');
                this.hideLoading();
                return;
            }

            // Show version selection dialog
            const versionResult = await this.showVersionSelectionDialog();
            if (!versionResult.isConfirmed) {
                //console.log('Version selection cancelled by user');
                this.hideLoading();
                return;
            }

            // Get version from version selection dialog
            const version = versionResult.value;
            //console.log('Selected version for submission:', version);

            // Prepare API request
            const requestBody = {
                invoiceId: formattedInvoice.invoiceNumber,
                invoice_number: formattedInvoice.invoiceNumber,
                version: version,
                invoiceData: currentInvoice
            };

            //console.log('Submitting invoice with request:', requestBody);

            // Get CSRF token from cookie if available
            let csrfToken = document.cookie.split('; ')
                .find(row => row.startsWith('XSRF-TOKEN='))
                ?.split('=')[1];

            // If not in cookie, try meta tag
            if (!csrfToken) {
                const metaTag = document.querySelector('meta[name="csrf-token"]');
                csrfToken = metaTag ? metaTag.getAttribute('content') : '';
            }

            // Make API call to submit invoice
            const response = await fetch('/bqe/submit-to-lhdn', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken || '',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(requestBody)
            });

            // Parse response
            const result = await response.json();
            //console.log('Submission result:', result);

            // Hide loading indicator
            this.hideLoading();

            // Handle response
            if (result.success) {
                // Extract UUID information from the result
                const uuid = result.submissionDetails?.uuid || null;
                const submissionUuid = result.submissionDetails?.submissionUid || null;
                //console.log('Received UUIDs from submission:', { uuid, submissionUuid });

                // Show enhanced success message
                await Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    html: `
                        <div class="submission-success-container">
                            <div class="success-animation mb-3">
                                <div class="checkmark-circle">
                                    <div class="checkmark draw"></div>
                                </div>
                            </div>

                            <h5 class="mb-3">Invoice has been successfully submitted to LHDN</h5>

                            <div class="submission-details card mb-3">
                                <div class="card-body">
                                    <div class="row mb-2">
                                        <div class="col-5 text-start text-muted">Invoice Number:</div>
                                        <div class="col-7 text-start fw-bold">${currentInvoice.invoice_number || currentInvoice._rawInvoice?.invoiceNumber || 'N/A'}</div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-5 text-start text-muted">Submission Time:</div>
                                        <div class="col-7 text-start">
                                            <div>${result.submissionTime || new Date().toLocaleString()}</div>
                                            <div class="text-muted small">Just now</div>
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-5 text-start text-muted">Status:</div>
                                        <div class="col-7 text-start"><span class="badge bg-success">Submitted</span></div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-5 text-start text-muted">Version:</div>
                                        <div class="col-7 text-start"><span class="badge bg-secondary">${version || '1.0'}</span></div>
                                    </div>
                                    ${uuid ? `
                                    <div class="row mb-2">
                                        <div class="col-5 text-start text-muted">UUID:</div>
                                        <div class="col-7 text-start">
                                            <div class="d-flex align-items-center">
                                                <span class="text-monospace small uuid-text">${uuid}</span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2 copy-uuid-btn"
                                                        onclick="navigator.clipboard.writeText('${uuid}').then(() => {
                                                            this.innerHTML = '<i class=\\'bi bi-check\\'></i>';
                                                            setTimeout(() => { this.innerHTML = '<i class=\\'bi bi-clipboard\\'></i>'; }, 2000);
                                                        })">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>` : ''}
                                    ${this.isForeignCurrencyInvoice(currentInvoice) ? `
                                    <div class="row mb-2">
                                        <div class="col-5 text-start text-muted">Currency:</div>
                                        <div class="col-7 text-start">
                                            <span class="badge bg-info">${currentInvoice.currency_details?.data?.currencyCode || 'Foreign'}</span>
                                        </div>
                                    </div>` : ''}
                                </div>
                            </div>

                            <div class="next-steps alert alert-light border text-start">
                                <div class="fw-bold mb-2"><i class="bi bi-info-circle me-2"></i>What's Next?</div>
                                <ul class="mb-0 ps-3">
                                    <li>Your invoice has been recorded in the LHDN system</li>
                                    <li>You can view the submission status in the dashboard</li>
                                    <li>The invoice can be cancelled within 72 hours if needed</li>
                                </ul>
                            </div>

                            <div class="action-buttons mt-3">
                                <a href="/dashboard/inbound" class="btn btn-outline-primary">
                                    <i class="bi bi-grid me-2"></i>View in Dashboard
                                </a>
                            </div>
                        </div>
                    `,
                    confirmButtonText: 'Done',
                    confirmButtonColor: '#28a745',
                    customClass: {
                        popup: 'submission-success-popup',
                        confirmButton: 'btn-lg px-4'
                    },
                    width: '32rem'
                });

                // Add the required CSS for the animation and styling
                if (!document.getElementById('submission-success-styles')) {
                    const styleElement = document.createElement('style');
                    styleElement.id = 'submission-success-styles';
                    styleElement.textContent = `
                        .submission-success-container {
                            padding: 0.5rem;
                        }
                        .submission-success-popup {
                            border-radius: 16px !important;
                            padding: 1.5rem !important;
                            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
                        }
                        .success-animation {
                            margin: 0 auto;
                        }
                        .checkmark-circle {
                            width: 80px;
                            height: 80px;
                            position: relative;
                            display: inline-block;
                            vertical-align: top;
                            margin-left: auto;
                            margin-right: auto;
                        }
                        .checkmark-circle .background {
                            width: 80px;
                            height: 80px;
                            border-radius: 50%;
                            background: #28a745;
                            position: absolute;
                        }
                        .checkmark-circle .checkmark {
                            border-radius: 5px;
                        }
                        .checkmark-circle .checkmark.draw:after {
                            animation-delay: 0.2s;
                            animation-duration: 0.5s;
                            animation-name: checkmark;
                            animation-timing-function: ease;
                            animation-fill-mode: forwards;
                            transform-origin: 50% 50%;
                            opacity: 0;
                            transform: scale(0);
                            content: '';
                            width: 40px;
                            height: 80px;
                            border-right: 8px solid #28a745;
                            border-top: 8px solid #28a745;
                            border-radius: 2px;
                            position: absolute;
                            top: -5px;
                            left: 20px;
                            transform: rotate(135deg);
                        }
                        @keyframes checkmark {
                            0% {
                                opacity: 1;
                                transform: scale(0);
                            }
                            100% {
                                opacity: 1;
                                transform: scale(1) rotate(135deg);
                            }
                        }
                        .text-monospace {
                            font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
                            word-break: break-all;
                            font-size: 0.8rem;
                            color: #495057;
                        }
                        .submission-details {
                            background-color: #f8f9fa;
                            border: 1px solid rgba(0,0,0,0.1) !important;
                            border-radius: 10px !important;
                            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                        }
                        .submission-details .card-body {
                            padding: 1rem !important;
                        }
                        .next-steps {
                            background-color: #f8f9fa;
                            border-left: 4px solid #28a745 !important;
                            border-radius: 8px !important;
                        }
                        .copy-uuid-btn {
                            padding: 0.15rem 0.4rem;
                            font-size: 0.7rem;
                            border-radius: 4px;
                        }
                        .uuid-text {
                            max-width: 180px;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        .action-buttons {
                            display: flex;
                            justify-content: center;
                        }
                        .action-buttons .btn {
                            padding: 0.5rem 1.5rem;
                            border-radius: 8px;
                            font-weight: 500;
                            transition: all 0.2s ease;
                        }
                        .action-buttons .btn:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        }
                    `;
                    document.head.appendChild(styleElement);
                }

                // Update the table row data
                if (this.table) {
                    const rowData = this.table.rows().data().toArray();
                    const updatedData = rowData.map(row => {
                        if (row.id === invoiceId || row.invoice_number === formattedInvoice.invoiceNumber) {
                            return {
                                ...row,
                                status: 'Submitted',
                                date_submitted: result.submissionTime || new Date().toISOString(),
                                date_sync: new Date().toISOString(),
                                uuid: uuid, // Add UUID to the row data
                                submission_uuid: submissionUuid // Add submission UUID to the row data
                            };
                        }
                        return row;
                    });

                    // Update table with new data
                    this.table.clear();
                    this.table.rows.add(updatedData);
                    this.table.draw();

                    // Update card counts
                this.updateCardCounts();

                    // Store the UUIDs in the current invoice object too
                    if (this._currentInvoice) {
                        this._currentInvoice.uuid = uuid;
                        this._currentInvoice.submission_uuid = submissionUuid;
                        ////console.log('Updated current invoice with UUIDs:', {
                        //    uuid: this._currentInvoice.uuid,
                        //    submission_uuid: this._currentInvoice.submission_uuid
                        //});
                    }
                }

                // Close the view details modal
                const viewDetailsModal = document.getElementById('viewDetailsModal');
                if (viewDetailsModal) {
                    const modal = bootstrap.Modal.getInstance(viewDetailsModal);
                    if (modal) {
                        modal.hide();
                    }
                }
            } else {
                // Handle LHDN validation errors properly
                await this.showLHDNValidationError(result);
                console.error('Submission failed:', result);
            }
        } catch (error) {
            // Hide loading indicator
            this.hideLoading();

            // Format user-friendly error message
            let errorTitle = 'Submission Failed';
            let errorMessage = 'We were unable to process your submission at this time.';
            let errorDetails = '';
            let correlationId = '';

            // Extract correlation ID if available
            if (error.details && typeof error.details === 'string' && error.details.includes('Correlation Id')) {
                correlationId = error.details.match(/Correlation Id: ([A-Z0-9-]+)/)?.[1] || '';
            }

            // Handle specific error cases
            if (error.code === 'ValidationError') {
                errorTitle = 'Validation Error';
                errorMessage = 'Please check the following details:';
                errorDetails = error.details;
            } else if (error.code === 'DuplicateDocument') {
                errorTitle = 'Duplicate Submission';
                errorMessage = 'This invoice has already been submitted.';
            } else if (error.code === 'SystemError') {
                errorTitle = 'System Unavailable';
                errorMessage = 'The system is temporarily unavailable. Please try again in a few minutes.';
            }

            // Show user-friendly error dialog
            await Swal.fire({
                icon: 'error',
                title: errorTitle,
                html: `
                    <div class="error-dialog" style="border-radius: 10px; padding: 20px; background-color: #fefefe; box-shadow: 0 0 10px rgba(0,0,0,0.15);">
                        <div class="error-message" style="font-size: 1.1rem; color: #333; margin-bottom: 15px;">
                           ${errorMessage}
                        </div>
                        ${errorDetails ? `
                            <div class="validation-errors mt-3" style="border: 1px solid #ffc107; border-radius: 5px; padding: 15px; background-color: #fff3cd;">
                                <h6 class="alert-heading" style="font-weight: bold; color: #856404; margin-bottom: 10px;">Details:</h6>
                                <ul class="error-list" style="list-style: none; padding-left: 0;">
                                    ${Array.isArray(errorDetails) ?
                                        errorDetails.map(detail => `
                                            <li class="error-item" style="margin-bottom: 8px; color: #664d03;">
                                                <strong style="font-weight: bold;">${detail.field || 'Error'}</strong>:
                                                <span class="error-description">${detail.message}</span>
                                            </li>
                                        `).join('') :
                                        `<li class="error-item" style="color: #664d03;">
                                            <span class="error-description">${errorDetails}</span>
                                        </li>`
                                    }
                                </ul>
                            </div>
                        ` : ''}
                        ${correlationId ? `
                            <div class="mt-3" style="text-align: center;">
                                <small class="text-muted">
                                    If you need assistance, please contact support with this reference number:<br>
                                    <code style="background-color: #e9ecef; padding: 2px 5px; border-radius: 3px;">${correlationId}</code>
                                </small>
                            </div>
                        ` : ''}
                    </div>
                `,
                confirmButtonText: 'Close',
                confirmButtonColor: '#0d6efd',
                closeButtonColor: '#6c757d',
                allowOutsideClick: false,
                customClass: {
                    container: 'error-dialog-container',
                    popup: 'error-dialog-popup',
                    content: 'error-dialog-content',
                    confirmButton: 'outbound-action-btn submit',
                    closeButton: 'outbound-action-btn cancel'
                },
                buttonsStyling: false,
                didOpen: () => {
                    const popup = Swal.getPopup();
                    if (popup) {
                        popup.style.borderRadius = '15px';
                    }
                }
            });
            console.error('Error submitting invoice to LHDN:', error);
        } finally {
            // Reset submission flag
            this.isSubmitting = false;
        }
    }

    // Add method to show enhanced error modal
    async showEnhancedErrorModal(error) {
        console.log('Showing enhanced error modal for:', error);

        let title = 'Error';
        let message = 'An unexpected error occurred.';

        // Handle different types of errors
        if (error.message) {
            if (error.message.includes('No valid auth response available')) {
                title = 'Authentication Required';
                message = 'No valid auth response available. Please authorize BQE first.';
            } else if (error.message.includes('authentication')) {
                title = 'Authentication Error';
                message = 'Your session has expired. Please log in again.';
            } else if (error.message.includes('network') || error.message.includes('fetch')) {
                title = 'Network Error';
                message = 'Unable to connect to the server. Please check your internet connection and try again.';
            } else {
                message = error.message;
            }
        }

        // Use SweetAlert2 for consistent error display
        await Swal.fire({
            icon: 'error',
            title: title,
            text: message,
            confirmButtonText: 'OK',
            customClass: {
                confirmButton: 'btn btn-primary',
                popup: 'swal2-popup-custom'
            },
            buttonsStyling: false
        });
    }

    // Add method to handle LHDN validation errors
    async showLHDNValidationError(result) {
        let errorTitle = 'LHDN Validation Failed';
        let errorMessage = 'The document failed validation at LHDN.';
        let errorDetails = [];

        console.log('Processing LHDN validation error:', result);

        // Parse the error details from the result - handle multiple possible structures
        let validationResults = null;

        // Check different possible locations for validation results
        if (result.details && result.details.validationResults) {
            validationResults = result.details.validationResults;
        } else if (result.validationResults) {
            validationResults = result.validationResults;
        } else if (result.details && typeof result.details === 'string') {
            // Try to parse if details is a JSON string
            try {
                const parsedDetails = JSON.parse(result.details);
                if (parsedDetails.validationResults) {
                    validationResults = parsedDetails.validationResults;
                }
            } catch (e) {
                console.warn('Could not parse details as JSON:', e);
            }
        }

        // Process validation results if found
        if (validationResults && validationResults.validationSteps) {
            const failedSteps = validationResults.validationSteps.filter(step => step.status === 'Invalid');

            failedSteps.forEach(step => {
                if (step.error && step.error.innerError && Array.isArray(step.error.innerError)) {
                    step.error.innerError.forEach(innerError => {
                        errorDetails.push({
                            code: innerError.errorCode,
                            message: innerError.error,
                            field: innerError.propertyPath || innerError.propertyName,
                            description: innerError.errorMs || innerError.error
                        });
                    });
                } else if (step.error) {
                    errorDetails.push({
                        code: step.error.errorCode,
                        message: step.error.error,
                        field: step.error.propertyPath || step.error.propertyName,
                        description: step.error.errorMs || step.error.error
                    });
                }
            });
        }
        // Fallback: check if result has error details directly
        else if (result.details && Array.isArray(result.details)) {
            result.details.forEach(detail => {
                errorDetails.push({
                    code: detail.code || detail.errorCode,
                    message: detail.message || detail.error,
                    field: detail.target || detail.propertyPath || detail.propertyName,
                    description: detail.description || detail.errorMs
                });
            });
        }
        // Another fallback: single error object
        else if (result.error) {
            errorDetails.push({
                code: result.error.code || result.error.errorCode,
                message: result.error.message || result.error.error,
                field: result.error.target || result.error.propertyPath,
                description: result.error.description || result.error.errorMs
            });
        }

        // Use result message if available
        if (result.message) {
            errorMessage = result.message;
        }

        // Handle specific error codes
        if (errorDetails.length > 0) {
            const firstError = errorDetails[0];

            // Handle document reference validation errors (DR308)
            if (firstError.code === 'DR308') {
                errorTitle = 'Document Reference Error';
                errorMessage = 'The buyer information in this document does not match the referenced document.';
            }
            // Handle other specific error codes
            else if (firstError.code?.startsWith('CF')) {
                errorTitle = 'Field Validation Error';
                errorMessage = 'One or more fields contain invalid data.';
            }
            else if (firstError.code?.startsWith('DR')) {
                errorTitle = 'Document Reference Error';
                errorMessage = 'There is an issue with document references.';
            }
        }

        // Show the error modal
        await Swal.fire({
            icon: 'error',
            title: errorTitle,
            html: `
                <div class="lhdn-error-dialog" style="border-radius: 10px; padding: 20px; background-color: #fefefe;">
                    <div class="error-message" style="font-size: 1.1rem; color: #333; margin-bottom: 20px;">
                        ${errorMessage}
                    </div>
                    ${errorDetails.length > 0 ? `
                        <div class="validation-errors" style="border: 1px solid #dc3545; border-radius: 8px; padding: 15px; background-color: #f8d7da; margin-bottom: 15px;">
                            <h6 style="color: #721c24; margin-bottom: 15px; font-weight: bold;">
                                <i class="bi bi-exclamation-triangle me-2"></i>Validation Errors:
                            </h6>
                            <div class="error-list">
                                ${errorDetails.map(error => `
                                    <div class="error-item" style="margin-bottom: 12px; padding: 10px; background-color: #ffffff; border-radius: 5px; border-left: 4px solid #dc3545;">
                                        <div style="font-weight: bold; color: #721c24; margin-bottom: 5px;">
                                            Error Code: ${error.code}
                                        </div>
                                        <div style="color: #495057; margin-bottom: 5px;">
                                            ${error.message}
                                        </div>
                                        ${error.field ? `
                                            <div style="font-size: 0.9rem; color: #6c757d;">
                                                Field: <code style="background-color: #e9ecef; padding: 2px 4px; border-radius: 3px;">${error.field}</code>
                                            </div>
                                        ` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                    <div class="help-section" style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px;">
                        <h6 style="color: #0c5460; margin-bottom: 10px;">
                            <i class="bi bi-info-circle me-2"></i>What to do next:
                        </h6>
                        <ul style="color: #0c5460; margin-bottom: 0; padding-left: 20px;">
                            <li>Review the error details above</li>
                            <li>Correct the identified issues in your document</li>
                            <li>Ensure all required fields are properly filled</li>
                            <li>Try submitting the document again</li>
                        </ul>
                    </div>
                </div>
            `,
            confirmButtonText: 'I Understand',
            confirmButtonColor: '#dc3545',
            customClass: {
                popup: 'lhdn-validation-error-popup',
                confirmButton: 'btn btn-danger px-4'
            },
            buttonsStyling: false,
            width: '600px'
        });
    }

    async fetchCompanyDetails() {
        const response = await fetch('/bqe/company');
            if (!response.ok) {
            throw new Error('Failed to fetch company details');
        }
        return await response.json();
    }

    async fetchSupplierDetails() {
        const companyData = await this.fetchCompanyDetails();
            return {
            tin: companyData.customFields?.find(f => f.label === "Supplier's TIN")?.value,
            registrationNumber: companyData.customFields?.find(f => f.label === "Supplier's Registration No")?.value,
            sstId: companyData.customFields?.find(f => f.label === "Supplier's SST No")?.value,
            msicCode: companyData.customFields?.find(f => f.label === "Supplier's MSIC Code")?.value,
            businessActivity: companyData.customFields?.find(f => f.label === "Supplier's Business Activity")?.value
        };
    }

    // Add this method to InvoiceTableManager class
    async showVersionSelectionDialog() {
        return Swal.fire({
            title: 'Select Document Version',
            html: `
                <div class="notice-container mb-4 text-center" style="background-color: #f0f9ff; border-radius: 8px; padding: 16px 20px;">
                    <div class="d-flex align-items-center justify-content-center mb-1">
                        <i class="bi bi-info-circle" style="color: #3b82f6; font-size: 0.9rem;"></i>
                        <div style="color: #64748b; font-weight: 500; font-size: 0.9rem; margin-left: 6px;">Important Notice</div>
                    </div>
                    <p class="mb-0" style="color: #475569; line-height: 1.5; font-size: 0.85rem;">
                        Please select the appropriate document version based on your signing requirements. Your
                        selection will determine the final document format and processing workflow.
                    </p>
            </div>

                <div class="version-options">
                    <div class="version-option mb-3">
                        <label class="w-100 p-0 rounded cursor-pointer position-relative" style="border: 1px solid #e2e8f0;">
                            <div class="d-flex align-items-start p-3">
                                <input type="radio" name="version" value="1.0" checked
                                       class="form-check-input mt-1" style="width: 16px; height: 16px;">
                                <div class="ms-3 text-center w-100">
                                    <div class="mb-1 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-file-text me-2" style="color: #3b82f6;"></i>
                                        <span style="color: #334155; font-weight: 500;">Version 1.0 (Standard Format)</span>
            </div>
                                    <div style="color: #64748b; font-size: 0.85rem;">
                                        Basic document format without digital signature capabilities
                                    </div>
                                </div>
                            </div>
                        </label>
                    </div>

                    <div class="version-option mb-3">
                        <label class="w-100 p-0 rounded cursor-pointer position-relative" style="border: 1px solid #e2e8f0; background-color: #f8fafc;">
                            <div class="d-flex align-items-start p-3">
                                <input type="radio" name="version" value="1.1" disabled
                                       class="form-check-input mt-1" style="width: 16px; height: 16px;">
                                <div class="ms-3 text-center w-100">
                                    <div class="mb-1 d-flex align-items-center justify-content-center">
                                        <i class="bi bi-file-earmark-lock me-2" style="color: #94a3b8;"></i>
                                        <span style="color: #94a3b8; font-weight: 500;">Version 1.1 (Enhanced Security)</span>
                                    </div>
                                    <div style="color: #94a3b8; font-size: 0.85rem;">
                                        Advanced format with digital signature support
                                    </div>
                                </div>
                                <div class="tooltip-wrapper" style="margin-left: 8px;">
                                    <i class="bi bi-info-circle"
                                       style="cursor: help; font-size: 1rem; color: #3b82f6;"
                                       data-bs-toggle="tooltip"
                                       data-bs-html="true"
                                       data-bs-placement="right"
                                       title="<div style='min-width: 240px; text-align: left;'>
                                         <div style='margin-bottom: 8px; color: #000;'>
                                           Digital Certificate Required <span style='color: #dc2626;'>*</span>
                                         </div>
                                         <div style='color: #64748b; margin-bottom: 12px;'>
                                           To enable this version, please complete:
                                         </div>
                                         <div style='margin-left: 0;'>
                                           <div style='margin-bottom: 8px;'>
                                             <i class='bi bi-shield' style='color: #64748b; margin-right: 8px;'></i>
                                             <span style='color: #000;'>Must have digital certificate</span>
                                             <span style='color: #dc2626;'>*</span>
                                           </div>
                                           <div style='margin-bottom: 8px;'>
                                             <i class='bi bi-person-badge' style='color: #64748b; margin-right: 8px;'></i>
                                             <span style='color: #000;'>Set up signing credentials</span>
                                             <span style='color: #dc2626;'>*</span>
                                           </div>
                                           <div style='margin-bottom: 12px;'>
                                             <i class='bi bi-gear' style='color: #64748b; margin-right: 8px;'></i>
                                             <span style='color: #000;'>Configure signature parameters</span>
                                             <span style='color: #dc2626;'>*</span>
                                           </div>
                                         </div>
                                         <div style='color: #64748b; padding-top: 12px; border-top: 1px solid #e2e8f0;'>
                                           <i class='bi bi-headset'></i>
                                           <span style='margin-left: 8px;'>Contact administrator for assistance</span>
                                         </div>
                                       </div>"></i>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '<i class="bi bi-arrow-right-circle me-2"></i>Continue',
            cancelButtonText: '<i class="bi bi-x me-2"></i>Cancel',
            customClass: {
                container: 'version-selection-dialog',
                popup: 'shadow-sm rounded-3',
                confirmButton: 'btn btn-primary px-4',
                cancelButton: 'btn btn-secondary px-4',
                actions: 'gap-2'
            },
            buttonsStyling: false,
            preConfirm: () => {
                return document.querySelector('input[name="version"]:checked')?.value;
            }
        });
    }

    // Add this method to InvoiceTableManager class
    validateMandatoryFields(invoice) {
        const requiredFields = {
            'Invoice Number': invoice._rawInvoice?.invoiceNumber,
            'Invoice Date': invoice._rawInvoice?.date,
            'Invoice Amount': invoice._rawInvoice?.invoiceAmount,
            'Supplier Name': invoice.company?.name,
            'Supplier TIN': invoice.supplier?.tin,
            'Supplier Registration': invoice.supplier?.registrationNumber,
            'Customer Name': invoice._clientDetails?.company,
            'Customer TIN': invoice._clientDetails?.taxId,
           // 'Customer Registration': invoice._clientDetails?.registrationNumber,
        };

        const missingFields = [];
        for (const [field, value] of Object.entries(requiredFields)) {
            if (!value || value === 'NA') {
                missingFields.push(field);
            }
        }

        return {
            isValid: missingFields.length === 0,
            missingFields
        };
    }

    // Add this method to the InvoiceTableManager class
    updateTotalInvoicesFetched(count = 0) {
        try {
            // Update the records count display
            const recordsCount = document.querySelector('.records-count');
            if (recordsCount) {
                recordsCount.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span>Total Invoices Fetched: ${count}</span>
                        <i class="bi bi-question-circle ms-2"
                           data-bs-toggle="tooltip"
                           title="Total number of invoices found for selected date range"></i>
                    </div>`;

                // Initialize tooltip on the newly added icon
                const tooltipIcon = recordsCount.querySelector('[data-bs-toggle="tooltip"]');
                if (tooltipIcon) {
                    new bootstrap.Tooltip(tooltipIcon, {
                        placement: 'top',
                        trigger: 'hover'
                    });
                }
            }

            // Update table info if available
            const tableInfo = document.querySelector('.dataTables_info');
            if (tableInfo && this.dataTable) {
                const pageInfo = this.dataTable.page.info();
                if (pageInfo) {
                    const currentPage = pageInfo.page + 1;
                    const totalPages = pageInfo.pages;
                    tableInfo.textContent = `Showing ${count} entries (Page ${currentPage} of ${totalPages})`;
                }
            }

            // Update card counts
            this.updateCardCounts();

        } catch (error) {
            console.error('Error updating total invoices count:', error);
        }
    }

    // Add this method to handle saving invoices in batches
    async saveInvoicesInBatches(invoices, batchSize = 5) {
        try {
            const batches = [];
            for (let i = 0; i < invoices.length; i += batchSize) {
                batches.push(invoices.slice(i, i + batchSize));
            }

            //console.log(`Saving ${invoices.length} invoices in ${batches.length} batches`);

            let savedCount = 0;
            const results = [];

            for (let i = 0; i < batches.length; i++) {
                try {
                    const batch = batches[i];
                    const saveResponse = await fetch('/bqe/save-invoices', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ invoices: batch })
                    });

                    if (!saveResponse.ok) {
                        throw new Error(`Failed to save batch ${i + 1}`);
                    }

                    const saveResult = await saveResponse.json();
                    results.push(saveResult);
                    savedCount += batch.length;

                } catch (error) {
                    console.error(`Error saving batch ${i + 1}:`, error);
                    // Continue with next batch even if one fails
                }
            }

            return {
                success: true,
                totalSaved: savedCount,
                results: results
            };

        } catch (error) {
            console.error('Error in saveInvoicesInBatches:', error);
            throw new Error(`Failed to save invoices: ${error.message}`);
        }
    }

    // Add this method to format relative time
    formatRelativeTime(date) {
        if (!date) return '-';

        try {
            const now = moment();
            const syncDate = moment(date);
            const diffMinutes = now.diff(syncDate, 'minutes');

            if (diffMinutes < 1) return 'just now';
            if (diffMinutes < 60) return `${diffMinutes} minutes ago`;

            const diffHours = now.diff(syncDate, 'hours');
            if (diffHours < 24) return `${diffHours} hours ago`;

            const diffDays = now.diff(syncDate, 'days');
            if (diffDays < 30) return `${diffDays} days ago`;

            return syncDate.format('YYYY-MM-DD');
        } catch (error) {
            console.error('Error formatting relative time:', error);
            return '-';
        }
    }

    prepareInvoiceData(invoiceId) {
        try {
            //console.log("Preparing invoice data for invoice ID:", invoiceId);

            // Get the invoice data from the current modal if available, or fetch it
            let invoice = this._currentInvoice; // Use _currentInvoice which is set in updateModalContent

            if (!invoice && invoiceId) {
                // Fetch the invoice data if not already loaded in modal
                invoice = this.invoices.find(inv => inv.id === invoiceId);
                if (!invoice) {
                    throw new Error(`Invoice with ID ${invoiceId} not found`);
                }
            }

            if (!invoice) {
                throw new Error("No invoice data available");
            }

            // Safely access line items - create empty array if none exist
            const lineItems = invoice._rawInvoice?.line_items || [];

            // Create a safe version of the raw invoice to ensure all properties exist
            const rawInvoice = {
                id: invoiceId || invoice.id,
                invoiceNumber: invoice._rawInvoice?.invoiceNumber || invoice.invoice_number,
                date: invoice._rawInvoice?.date || new Date().toISOString(),
                invoiceAmount: invoice._rawInvoice?.invoiceAmount ||
                    parseFloat((invoice.amount || '0').toString().replace(/[^0-9.]/g, '')),
                serviceTaxAmount: invoice._rawInvoice?.serviceTaxAmount || 0,
                currency: invoice._rawInvoice?.currency || 'MYR',
                type: invoice._rawInvoice?.type || 13,
                invoiceFrom: invoice._rawInvoice?.invoiceFrom || {},
                invoiceTo: invoice._rawInvoice?.invoiceTo || {},
                rfNumber: invoice._rawInvoice?.rfNumber || `RF${invoice.invoice_number || ''}`,
                referenceNumber: invoice._rawInvoice?.referenceNumber || '',
                referenceType: invoice._rawInvoice?.referenceType || '',
                referenceDescription: invoice._rawInvoice?.referenceDescription || '',
                custom_fields: invoice._rawInvoice?.custom_fields || []
            };

            // Safely get custom field values
            const getTaxRate = () => {
                const taxRateField = rawInvoice.custom_fields.find(f =>
                    f && typeof f === 'object' && f.label === 'Tax Rate'
                );
                return taxRateField && !isNaN(parseFloat(taxRateField.value))
                    ? parseFloat(taxRateField.value)
                    : 8; // Default to 8% if not found (common service tax rate in Malaysia)
            };

            // Get tax rate value for calculations
            const taxRate = getTaxRate();

            // Create basic invoice details
            const invoiceDetails = {
                description: 'Service Fee',
                amount: rawInvoice.invoiceAmount,
                taxRate: taxRate
            };

            // Create a default line item with the invoice amount
            const defaultLineItem = {
                id: 'default-line',
                lineId: 1,
                description: 'Service Fee',
                amount: rawInvoice.invoiceAmount,
                quantity: 1,
                unitPrice: rawInvoice.invoiceAmount,
                taxRate: taxRate,
                projectId: '',
                project: '',
                unitCode: "EA",
                price: {
                    amount: rawInvoice.invoiceAmount
                },
                item: {
                    description: 'Service Fee',
                    classification: {
                        code: "022",
                        type: "UNSPSC"
                    }
                }
            };

            // Add the required invoice_line_items array
            const invoice_line_items = [{
                lineId: 1,
                description: 'Service Fee',
                quantity: 1,
                unitCode: "EA",
                price: {
                    amount: rawInvoice.invoiceAmount
                },
                amount: rawInvoice.invoiceAmount,
                taxRate: taxRate,
                item: {
                    description: 'Service Fee',
                    classification: {
                        code: "022",
                        type: "UNSPSC"
                    }
                }
            }];

            // Create supplier info safely
            const supplierInfo = {
                tin: invoice.supplier?.tin ||
                    invoice.company?.customFields?.find?.(f => f.label === "Supplier's TIN")?.value || '',
                registrationNumber: invoice.supplier?.registrationNumber ||
                    invoice.company?.customFields?.find?.(f => f.label === "Supplier's Registration No")?.value || '',
                sstId: invoice.supplier?.sstId ||
                    invoice.company?.customFields?.find?.(f => f.label === "Supplier's SST No")?.value || '',
                msicCode: invoice.supplier?.msicCode ||
                    invoice.company?.customFields?.find?.(f => f.label === "Supplier's MSIC Code")?.value || '',
                businessActivity: invoice.supplier?.businessActivity || "Engineering Services"
            };

            // Return the prepared data structure
            return {
                id: invoiceId || invoice.id,
                _rawInvoice: rawInvoice,
                _clientDetails: invoice._clientDetails || {},
                company: invoice.company || {},
                supplier: supplierInfo,
                // Include the line items in both formats for maximum compatibility
                invoiceDetails: [invoiceDetails],
                lineItems: [lineItems],
                invoice_line_items: invoice_line_items,
                rows: [defaultLineItem]
            };
        } catch (error) {
            console.error("Error preparing invoice data:", error);
            throw new Error(`Failed to prepare invoice data: ${error.message}`);
        }
    }

    async checkStagingDatabase() {
        const fromDate = document.getElementById('fromDate')?.value;
        const toDate = document.getElementById('toDate')?.value;

        const stagingResponse = await fetch('/bqe/check-staging', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ fromDate, toDate })
        });

        const stagingData = await stagingResponse.json();

        // Create staging map with all available data
        const stagingMap = {};
        if (stagingData.hasData) {
            stagingData.invoices.forEach(inv => {
                if (inv.invoice_number) {
                    stagingMap[inv.invoice_number] = {
                        status: inv.status || 'Pending',
                        date_submitted: inv.date_submitted,
                        date_sync: inv.date_sync,
                    };
                }
            });
        }

        return { stagingData, stagingMap };
    }

    mapInvoicesWithStagingData(invoices, { stagingMap }) {
        return invoices.map(invoice => {
            const stagingInfo = stagingMap[invoice.invoice_number];

            if (stagingInfo) {
                // For submitted invoices
                if (stagingInfo.status === 'Submitted') {
                    return {
                        ...invoice,
                        status: stagingInfo.status,
                        date_submitted: stagingInfo.date_submitted,
                        date_sync: stagingInfo.date_sync,
                        submission_timestamp: stagingInfo.submission_timestamp
                    };
                }

                // For other statuses (Pending, Cancelled)
                return {
                    ...invoice,
                    status: stagingInfo.status,
                    date_submitted: null,
                    date_sync: stagingInfo.date_sync,
                    submission_timestamp: null
                };
            }

            // For new invoices not in staging
            return {
                ...invoice,
                status: 'Pending',
                date_submitted: null,
                date_sync: moment().format('YYYY-MM-DD HH:mm:ss'),
                submission_timestamp: null
            };
        });
    }

    async saveAndUpdateUI(mappedInvoices) {
        try {
            const saveResults = await this.saveInvoicesInBatches(mappedInvoices);
            //console.log('Save results:', saveResults);

            if (saveResults.success) {
                // Clear any cached data if the collections exist
                if (this.clientDetailsCache && typeof this.clientDetailsCache.clear === 'function') {
                    this.clientDetailsCache.clear();
                }
                if (this.projectDetailsCache && typeof this.projectDetailsCache.clear === 'function') {
                    this.projectDetailsCache.clear();
                }

                // Update the UI elements
                this.updateCardCounts();

                // Use the correct table instance
                if (this.table && typeof this.table.clear === 'function') {
                    this.table.clear();
                    this.table.rows.add(mappedInvoices);
                    this.table.draw();
                } else {
                    console.error('DataTable instance not found, attempting to reinitialize');
                    this.initializeTable();
                    if (this.table) {
                        this.table.clear();
                        this.table.rows.add(mappedInvoices);
                        this.table.draw();
                    }
                }
            }

            return saveResults;
        } catch (error) {
            console.error('Error in saveAndUpdateUI:', error);
            throw error;
        }
    }

    // Add these methods to the InvoiceTableManager class
    calculateDuration() {
        if (!this.startTime) return '';
        const duration = Date.now() - this.startTime;

        if (duration < 1000) {
            return 'less than a second';
        } else if (duration < 60000) {
            const seconds = Math.floor(duration / 1000);
            return `${seconds} second${seconds !== 1 ? 's' : ''}`;
        } else {
            const minutes = Math.floor(duration / 60000);
            const seconds = Math.floor((duration % 60000) / 1000);
            return `${minutes} minute${minutes !== 1 ? 's' : ''} ${seconds} second${seconds !== 1 ? 's' : ''}`;
        }
    }

    calculateEstimatedTimeLeft(currentStep, totalSteps) {
        if (!this.startTime || currentStep === 0) return '';

        const elapsedTime = Date.now() - this.startTime;
        const averageTimePerStep = elapsedTime / currentStep;
        const remainingSteps = totalSteps - currentStep;
        const estimatedTimeLeft = averageTimePerStep * remainingSteps;

        if (estimatedTimeLeft < 5000) return '';

        if (estimatedTimeLeft < 60000) {
            const seconds = Math.ceil(estimatedTimeLeft / 1000);
            return ` • Est. ${seconds}s remaining`;
        } else {
            const minutes = Math.ceil(estimatedTimeLeft / 60000);
            return ` • Est. ${minutes}m remaining`;
        }
    }

    // Add this method to handle loading dots animation
    updateLoadingDots(element) {
        if (!this.loadingDotsInterval) {
            let dots = 0;
            this.loadingDotsInterval = setInterval(() => {
                if (element && document.body.contains(element)) {
                    const currentText = element.textContent.replace(/\.+$/, '');
                    dots = (dots + 1) % 4;
                    element.textContent = currentText + '.'.repeat(dots);
                } else {
                    // Clear interval if element is no longer in the DOM
                    clearInterval(this.loadingDotsInterval);
                    this.loadingDotsInterval = null;
                }
            }, 500);
        }
    }

    async cancelInvoice(invoice) {
        //console.log('===== CANCEL INVOICE PROCESS STARTED =====');
        //console.log('Cancel button clicked for invoice:', invoice);

        try {
            // Ensure invoice is an object with a proper structure
            if (typeof invoice === 'string') {
                //console.log('Invoice is a string, converting to object:', invoice);
                invoice = { id: invoice };
            } else if (!invoice || typeof invoice !== 'object') {
                console.error('Invalid invoice format:', invoice);
                throw new Error('Invalid invoice format');
            }

            // Close modals and remove focus before showing cancel dialog
            //console.log('1. Closing any open modals...');
            const modals = document.querySelectorAll('.modal');
            for (const modal of modals) {
                // Check if this modal has bootstrap instance
                const instance = bootstrap.Modal.getInstance(modal);
                if (instance) {
                    //console.log('Found modal to close:', modal.id);
                    // Set attribute to prevent accessibility issues
                    modal.setAttribute('inert', '');
                    instance.hide();
                }
            }

            // Add a delay to ensure modals have time to close
            //console.log('2. Waiting for modals to close...');
            await new Promise(resolve => setTimeout(resolve, 500));

            // Remove any inert attributes after delay
            //console.log('3. Removing inert attributes...');
            document.querySelectorAll('[inert]').forEach(el => {
                el.removeAttribute('inert');
            });

            // Check if cancellation is expired (72 hours = 3 days)
            //console.log('4. Checking cancellation time window...');

            // Make sure we have an invoice_number if possible
            if (!invoice.invoice_number && invoice._rawInvoice?.invoiceNumber) {
                invoice.invoice_number = invoice._rawInvoice.invoiceNumber;
                //console.log('Updated invoice_number from _rawInvoice:', invoice.invoice_number);
            }

            // If invoice object has a number property, try that as well
            if (!invoice.invoice_number && invoice.number) {
                invoice.invoice_number = invoice.number;
                //console.log('Updated invoice_number from number property:', invoice.invoice_number);
            }

            // Check if cancellation time limit is expired (72 hours = 3 days)
            if (invoice.date_submitted) {
                const submissionDate = moment(invoice.date_submitted);
                const now = moment();
                const diffHours = now.diff(submissionDate, 'hours');
                //console.log(`Hours since submission: ${diffHours} (limit: 72 hours)`);

                if (diffHours > 72) {
                    //console.log('Cancellation window expired');

                    await Swal.fire({
                        icon: 'error',
                        title: 'Cancellation Window Expired',
                        html: `
                            <div class="text-start">
                                <p>This invoice cannot be cancelled because it was submitted more than 72 hours ago.</p>
                                <p>Submission date: ${submissionDate.format('DD-MM-YYYY HH:mm:ss')}</p>
                                <p>Current time: ${now.format('DD-MM-YYYY HH:mm:ss')}</p>
                                <p>Hours elapsed: ${diffHours}</p>
                            </div>
                        `,
                        confirmButtonText: 'OK'
                    });
                    return;
                }
            }

            // IMPORTANT: Check UUID fields - these come from LHDN
            //console.log('5. Checking UUID fields from LHDN...');
            //console.log('Initial UUID values:');
            //console.log('- uuid:', invoice.uuid);
            //console.log('- submission_uuid:', invoice.submission_uuid);

            // If we don't have the uuid, try to get it from backend
            if (!invoice.uuid) {
                //console.log('UUID is missing, attempting to fetch from database...');

                // Show loading state while we fetch UUIDs
            Swal.fire({
                    title: 'Looking up Invoice Data...',
                    text: 'Please wait while we retrieve the necessary information.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                try {
                    // First try direct lookup using invoice_number if available
                    if (invoice.invoice_number) {
                        //console.log('Attempting direct UUID lookup using invoice number:', invoice.invoice_number);

                        const directLookupResponse = await fetch('/bqe/get-invoice-uuids', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                invoice_number: invoice.invoice_number
                            })
                        });

                        if (directLookupResponse.ok) {
                            const lookupResult = await directLookupResponse.json();
                            //console.log('UUID lookup result:', lookupResult);

                            if (lookupResult.success && lookupResult.invoice) {
                                //console.log('Found invoice via direct lookup:', lookupResult.invoice);
                                invoice.uuid = lookupResult.invoice.uuid;
                                invoice.submission_uuid = lookupResult.invoice.submission_uuid;

                                ////console.log('Updated invoice with UUIDs from database:', {
                                //    uuid: invoice.uuid,
                                //    submission_uuid: invoice.submission_uuid
                                //});

                                // If the invoice ID wasn't set properly before, set it now
                                if (!invoice.id || typeof invoice.id === 'object') {
                                    invoice.id = lookupResult.invoice.id;
                                    ////console.log('Updated invoice ID from direct lookup:', invoice.id);
                                }

                                // If this direct lookup worked, we can skip the staging fetch
                                if (invoice.uuid) {
                                    Swal.close();
                                    return; // Exit the method as we have the UUID now
                                }
                            }
                        }
                    }

                    // Then try to fetch all staging data (this can be a fallback)
                    //console.log('Trying fallback to staging database check');
                    const stagingResponse = await fetch('/bqe/check-staging', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            fromDate: moment().subtract(90, 'days').format('YYYY-MM-DD'),
                            toDate: moment().format('YYYY-MM-DD')
                        })
                    });

                    if (stagingResponse.ok) {
                        const stagingData = await stagingResponse.json();
                        //console.log('Retrieved staging data:', stagingData);

                        if (stagingData.hasData && stagingData.invoices) {
                            // Find the invoice with matching invoice number or ID (case insensitive check)
                            const matchedInvoice = stagingData.invoices.find(inv => {
                                // Try matching by invoice number first
                                if (invoice.invoice_number && inv.invoice_number) {
                                    const matchByNumber = String(inv.invoice_number).toLowerCase() ===
                                        String(invoice.invoice_number).toLowerCase();
                                    if (matchByNumber) return true;
                                }

                                // Then try matching by ID
                                if (invoice.id && inv.id) {
                                    const matchById = String(inv.id) === String(invoice.id);
                                    if (matchById) return true;
                                }

                                // Finally try matching by BQE invoice ID
                                if (invoice.id && inv.bqe_invoice_id) {
                                    const matchByBqeId = String(inv.bqe_invoice_id) === String(invoice.id);
                                    if (matchByBqeId) return true;
                                }

                                return false;
                            });

                            if (matchedInvoice) {
                                //console.log('Found matching invoice in staging database:');
                                //console.log('- invoice_number:', matchedInvoice.invoice_number);
                                //console.log('- uuid:', matchedInvoice.uuid);
                                //console.log('- submission_uuid:', matchedInvoice.submission_uuid);

                                // Update both UUID fields if they exist in the matched invoice
                                if (matchedInvoice.uuid) {
                                    invoice.uuid = matchedInvoice.uuid;
                                    //console.log('Updated invoice UUID from staging:', invoice.uuid);
                                }

                                if (matchedInvoice.submission_uuid) {
                                    invoice.submission_uuid = matchedInvoice.submission_uuid;
                                    //console.log('Updated invoice submission UUID from staging:', invoice.submission_uuid);
                                }

                                // If the invoice ID wasn't set properly before, set it now
                                if (!invoice.id || typeof invoice.id === 'object') {
                                    invoice.id = matchedInvoice.id;
                                    //console.log('Updated invoice ID:', invoice.id);
                                }
                            } else {
                                //console.log('No matching invoice found in staging data');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error fetching database data:', error);
                }
            }

            // Still no UUID? Try fetching the specific invoice if we have the ID
            if (!invoice.uuid && invoice.id) {
                try {
                    //console.log('Fetching invoice details for ID:', invoice.id);
                    const invoiceDetailsResponse = await fetch(`/bqe/invoice/${invoice.id}`);
                    if (invoiceDetailsResponse.ok) {
                        const invoiceDetails = await invoiceDetailsResponse.json();
                        //console.log('Fetched invoice details:', invoiceDetails);

                        // Check for both UUID fields
                        if (invoiceDetails.uuid) {
                            //console.log('Found UUID in invoice details:', invoiceDetails.uuid);
                            invoice.uuid = invoiceDetails.uuid;
                        }

                        if (invoiceDetails.submission_uuid) {
                            //console.log('Found submission_uuid in invoice details:', invoiceDetails.submission_uuid);
                            invoice.submission_uuid = invoiceDetails.submission_uuid;
                        }
                    }
                } catch (error) {
                    console.error('Error fetching invoice details:', error);
                }
            }

            // Close loading dialog
            Swal.close();

            //console.log('UUID fields after retrieval attempts:');
            //console.log('- uuid:', invoice.uuid);
            //console.log('- submission_uuid:', invoice.submission_uuid);

            // If we still don't have a UUID, show error
            if (!invoice.uuid) {
                console.error('UUID is still missing after all attempts to retrieve it');

                // Add a button to manually enter UUIDs as a workaround
                await Swal.fire({
                icon: 'error',
                    title: 'Cannot Cancel Invoice',
                    html: `
                        <div class="text-start">
                            <p>Required UUID information is missing. The invoice cannot be cancelled automatically.</p>
                            <p class="small text-danger mt-3">Technical details:</p>
                            <div class="small text-muted">
                                <code>uuid: ${invoice.uuid || 'missing'}</code><br>
                                <code>submission_uuid: ${invoice.submission_uuid || 'missing'}</code><br>
                                <code>invoice_number: ${invoice.invoice_number}</code><br>
                                <code>invoice_id: ${invoice.id || 'unknown'}</code>
                            </div>
                            <p class="mt-3">Would you like to manually enter the UUID values?</p>
                        </div>
                    `,
                    showCancelButton: true,
                    showDenyButton: true,
                    confirmButtonText: 'Enter UUIDs',
                    denyButtonText: 'View Technical Help',
                    cancelButtonText: 'Cancel',
                    allowOutsideClick: false
                }).then(async (result) => {
                    if (result.isConfirmed) {
                        // Show UUID entry form
                        const { value: formValues } = await Swal.fire({
                            title: 'Enter UUID Values',
                            html: `
                                <div class="text-start">
                                    <p class="small text-muted mb-3">Please enter the UUID values from the LHDN system:</p>
                                    <div class="mb-3">
                                        <label for="manual-uuid" class="form-label">UUID:</label>
                                        <input id="manual-uuid" class="form-control" value="${invoice.uuid || ''}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="manual-submission-uuid" class="form-label">Submission UUID:</label>
                                        <input id="manual-submission-uuid" class="form-control" value="${invoice.submission_uuid || ''}">
                                    </div>
                                </div>
                            `,
                            focusConfirm: false,
                            showCancelButton: true,
                            preConfirm: () => {
                                return {
                                    uuid: document.getElementById('manual-uuid').value,
                                    submissionUuid: document.getElementById('manual-submission-uuid').value
                                }
                            }
                        });

                        if (formValues) {
                            invoice.uuid = formValues.uuid;
                            invoice.submission_uuid = formValues.submissionUuid;
                            //console.log('Manually entered UUID values:', formValues);

                            // Retry the cancellation with the manual values
                            if (invoice.uuid) {
                                this.cancelInvoice(invoice);
                            } else {
                                Swal.fire('Error', 'UUID is required for cancellation.', 'error');
                            }
                        }
                    } else if (result.isDenied) {
                        // Show technical help
                        Swal.fire({
                            title: 'Technical Help',
                            html: `
                                <div class="text-start">
                                    <p>To cancel this invoice, you need the UUID from the LHDN system.</p>
                                    <p>You can find this by:</p>
                                    <ol>
                                        <li>Check the invoice in the LHDN portal</li>
                                        <li>Look for the document UUID/identifier</li>
                                        <li>Enter this value in the UUID field</li>
                                    </ol>
                                    <p>Alternatively, contact technical support for assistance.</p>
                                </div>
                            `,
                            confirmButtonText: 'OK'
                        });
                    }
                });

                return;
            }

            // Use vanilla JS confirm to simplify
            //console.log('6. Showing cancellation confirmation dialog...');
            // Log the full invoice object to help debug
            //console.log('Full invoice object before showing dialog:', JSON.stringify(invoice, null, 2));

            const confirmResult = await Swal.fire({
                title: 'Cancel Invoice',
                html: `
                    <div class="mb-3">
                        <p class="text-start">
                            <strong>Invoice Number:</strong> ${invoice.invoice_number || 'N/A'}<br>
                            <strong>Date Submitted:</strong> ${moment(invoice.date_submitted).format('DD-MM-YYYY HH:mm:ss')}<br>
                            <strong>UUID:</strong> ${invoice.uuid || 'N/A'}<br>
                            <strong>ID:</strong> ${invoice.id || 'N/A'}
                        </p>
                        <div class="form-group text-start">
                            <label for="cancellation-reason" class="form-label">Cancellation Reason <span class="text-danger">*</span></label>
                            <textarea id="cancellation-reason" class="form-control" rows="3" placeholder="Please provide a reason for cancellation"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Yes, cancel it',
                cancelButtonText: 'No, keep it',
                confirmButtonColor: '#dc3545',
                focusConfirm: false,
                allowOutsideClick: false
            });

            //console.log('7. Confirmation dialog result:', confirmResult);
            if (!confirmResult.isConfirmed) {
                //console.log('Cancellation cancelled by user');
                return;
            }

            const reason = document.getElementById('cancellation-reason')?.value?.trim();
            //console.log('8. Cancellation reason:', reason);

            if (!reason) {
                //console.log('No cancellation reason provided');
                await Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Please provide a cancellation reason'
                });
                return;
            }

            // Show loading state
            //console.log('9. Showing loading state...');
            Swal.fire({
                title: 'Cancelling Invoice...',
                text: 'Please wait while we process your request.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Examine the invoice object to ensure we have the right keys
            //console.log('10. Full invoice object (after UUID retrieval):', JSON.stringify(invoice, null, 2));

            //console.log('11. Key invoice properties:');
            //console.log('- id:', invoice.id);
            //console.log('- bqe_invoice_id:', invoice.bqe_invoice_id);
            //console.log('- uuid:', invoice.uuid);
            //console.log('- submission_uuid:', invoice.submission_uuid);
            //console.log('- invoice_number:', invoice.invoice_number);
            //console.log('- status:', invoice.status);

            // Create the request payload with correct field names
            const payload = {
                invoiceId: invoice.id || invoice.bqe_invoice_id,
                uuid: invoice.uuid || '',
                reason: reason,
                invoice_number: invoice.invoice_number || ''
            };

            // Make sure the invoice_number is included
            if (!payload.invoice_number && invoice._rawInvoice?.invoiceNumber) {
                payload.invoice_number = invoice._rawInvoice.invoiceNumber;
                //console.log('Updated invoice_number from _rawInvoice:', payload.invoice_number);
            }

            // Log request details for debugging
            //console.log('12. Cancellation request payload:', payload);

            // Final UUID check before submitting
            if (!payload.uuid || payload.uuid === 'null' || payload.uuid === 'undefined') {
                console.error('UUID is still null or invalid in the payload:', payload.uuid);
                await Swal.fire({
                    icon: 'error',
                    title: 'Invalid UUID',
                    html: `
                        <div class="text-start">
                            <p>The UUID value is invalid or missing. Cannot proceed with cancellation.</p>
                            <p class="small text-danger mt-3">Technical info:</p>
                            <div class="small text-muted">
                                <code>uuid: ${payload.uuid || 'missing'}</code><br>
                                <code>invoiceId: ${payload.invoiceId || 'missing'}</code><br>
                                <code>invoice_number: ${payload.invoice_number}</code>
                            </div>
                        </div>
                    `
                });
                return;
            }

            //console.log('13. Sending cancellation request to /bqe/cancel-invoice...');
            const response = await fetch('/bqe/cancel-invoice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            //console.log('14. API response status:', response.status, response.statusText);
            //console.log('15. API response headers:', response.headers);

            const data = await response.json();
            //console.log('16. API response data:', data);

            if (!response.ok) {
                //console.log('18. Request failed - response was not OK');

                // Handle specific error cases
                if (response.status === 404 || (data.error && data.error.includes('UUID'))) {
                    let errorTitle = 'UUID Not Found';
                    let errorMessage = `
                        <div class="text-start">
                            <p>The invoice's UUID was not found in LHDN's system. This can happen if:</p>
                            <ul>
                                <li>The invoice was not successfully submitted to LHDN</li>
                                <li>The UUID was not properly saved during submission</li>
                                <li>The invoice was already cancelled</li>
                            </ul>
                            <p class="mt-3">Please verify the invoice status in LHDN's portal or contact support for assistance.</p>
                            <p class="small text-danger mt-3">Technical details:</p>
                            <div class="small text-muted">
                                <code>UUID: ${payload.uuid}</code><br>
                                <code>Invoice Number: ${payload.invoice_number}</code><br>
                                <code>Error: ${data.error || 'UUID not found in LHDN system'}</code>
                            </div>
                        </div>
                    `;

                    await Swal.fire({
                        icon: 'error',
                        title: errorTitle,
                        html: errorMessage
                    });
                    return;
                }

                // Handle other errors
                throw new Error(data.error || 'Failed to cancel invoice');
            }

            // Success case
            //console.log('17. Cancellation successful:', data);
            await Swal.fire({
                icon: 'success',
                title: 'Invoice Cancelled',
                text: 'The invoice has been successfully cancelled.'
            });

            // Refresh the table to show updated status
                await this.refreshInvoiceTable();

        } catch (error) {
            console.error('===== CANCEL INVOICE PROCESS FAILED =====');
            console.error('Error cancelling invoice:', error);
            console.error('Error stack:', error.stack);

            // Show error to user
            await Swal.fire({
                icon: 'error',
                title: 'Cancellation Failed',
                html: `
                    <div class="text-start">
                        <p>Failed to cancel the invoice. Please try again or contact support if the issue persists.</p>
                        <p class="small text-danger mt-3">Error details:</p>
                        <div class="small text-muted">
                            ${error.message}
                        </div>
                    </div>
                `
            });
        } finally {
            // Hide loading state
            this.hideLoading();
        }
    }

    // Add a method to reset the current invoice state
    resetCurrentInvoice() {
        //console.log('Resetting current invoice state');
        this._currentInvoice = null;
        this._currentModalInvoice = null;

        // Clear any stored data
        this._invoiceDetails = null;
        this._lastSubmittedInvoice = null;

        // Reset currency indicator to default
        const currencyIndicator = document.getElementById('currency-indicator');
        if (currencyIndicator) {
            currencyIndicator.textContent = 'MYR';
            currencyIndicator.className = 'ms-2 badge bg-secondary';
            currencyIndicator.style.cursor = 'help';
            currencyIndicator.removeAttribute('data-bs-toggle');
            currencyIndicator.removeAttribute('data-bs-placement');
            currencyIndicator.removeAttribute('title');

            // Destroy tooltip if it exists
            const tooltip = bootstrap.Tooltip.getInstance(currencyIndicator);
            if (tooltip) {
                tooltip.dispose();
            }
        }

        // Reset the modal content
        const modalBody = document.querySelector('#invoice-details-modal .modal-body');
        if (modalBody) {
            modalBody.innerHTML = '<div class="loading-container"><div class="spinner-border text-primary" role="status"></div><p>Loading invoice details...</p></div>';
        }
    }

    // Add this method to fetch invoice details including UUID
    async fetchInvoiceDetails(invoiceId) {
        try {
            const response = await fetch(`/bqe/invoice/${invoiceId}`);
            if (!response.ok) {
                throw new Error('Failed to fetch invoice details');
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching invoice details:', error);
            return null;
        }
    }

    // Add this method to the InvoiceTableManager class for debugging
    async showManualUUIDDialog() {
        Swal.fire({
            title: 'Manually Set UUIDs',
            html: `
                <div class="text-start">
                    <p class="small text-muted mb-3">For debugging purposes only:</p>
                    <div class="mb-3">
                        <label for="debug-invoice-number" class="form-label">Invoice Number:</label>
                        <input id="debug-invoice-number" class="form-control" placeholder="e.g. TestTax GIL 03">
                    </div>
                    <div class="mb-3">
                        <label for="debug-uuid" class="form-label">UUID:</label>
                        <input id="debug-uuid" class="form-control" placeholder="e.g. abc123-def456-ghi789">
                    </div>
                    <div class="mb-3">
                        <label for="debug-submission-uuid" class="form-label">Submission UUID (optional):</label>
                        <input id="debug-submission-uuid" class="form-control" placeholder="Will use UUID if not provided">
                    </div>
                </div>
            `,
            focusConfirm: false,
            showCancelButton: true,
            confirmButtonText: 'Set UUIDs',
            confirmButtonColor: '#0d6efd',
            preConfirm: () => {
                const invoiceNumber = document.getElementById('debug-invoice-number').value;
                const uuid = document.getElementById('debug-uuid').value;
                const submissionUuid = document.getElementById('debug-submission-uuid').value;

                if (!invoiceNumber || !uuid) {
                    Swal.showValidationMessage('Invoice Number and UUID are required');
                    return false;
                }

                return {
                    invoice_number: invoiceNumber,
                    uuid: uuid,
                    submission_uuid: submissionUuid || uuid
                };
            }
        }).then(async (result) => {
            if (result.isConfirmed) {
                const data = result.value;

                try {
                    // Show loading indicator
                    Swal.fire({
                        title: 'Setting UUIDs...',
                        text: 'Please wait while we update the database.',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Make API call
                    const response = await fetch('/bqe/debug-set-uuids', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    const responseData = await response.json();

                    if (response.ok && responseData.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'UUIDs Set Successfully',
                            html: `
                                <div class="text-start">
                                    <p>UUIDs have been set for invoice: <strong>${data.invoice_number}</strong></p>
                                    <p class="small text-muted">UUID: ${data.uuid}</p>
                                    <p class="small text-muted">Submission UUID: ${data.submission_uuid}</p>
                                </div>
                            `
                        });

                        // Refresh the table
                        await this.refreshInvoiceTable();
                    } else {
                        throw new Error(responseData.error || 'Failed to set UUIDs');
                    }
                } catch (error) {
                    console.error('Error setting UUIDs:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: error.message || 'An error occurred while setting UUIDs'
                    });
                }
            }
        });
    }

    async handleDataSourceChange(event) {
        const selectedId = event.target.id;
        // Check for both possible IDs: bqe (BQE) and manualSubmission (Manual)
        this.currentDataSource = selectedId === 'bqe' ? 'bqe' : 'manual';

        console.log('Data source changed to:', this.currentDataSource, 'from element:', selectedId);

        // Clear current table data
        if (this.table) {
            this.table.clear().draw();
        }

        // Show/hide appropriate UI elements based on data source
        this.toggleUIElements();

        // Reset card counts
        this.updateCardCounts();

        // Auto-load data for manual submissions
        if (this.currentDataSource === 'manual') {
            console.log('Switching to manual mode, loading manual submission data...');
            await this.refreshManualSubmissionTable();
        }
    }

    toggleUIElements() {
        const filtersSection = document.querySelector('.filters-section');
        const excelUploadSection = document.getElementById('excelUploadSection');
        const primaryFiltersRow = document.querySelector('.row.g-3.mb-3'); // Global search and quick filters
        const quickFilters = document.querySelector('.quick-filters');
        const globalSearchWrapper = document.querySelector('.search-wrapper');
        const advancedFilters = document.getElementById('advancedFilters'); // Advanced filters collapse

        console.log('toggleUIElements - currentDataSource:', this.currentDataSource);
        console.log('Elements found:', {
            filtersSection: !!filtersSection,
            excelUploadSection: !!excelUploadSection,
            primaryFiltersRow: !!primaryFiltersRow,
            quickFilters: !!quickFilters,
            globalSearchWrapper: !!globalSearchWrapper,
            advancedFilters: !!advancedFilters
        });

        if (this.currentDataSource === 'bqe') {
            // Show BQE filters, hide Excel upload
            console.log('Showing BQE elements, hiding Excel upload');
            if (filtersSection) {
                filtersSection.classList.remove('d-none');
                filtersSection.style.display = '';
            }
            if (excelUploadSection) {
                excelUploadSection.classList.add('d-none');
                excelUploadSection.style.display = 'none';
            }
            if (primaryFiltersRow) {
                primaryFiltersRow.classList.remove('d-none');
                primaryFiltersRow.style.display = '';
            }
            if (quickFilters) {
                quickFilters.classList.remove('d-none');
                quickFilters.style.display = '';
            }
            if (globalSearchWrapper) {
                globalSearchWrapper.classList.remove('d-none');
                globalSearchWrapper.style.display = '';
            }
            if (advancedFilters) {
                advancedFilters.style.display = '';
            }
        } else {
            // Hide BQE filters, show Excel upload
            console.log('Hiding BQE elements, showing Excel upload');
            if (filtersSection) {
                filtersSection.classList.add('d-none');
                // Force hide with inline style to override BQE auth script
                filtersSection.style.display = 'none';
            }
            if (excelUploadSection) {
                excelUploadSection.classList.remove('d-none');
                excelUploadSection.style.display = '';
            }
            if (primaryFiltersRow) {
                primaryFiltersRow.classList.add('d-none');
                primaryFiltersRow.style.display = 'none';
            }
            if (quickFilters) {
                quickFilters.classList.add('d-none');
                quickFilters.style.display = 'none';
            }
            if (globalSearchWrapper) {
                globalSearchWrapper.classList.add('d-none');
                globalSearchWrapper.style.display = 'none';
            }
            if (advancedFilters) {
                advancedFilters.style.display = 'none';
            }
        }
    }

    initializeExcelUpload() {
        // Create Excel upload section if it doesn't exist
        this.createExcelUploadSection();

        // Initialize file upload handlers
        const fileInput = document.getElementById('excelFileInput');
        const uploadBtn = document.getElementById('uploadExcelBtn');
        const dropZone = document.getElementById('excelDropZone');

        if (fileInput && uploadBtn) {
            // File input change handler
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleFileSelection(file);
                }
            });

            // Upload button click handler
            uploadBtn.addEventListener('click', () => {
                const file = fileInput.files[0];
                if (file) {
                    this.uploadExcelFile(file);
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'No File Selected',
                        text: 'Please select an Excel file to upload.',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }

        if (dropZone) {
            // Drag and drop handlers
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (this.validateExcelFile(file)) {
                        fileInput.files = files;
                        this.handleFileSelection(file);
                    }
                }
            });
        }
    }

    createExcelUploadSection() {
        // Check if section already exists
        if (document.getElementById('excelUploadSection')) {
            return;
        }


        const filtersSection = document.querySelector('.filters-section');
        if (!filtersSection) {
            return;
        }

        const excelUploadHTML = `
            <div id="excelUploadSection" class="excel-upload-section d-none">
                <div class="upload-header">
                    <div class="upload-title">
                        <i class="bi bi-file-earmark-excel me-2"></i>
                        Excel File Upload
                        <span class="upload-info-badge ms-2" data-bs-toggle="tooltip"
                            title="Upload Excel files for manual e-invoice submission. Supported formats: .xlsx, .xls">
                            <i class="bi bi-info-circle"></i>
                            Manual Submission
                        </span>
                    </div>
                </div>

                <div class="upload-body">
                    <div class="upload-area">
                        <!-- Drop Zone -->
                        <div id="excelDropZone" class="drop-zone">
                            <div class="drop-zone-content">
                                <i class="bi bi-cloud-upload drop-icon"></i>
                                <h5>Drag & Drop Excel File Here</h5>
                                <p class="text-muted">or click to browse files</p>
                                <div class="supported-formats">
                                    <small class="text-muted">
                                        <i class="bi bi-check-circle text-success me-1"></i>Supported: .xlsx, .xls
                                        <span class="mx-2">|</span>
                                        <i class="bi bi-info-circle text-info me-1"></i>Max size: 10MB
                                    </small>
                                </div>
                            </div>
                            <input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;">
                        </div>

                        <!-- File Info -->
                        <div id="fileInfo" class="file-info d-none">
                            <div class="file-details">
                                <i class="bi bi-file-earmark-excel text-success"></i>
                                <div class="file-text">
                                    <div class="file-name"></div>
                                    <div class="file-size text-muted"></div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger remove-file">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Document Type Selection -->
                    <div class="document-type-section mt-3">
                        <label class="form-label">
                            <i class="bi bi-file-text me-2"></i>Document Type
                        </label>
                        <select id="documentTypeSelect" class="form-select">
                            <option value="">Select Document Type</option>
                            <option value="invoice">Invoice</option>
                            <option value="credit-note">Credit Note</option>
                            <option value="debit-note">Debit Note</option>
                            <option value="refund-note">Refund Note</option>
                            <option value="self-billed-invoice">Self-Billed Invoice</option>
                            <option value="self-billed-credit-note">Self-Billed Credit Note</option>
                            <option value="self-billed-debit-note">Self-Billed Debit Note</option>
                            <option value="self-billed-refund-note">Self-Billed Refund Note</option>
                            <option value="consolidation">Consolidation</option>
                        </select>
                    </div>

                    <!-- Upload Actions -->
                    <div class="upload-actions mt-3">
                        <button id="uploadExcelBtn" class="btn outbound-action-btn submit" disabled>
                            <i class="bi bi-upload me-1"></i>
                            Upload & Process
                        </button>
                        <button id="downloadTemplateBtn" class="btn outbound-action-btn cancel">
                            <i class="bi bi-download me-1"></i>
                            Download Template
                        </button>
                    </div>
                </div>

                <!-- Upload Progress -->
                <div id="uploadProgress" class="upload-progress d-none">
                    <div class="progress-info">
                        <span class="progress-text">Processing file...</span>
                        <span class="progress-percentage">0%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;

        // Insert after filters section
        filtersSection.insertAdjacentHTML('afterend', excelUploadHTML);

        // Add event listeners for the new elements
        const dropZone = document.getElementById('excelDropZone');
        const fileInput = document.getElementById('excelFileInput');
        const removeFileBtn = document.querySelector('.remove-file');
        const downloadTemplateBtn = document.getElementById('downloadTemplateBtn');

        if (dropZone && fileInput) {
            dropZone.addEventListener('click', () => {
                fileInput.click();
            });
        }

        if (removeFileBtn) {
            removeFileBtn.addEventListener('click', () => {
                this.clearFileSelection();
            });
        }

        if (downloadTemplateBtn) {
            downloadTemplateBtn.addEventListener('click', () => {
                this.downloadExcelTemplate();
            });
        }

        // Document type change handler
        const documentTypeSelect = document.getElementById('documentTypeSelect');
        if (documentTypeSelect) {
            documentTypeSelect.addEventListener('change', () => {
                this.updateUploadButtonState();
            });
        }
    }

    handleFileSelection(file) {
        if (!this.validateExcelFile(file)) {
            return;
        }

        // Show file info
        const fileInfo = document.getElementById('fileInfo');
        const dropZone = document.getElementById('excelDropZone');

        if (fileInfo && dropZone) {
            const fileName = fileInfo.querySelector('.file-name');
            const fileSize = fileInfo.querySelector('.file-size');

            if (fileName) fileName.textContent = file.name;
            if (fileSize) fileSize.textContent = this.formatFileSize(file.size);

            dropZone.classList.add('d-none');
            fileInfo.classList.remove('d-none');
        }

        this.updateUploadButtonState();
    }

    validateExcelFile(file) {
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(file.type)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid File Type',
                text: 'Please select a valid Excel file (.xlsx or .xls)',
                confirmButtonText: 'OK'
            });
            return false;
        }

        if (file.size > maxSize) {
            Swal.fire({
                icon: 'error',
                title: 'File Too Large',
                text: 'File size must be less than 10MB',
                confirmButtonText: 'OK'
            });
            return false;
        }

        return true;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateUploadButtonState() {
        const fileInput = document.getElementById('excelFileInput');
        const documentTypeSelect = document.getElementById('documentTypeSelect');
        const uploadBtn = document.getElementById('uploadExcelBtn');

        if (uploadBtn && fileInput && documentTypeSelect) {
            const hasFile = fileInput.files && fileInput.files.length > 0;
            const hasDocumentType = documentTypeSelect.value !== '';

            uploadBtn.disabled = !(hasFile && hasDocumentType);
        }
    }

    clearFileSelection() {
        const fileInput = document.getElementById('excelFileInput');
        const fileInfo = document.getElementById('fileInfo');
        const dropZone = document.getElementById('excelDropZone');

        if (fileInput) {
            fileInput.value = '';
        }

        if (fileInfo && dropZone) {
            fileInfo.classList.add('d-none');
            dropZone.classList.remove('d-none');
        }

        this.updateUploadButtonState();
    }

    async uploadExcelFile(file) {
        const documentType = document.getElementById('documentTypeSelect').value;

        if (!documentType) {
            Swal.fire({
                icon: 'warning',
                title: 'Document Type Required',
                text: 'Please select a document type before uploading.',
                confirmButtonText: 'OK'
            });
            return;
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('documentType', documentType);

        try {
            this.showUploadProgress();

            const response = await fetch('/api/outbound-files/upload-consolidated', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                await Swal.fire({
                    icon: 'success',
                    title: 'Upload Successful',
                    text: `Successfully processed ${result.recordsCount || 0} records from the Excel file.`,
                    confirmButtonText: 'OK'
                });

                // Refresh the table to show new data
                await this.refreshManualSubmissionTable();
                this.clearFileSelection();
            } else {
                throw new Error(result.error || 'Upload failed');
            }
        } catch (error) {
            console.error('Excel upload error:', error);
            await Swal.fire({
                icon: 'error',
                title: 'Upload Failed',
                text: error.message || 'An error occurred while uploading the file.',
                confirmButtonText: 'OK'
            });
        } finally {
            this.hideUploadProgress();
        }
    }

    showUploadProgress() {
        const uploadProgress = document.getElementById('uploadProgress');
        const uploadActions = document.querySelector('.upload-actions');

        if (uploadProgress) {
            uploadProgress.classList.remove('d-none');
        }
        if (uploadActions) {
            uploadActions.style.opacity = '0.5';
            uploadActions.style.pointerEvents = 'none';
        }
    }

    hideUploadProgress() {
        const uploadProgress = document.getElementById('uploadProgress');
        const uploadActions = document.querySelector('.upload-actions');

        if (uploadProgress) {
            uploadProgress.classList.add('d-none');
        }
        if (uploadActions) {
            uploadActions.style.opacity = '1';
            uploadActions.style.pointerEvents = 'auto';
        }
    }

    async refreshManualSubmissionTable() {
        try {
            console.log('Refreshing manual submission table...');

            // Only proceed if we're in manual mode
            if (this.currentDataSource !== 'manual') {
                console.log('Not in manual mode, skipping refresh');
                return;
            }

            // Show loading state
            if (this.table) {
                // DataTables doesn't have a processing() method, we'll handle loading differently
                console.log('Starting to load manual submission data...');
            }

            // Fetch data from list-fixed-paths endpoint
            const response = await fetch('/api/outbound-files/list-fixed-paths', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Manual submission data received:', data);

            if (!data.success) {
                throw new Error(data.error || 'Failed to fetch manual submission data');
            }

            // Transform the file data to match our table structure
            const transformedData = this.transformManualSubmissionData(data.files || []);
            console.log('Transformed manual submission data:', transformedData);

            // Clear and populate the table
            if (this.table) {
                this.table.clear();
                this.table.rows.add(transformedData);
                this.table.draw();
            }

            // Update card counts
            this.updateCardCounts();

        } catch (error) {
            console.error('Error refreshing manual submission table:', error);

            // Show error message
            await Swal.fire({
                icon: 'error',
                title: 'Error Loading Data',
                text: 'Failed to load manual submission data. Please try again.',
                confirmButtonText: 'OK'
            });

            // Clear table on error
            if (this.table) {
                this.table.clear().draw();
            }
        } finally {
            // Hide loading state
            if (this.table) {
                console.log('Finished loading manual submission data');
            }
        }
    }

    transformManualSubmissionData(files) {
        return files.map((file, index) => {
            // The API already returns the correct structure, just add source field
            return {
                ...file,
                source: 'manual',
                id: file.id || `manual_${index}_${Date.now()}`
            };
        });
    }

    determineFileStatus(file) {
        // Determine status based on file properties
        if (file.status) {
            return file.status;
        }

        // Check if file is in outgoing folder (submitted)
        if (file.path && file.path.includes('Outgoing')) {
            return 'Submitted';
        }

        // Check if file is in incoming folder (pending)
        if (file.path && file.path.includes('Incoming')) {
            return 'Pending';
        }

        return 'Pending';
    }

    determineDocumentType(fileName) {
        if (!fileName) return 'Invoice';

        const lowerName = fileName.toLowerCase();

        if (lowerName.includes('credit') || lowerName.includes('cn')) {
            return 'Credit Note';
        } else if (lowerName.includes('debit') || lowerName.includes('dn')) {
            return 'Debit Note';
        } else if (lowerName.includes('refund')) {
            return 'Refund Note';
        } else {
            return 'Invoice';
        }
    }

    extractInvoiceNumber(fileName) {
        if (!fileName) return 'N/A';

        // Try to extract invoice number from filename
        // Remove file extension
        const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');

        // Look for common invoice number patterns
        const patterns = [
            /INV[_-]?(\d+)/i,           // INV123, INV_123, INV-123
            /INVOICE[_-]?(\d+)/i,       // INVOICE123, INVOICE_123
            /(\d{4,})/,                 // Any 4+ digit number
            /([A-Z]+\d+)/i              // Letters followed by numbers
        ];

        for (const pattern of patterns) {
            const match = nameWithoutExt.match(pattern);
            if (match) {
                return match[1] || match[0];
            }
        }

        // If no pattern matches, use the filename without extension
        return nameWithoutExt;
    }

    extractCustomerName(fileName) {
        if (!fileName) return null;

        // Try to extract customer name from filename
        // This is a basic implementation - you might want to enhance this
        const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
        const parts = nameWithoutExt.split(/[_-]/);

        // Look for parts that might be customer names (not numbers or common prefixes)
        const customerParts = parts.filter(part =>
            part.length > 2 &&
            !/^\d+$/.test(part) &&
            !/(inv|invoice|cn|dn|credit|debit|refund)/i.test(part)
        );

        return customerParts.length > 0 ? customerParts.join(' ') : null;
    }

    downloadExcelTemplate() {
        const documentType = document.getElementById('documentTypeSelect').value;

        if (!documentType) {
            Swal.fire({
                icon: 'info',
                title: 'Select Document Type',
                text: 'Please select a document type to download the appropriate template.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Map document type names to codes (matching the option values in HTML)
        const docTypeMap = {
            'invoice': '01',
            'credit-note': '02',
            'debit-note': '03',
            'refund-note': '04',
            'self-billed-invoice': '11',
            'self-billed-credit-note': '12',
            'self-billed-debit-note': '13',
            'self-billed-refund-note': '14'
        };

        const docTypeCode = docTypeMap[documentType];
        if (!docTypeCode) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Document Type',
                text: 'Selected document type is not supported.',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Map document type codes to display names
        const docTypeDisplayNames = {
            '01': 'Invoice',
            '02': 'Credit Note',
            '03': 'Debit Note',
            '04': 'Refund Note',
            '11': 'Self-billed Invoice',
            '12': 'Self-billed Credit Note',
            '13': 'Self-billed Debit Note',
            '14': 'Self-billed Refund Note'
        };

        const displayName = docTypeDisplayNames[docTypeCode];

        // Create download link with document type code
        const downloadUrl = `/api/outbound-files/download-template?type=${encodeURIComponent(docTypeCode)}`;

        // Show enhanced download modal with invoice number input
        Swal.fire({
            icon: 'info',
            title: 'Download Template',
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <p><strong>Document Type:</strong> ${displayName} (${docTypeCode})</p>
                    </div>

                    <div class="mb-3">
                        <label for="invoiceNumberInput" class="form-label">
                            <i class="fas fa-file-invoice me-2"></i>Invoice Number (Optional)
                        </label>
                        <input type="text"
                               class="form-control"
                               id="invoiceNumberInput"
                               placeholder="Enter invoice number (e.g., INV-2024-001)"
                               maxlength="50">
                        <small class="text-muted">
                            This will replace 'CONSOFILENAME' in the template and auto-populate the invoice number field.
                        </small>
                    </div>

                    <div class="mb-3">
                        <p><strong>Template will be downloaded as:</strong></p>
                        <div class="alert alert-info">
                            <code id="filenamePreview">${docTypeCode}_CONSOFILENAME_eInvoice_[timestamp].xls</code>
                        </div>
                        <small class="text-muted">
                            The timestamp will be automatically generated when you download the file.
                        </small>
                    </div>
                </div>

                <script>
                    // Update filename preview when invoice number changes
                    document.getElementById('invoiceNumberInput').addEventListener('input', function(e) {
                        const invoiceNumber = e.target.value.trim();
                        const preview = document.getElementById('filenamePreview');
                        const filename = invoiceNumber
                            ? '${docTypeCode}_' + invoiceNumber + '_eInvoice_[timestamp].xls'
                            : '${docTypeCode}_CONSOFILENAME_eInvoice_[timestamp].xls';
                        preview.textContent = filename;
                    });
                </script>
            `,
            showCancelButton: true,
            confirmButtonText: 'Download',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const invoiceNumber = document.getElementById('invoiceNumberInput').value.trim();
                return { invoiceNumber };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const { invoiceNumber } = result.value;

                // Create download URL with invoice number if provided
                let finalDownloadUrl = downloadUrl;
                if (invoiceNumber) {
                    finalDownloadUrl += `&invoiceNumber=${encodeURIComponent(invoiceNumber)}`;
                }

                // Create temporary link and trigger download
                const link = document.createElement('a');
                link.href = finalDownloadUrl;
                // Let the server determine the filename with proper timestamp
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message with invoice number info
                const successMessage = invoiceNumber
                    ? `Template download started with invoice number: ${invoiceNumber}`
                    : 'Template download has been initiated.';

                Swal.fire({
                    icon: 'success',
                    title: 'Download Started',
                    text: successMessage,
                    timer: 3000,
                    showConfirmButton: false
                });
            }
        });
    }

    async submitManualFile(fileName, filePath, rowData, buttonElement) {
        try {
            console.log('🚀 Starting manual submission process:', fileName, filePath, rowData);

            // Reset button state first
            const originalContent = buttonElement.innerHTML;
            buttonElement.disabled = false;
            buttonElement.innerHTML = originalContent;

            // First check authentication status (following consolidate flow pattern)
            try {
                if (window.AuthStatusUtil && typeof window.AuthStatusUtil.checkLHDNAuthStatus === 'function') {
                    const authStatus = await window.AuthStatusUtil.checkLHDNAuthStatus();
                    if (!authStatus) {
                        console.warn('Authentication check failed before submission');
                        // Show auth error modal
                        if (window.AuthStatusUtil.showAuthErrorModal) {
                            window.AuthStatusUtil.showAuthErrorModal({
                                code: 'AUTH_ERROR',
                                message: 'Authentication error. Please log in again.',
                                details: 'Your session may have expired or the authentication token is invalid.'
                            });
                        }
                        return;
                    }
                }
            } catch (authError) {
                console.error('Auth check error:', authError);
                // Continue with the request, the server will handle auth errors if they occur
            }

            // Extract required parameters from rowData
            const type = rowData.documentType || 'Manual';

            // Get company information from rowData or settings
            let company = rowData.company || 'Default';
            if (company === 'Default') {
                try {
                    const settingsResponse = await fetch('/api/settings', {
                        credentials: 'same-origin'
                    });
                    if (settingsResponse.ok) {
                        const settingsData = await settingsResponse.json();
                        company = settingsData.data?.company?.name || 'Default';
                    }
                } catch (error) {
                    console.warn('Could not fetch company settings, using default:', error);
                }
            }

            // For manual submissions, use the current date (when file was uploaded) instead of issue date from Excel
            // This ensures we look in the correct directory where the file is actually stored
            let date;
            if (rowData.uploadedDate) {
                // Convert uploadedDate to YYYY-MM-DD format
                date = new Date(rowData.uploadedDate).toISOString().split('T')[0];
            } else {
                // Fallback to current date
                date = new Date().toISOString().split('T')[0];
            }

            console.log('Using date for file lookup:', date, 'from uploadedDate:', rowData.uploadedDate);

            // Follow the same modal flow as consolidate
            await this.submitToLHDNManual(fileName, type, company, date);

        } catch (error) {
            console.error('Error in manual submission process:', error);

            // Check if it's an authentication error
            if (error.code === 'AUTH_ERROR' || error.message?.includes('authentication')) {
                if (window.AuthStatusUtil && window.AuthStatusUtil.showAuthErrorModal) {
                    window.AuthStatusUtil.showAuthErrorModal(window.AuthStatusUtil.handleAuthError(error));
                } else {
                    await Swal.fire({
                        icon: 'error',
                        title: 'Authentication Error',
                        text: 'Your session has expired. Please log in again.',
                        confirmButtonText: 'OK'
                    });
                }
            } else {
                // Show error message
                await Swal.fire({
                    icon: 'error',
                    title: 'Submission Failed',
                    text: error.message || 'Failed to submit file to LHDN. Please try again.',
                    confirmButtonText: 'OK'
                });
            }
        }
    }

    // Main submission function following consolidate flow pattern
    async submitToLHDNManual(fileName, type, company, date) {
        console.log('🚀 Starting manual submission process:', { fileName, type, company, date });

        try {
            // 1. Show version selection dialog
            console.log('📋 Step 1: Showing version selection dialog');
            const version = await this.showVersionDialogManual();
            console.log('📋 Version selected:', version);

            if (!version) {
                console.log('❌ Version selection cancelled');
                return;
            }

            // 2. Show confirmation dialog
            console.log('🔍 Step 2: Showing confirmation dialog');
            const confirmed = await this.showConfirmationDialogManual(fileName, type, company, date, version);
            console.log('🔍 Confirmation result:', confirmed);

            if (!confirmed) {
                console.log('❌ Submission cancelled by user');
                return;
            }

            // 3. Show submission status modal and start process
            console.log('📤 Step 3: Starting submission status process');
            await this.showSubmissionStatusManual(fileName, type, company, date, version);

        } catch (error) {
            console.error('❌ Manual submission error:', error);

            // Check if it's an authentication error
            if (error.code === 'AUTH_ERROR' || error.message?.includes('authentication')) {
                if (window.AuthStatusUtil && window.AuthStatusUtil.showAuthErrorModal) {
                    window.AuthStatusUtil.showAuthErrorModal(window.AuthStatusUtil.handleAuthError(error));
                }
                return;
            }

            await Swal.fire({
                icon: 'error',
                title: 'Submission Error',
                text: error.message || 'An error occurred during submission.',
                confirmButtonText: 'OK'
            });
        }
    }

    // Version selection dialog for manual submissions
    async showVersionDialogManual() {
        // Load the CSS file if not already loaded
        if (!document.querySelector('link[href*="manual-submission-modals.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/assets/css/manual-submission-modals.css';
            document.head.appendChild(link);
        }

        return Swal.fire({
            html: `
                <div class="manual-modal-header">
                    <div class="manual-modal-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="manual-modal-title">Select LHDN Version</div>
                    <div class="manual-modal-subtitle">Choose the version for your manual submission</div>
                </div>
                <div class="manual-modal-body">
                    <div class="manual-version-container">
                        <div class="manual-version-card selected" data-version="1.0">
                            <div class="manual-version-header">
                                <div class="manual-version-title">Version 1.0</div>
                                <div class="manual-version-badge manual-badge-available">Available</div>
                            </div>
                            <div class="manual-version-description">
                                Standard e-invoice format compatible with all LHDN systems. Recommended for most submissions.
                            </div>
                        </div>

                        <div class="manual-version-card disabled" data-version="1.1">
                            <div class="manual-version-header">
                                <div class="manual-version-title">Version 1.1</div>
                                <div class="manual-version-badge manual-badge-coming-soon">Coming Soon</div>
                            </div>
                            <div class="manual-version-description">
                                Enhanced format with digital signature support and advanced validation features.
                            </div>
                        </div>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Continue',
            cancelButtonText: 'Cancel',
            width: 520,
            padding: '0',
            customClass: {
                popup: 'manual-submission-modal',
                confirmButton: 'manual-btn manual-btn-primary',
                cancelButton: 'manual-btn manual-btn-secondary'
            },
            buttonsStyling: false,
            didOpen: () => {
                document.querySelectorAll('.manual-version-card:not(.disabled)').forEach(card => {
                    card.addEventListener('click', () => {
                        document.querySelector('.manual-version-card.selected')?.classList.remove('selected');
                        card.classList.add('selected');
                    });
                });
            }
        }).then((result) => {
            if (result.isConfirmed) {
                return '1.0';
            }
            return null;
        });
    }

    // Confirmation dialog for manual submissions
    async showConfirmationDialogManual(fileName, type, company, date, version) {
        return Swal.fire({
            html: `
                <div class="manual-modal-header">
                    <div class="manual-modal-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="manual-modal-title">Confirm Manual Submission</div>
                    <div class="manual-modal-subtitle">Please review the document details before submitting to LHDN</div>
                </div>
                <div class="manual-modal-body">
                    <div class="manual-details-container">
                        <div class="manual-details-header">
                            <div class="manual-details-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="manual-details-title">Document Details</div>
                        </div>

                        <div class="manual-detail-row">
                            <span class="manual-detail-label">File Name:</span>
                            <span class="manual-detail-value">${fileName}</span>
                        </div>
                        <div class="manual-detail-row">
                            <span class="manual-detail-label">Source:</span>
                            <span class="manual-detail-value">${type}</span>
                        </div>
                        <div class="manual-detail-row">
                            <span class="manual-detail-label">Company:</span>
                            <span class="manual-detail-value">${company}</span>
                        </div>
                        <div class="manual-detail-row">
                            <span class="manual-detail-label">Upload Date:</span>
                            <span class="manual-detail-value">${new Date(date).toLocaleString()}</span>
                        </div>
                        <div class="manual-detail-row">
                            <span class="manual-detail-label">LHDN Version:</span>
                            <span class="manual-detail-value">
                                <span class="manual-version-badge manual-badge-available">${version}</span>
                            </span>
                        </div>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Yes, Submit to LHDN',
            cancelButtonText: 'Cancel',
            width: 520,
            padding: '0',
            focusConfirm: false,
            customClass: {
                popup: 'manual-submission-modal',
                confirmButton: 'manual-btn manual-btn-primary',
                cancelButton: 'manual-btn manual-btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => result.isConfirmed);
    }

    // Base template for semi-minimal dialog
    createSemiMinimalDialog(options) {
        const {
            title,
            subtitle,
            content
        } = options;

        return `
            <style>
                .content-card {
                    background: white;
                    border-radius: 8px;
                    border: 1px solid hsl(214 32% 91%);
                    padding: 1rem;
                    margin-bottom: 1rem;
                }

                .content-header {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 1rem;
                    padding-bottom: 0.75rem;
                    border-bottom: 1px solid hsl(214 32% 91%);
                }

                .content-badge {
                    width: 32px;
                    height: 32px;
                    background: hsl(220 76% 97%);
                    color: hsl(220 76% 55%);
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                }

                .content-title {
                    font-weight: 600;
                    color: hsl(220 39% 11%);
                }

                .field-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0.5rem 0;
                    border-bottom: 1px solid hsl(214 32% 95%);
                }

                .field-row:last-child {
                    border-bottom: none;
                }

                .field-label {
                    font-weight: 500;
                    color: hsl(215 16% 47%);
                }

                .field-value {
                    color: hsl(220 39% 11%);
                    font-weight: 500;
                }
            </style>

            <div style="text-align: center; margin-bottom: 1.5rem;">
                <div style="font-size: 1.125rem; font-weight: 600; color: hsl(220 39% 11%); margin-bottom: 0.25rem;">
                    ${title}
                </div>
                <div style="font-size: 0.875rem; color: hsl(215 16% 47%); line-height: 1.4;">
                    ${subtitle}
                </div>
            </div>

            ${content}
        `;
    }

    // Submission status modal with multi-step progress for manual submissions
    async showSubmissionStatusManual(fileName, type, company, date, version) {
        console.log('🚀 Starting manual submission status process:', { fileName, type, company, date, version });

        try {
            // Get the modal element from HTML
            const modalElement = document.getElementById('manualSubmissionModal');
            if (!modalElement) {
                throw new Error('Manual submission modal not found in HTML');
            }

            // Reset all steps to initial state
            console.log('🔄 Resetting steps to initial state');
            for (let i = 1; i <= 3; i++) {
                const step = document.getElementById(`manualStep${i}`);
                if (step) {
                    step.classList.remove('processing', 'completed', 'error');
                    const statusEl = step.querySelector('.manual-step-status');
                    if (statusEl) {
                        statusEl.textContent = 'Waiting...';
                        statusEl.style.opacity = '1';
                    }

                    // Reset icon
                    const iconContainer = step.querySelector('.manual-step-icon');
                    if (iconContainer) {
                        iconContainer.classList.remove('spinning');
                        const icon = iconContainer.querySelector('.fas');
                        if (icon) {
                            icon.style.display = 'block';
                            // Reset to original icons
                            if (i === 1) icon.className = 'fas fa-search';
                            else if (i === 2) icon.className = 'fas fa-paper-plane';
                            else if (i === 3) icon.className = 'fas fa-cogs';
                        }
                    }
                }
            }

            // Show the modal using Bootstrap
            console.log('📦 Showing submission modal');
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Wait for modal to be fully shown
            await new Promise(resolve => {
                modalElement.addEventListener('shown.bs.modal', resolve, { once: true });
            });

            // Verify steps were created
            console.log('🔍 Verifying step elements:');
            for (let i = 1; i <= 3; i++) {
                const step = document.getElementById(`manualStep${i}`);
                if (step) {
                    console.log(`✅ Manual Step ${i} element found`);
                } else {
                    console.error(`❌ Manual Step ${i} element not found`);
                }
            }

            // Step 1: Internal Validation
            console.log('🔍 Starting Step 1: Document Validation');
            await this.updateStepStatusManual(1, 'processing', 'Validating document...');
            const validatedData = await this.performStep1Manual(fileName, type, company, date);

            if (!validatedData) {
                throw new Error('No data available for validation');
            }
            await this.updateStepStatusManual(1, 'completed', 'Validation completed');

            // Step 2: Submit to LHDN
            console.log('📤 Starting Step 2: LHDN Submission');
            await this.updateStepStatusManual(2, 'processing', 'Submitting to LHDN...');

            // Add the original parameters to the validated data
            const submissionData = {
                ...validatedData,
                fileName,
                type,
                company,
                date,
                version
            };

            const submitted = await this.performStep2Manual(submissionData, version);

            if (!submitted) {
                throw new Error('LHDN submission failed');
            }
            await this.updateStepStatusManual(2, 'completed', 'Submission completed');

            // Step 3: Process Response
            console.log('⚙️ Starting Step 3: Processing');
            await this.updateStepStatusManual(3, 'processing', 'Processing response...');
            const processed = await this.performStep3Manual(submitted);

            if (!processed) {
                throw new Error('Response processing failed');
            }
            await this.updateStepStatusManual(3, 'completed', 'Processing completed');

            console.log('🎉 All steps completed successfully');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Close the modal
            modal.hide();

            await this.showSuccessMessageManual(fileName, version);
            // Refresh the manual submission table
            await this.refreshManualSubmissionTable();

            return true;

        } catch (error) {
            console.error('❌ Step execution failed:', error);

            // Find the current processing step and update its status to error
            const currentStep = document.querySelector('.manual-step-card.processing');
            if (currentStep) {
                const stepNumber = parseInt(currentStep.id.replace('manualStep', ''));
                console.log(`⚠️ Updating step ${stepNumber} to error state`);
                await this.updateStepStatusManual(stepNumber, 'error', 'Error occurred');
            }

            // Add delay for visual feedback
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Close the current modal
            const modalElement = document.getElementById('manualSubmissionModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }

            // Show error modal
            await this.showLHDNErrorModalManual(error);
            return false;
        }
    }



    // Helper function to update step status with animation
    async updateStepStatusManual(stepNumber, status, message) {
        console.log(`🔄 [Step ${stepNumber}] Updating status:`, { status, message });

        const step = document.getElementById(`manualStep${stepNumber}`);
        if (!step) {
            console.error(`❌ [Step ${stepNumber}] Step element not found`);
            return;
        }

        // Remove all status classes first
        step.classList.remove('processing', 'completed', 'error');
        console.log(`🎨 [Step ${stepNumber}] Removed old classes`);

        // Add the new status class
        step.classList.add(status);
        console.log(`🎨 [Step ${stepNumber}] Added new class:`, status);

        // Update status message with fade effect
        const statusEl = step.querySelector('.manual-step-status');
        if (statusEl && message) {
            console.log(`✍️ [Step ${stepNumber}] Updating message to:`, message);
            statusEl.style.opacity = '0';
            await new Promise(resolve => setTimeout(resolve, 300));
            statusEl.textContent = message;
            statusEl.style.opacity = '1';
        }

        // Update spinner visibility and icon
        const iconContainer = step.querySelector('.manual-step-icon');
        if (iconContainer) {
            const icon = iconContainer.querySelector('.fas');
            if (icon) {
                switch (status) {
                    case 'processing':
                        icon.style.display = 'none';
                        iconContainer.classList.add('spinning');
                        break;
                    case 'completed':
                        icon.style.display = 'block';
                        iconContainer.classList.remove('spinning');
                        icon.className = 'fas fa-check';
                        break;
                    case 'error':
                        icon.style.display = 'block';
                        iconContainer.classList.remove('spinning');
                        icon.className = 'fas fa-times';
                        break;
                    default:
                        icon.style.display = 'block';
                        iconContainer.classList.remove('spinning');
                        // Keep original icon class
                }
            }
        }

        // Add delay for visual feedback
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log(`✅ [Step ${stepNumber}] Status update completed`);
    }

    // Step 1: Validation (placeholder)
    async performStep1Manual(fileName, type, company, date) {
        try {
            console.log('🔍 [Step 1] Starting document validation');
            await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate validation time
            console.log('✅ [Step 1] Validation completed');
            return { fileName, type, company, date };
        } catch (error) {
            console.error('❌ [Step 1] Validation failed:', error);
            throw error;
        }
    }

    // Step 2: Submit to LHDN
    async performStep2Manual(data, version) {
        try {
            console.log('🚀 [Step 2] Starting LHDN submission with data:', data);
            await this.updateStepStatusManual(2, 'processing', 'Connecting to LHDN...');
            await this.updateStepStatusManual(2, 'processing', 'Preparing Documents...');
            console.log('📤 [Step 2] Initiating submission to LHDN');

            // Extract the required parameters from the data
            const {
                fileName,
                type,
                company,
                date
            } = data;

            // Make the API call with all required parameters
            const response = await fetch(`/api/outbound-files/${encodeURIComponent(fileName)}/submit-to-lhdn-consolidated`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    type,
                    company,
                    date,
                    version
                })
            });

            const result = await response.json();

            if (!response.ok) {
                console.error('❌ [Step 2] API error response:', result);
                await this.updateStepStatusManual(2, 'error', 'Submission failed');

                // Handle specific error types
                if (result.error?.code === 'TIN_MISMATCH') {
                    throw new Error(`TIN Mismatch: ${result.error.message}`);
                } else if (result.error?.code === 'AUTH_ERROR') {
                    throw new Error(`Authentication Error: ${result.error.message}`);
                } else {
                    throw new Error(result.error?.message || 'LHDN submission failed');
                }
            }

            console.log('✅ [Step 2] Submission successful:', result);
            await this.updateStepStatusManual(2, 'completed', 'Submission completed');
            return result;

        } catch (error) {
            console.error('❌ [Step 2] LHDN submission failed:', error);
            await this.updateStepStatusManual(2, 'error', 'Submission failed');
            throw error;
        }
    }

    // Step 3: Process response
    async performStep3Manual(response) {
        console.log('🚀 [Step 3] Starting response processing');

        try {
            // Start processing
            console.log('📝 [Step 3] Processing LHDN response');
            await this.updateStepStatusManual(3, 'processing', 'Processing response...');

            // Process response
            if (!response || !response.success) {
                console.error('❌ [Step 3] Invalid response data');
            }

            console.log('📝 [Step 3] Response data:', response ? 'Data present' : 'No data');
            if (!response) {
                console.error('❌ [Step 3] No response data to process');
                console.log('Updating step status to error...');
                await this.updateStepStatusManual(3, 'error', 'Processing failed');
                throw new Error('No response data to process');
            }

            // Simulate processing time
            console.log('⏳ [Step 3] Processing response data...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Complete successfully
            console.log('✅ [Step 3] Response processing completed');
            console.log('Updating step status to completed...');
            await this.updateStepStatusManual(3, 'completed', 'Processing completed');

            return true;
        } catch (error) {
            console.error('❌ [Step 3] Response processing failed:', error);
            console.log('Updating step status to error...');
            await this.updateStepStatusManual(3, 'error', 'Processing failed');
            throw error;
        }
    }

    // Success message for manual submissions
    async showSuccessMessageManual(fileName, version) {
        return Swal.fire({
            html: `
                <div class="manual-modal-header" style="background: linear-gradient(135deg, var(--manual-success) 0%, #047857 100%); padding: 1.5rem;">
                    <div class="manual-modal-icon" style="width: 40px; height: 40px; margin: 0 auto 0.75rem auto;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="manual-modal-title" style="font-size: 1.25rem; margin-bottom: 0.25rem;">Submission Successful!</div>
                    <div class="manual-modal-subtitle" style="font-size: 0.875rem;">Your document has been successfully submitted to LHDN</div>
                </div>
                <div class="manual-modal-body" style="padding: 1.25rem;">
                    <div class="manual-success-container" style="padding: 0.5rem 0;">
                        <div class="manual-success-icon" style="width: 60px; height: 60px; margin: 0 auto 1rem auto;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="manual-success-title" style="font-size: 1.125rem; margin-bottom: 0.5rem;">Document Submitted Successfully</div>
                        <div class="manual-success-message" style="margin-bottom: 1.25rem; font-size: 0.875rem;">
                            Your manual submission has been processed and sent to LHDN for validation.
                        </div>
                    </div>

                    <div class="manual-details-container" style="margin-top: 1rem; padding: 1rem;">
                        <div class="manual-details-header" style="margin-bottom: 1rem; padding-bottom: 0.75rem;">
                            <div class="manual-details-icon" style="width: 32px; height: 32px;">
                                <i class="fas fa-file-check"></i>
                            </div>
                            <div class="manual-details-title" style="font-size: 1rem;">Submission Details</div>
                        </div>

                        <div class="manual-detail-row">
                            <span class="manual-detail-label">File Name:</span>
                            <span class="manual-detail-value" style="font-size: 0.875rem;">${fileName}</span>
                        </div>
                        <div class="manual-detail-row">
                            <span class="manual-detail-label">LHDN Version:</span>
                            <span class="manual-detail-value">
                                <span class="manual-version-badge manual-badge-available">${version}</span>
                            </span>
                        </div>
                        <div class="manual-detail-row">
                            <span class="manual-detail-label">Submitted:</span>
                            <span class="manual-detail-value" style="font-size: 0.875rem;">${new Date().toLocaleString()}</span>
                        </div>
                    </div>

                    <div class="manual-details-container" style="margin-top: 1rem; padding: 1rem;">
                        <div class="manual-details-header" style="margin-bottom: 0.75rem; padding-bottom: 0.5rem;">
                            <div class="manual-details-icon" style="background: var(--manual-primary-light); color: var(--manual-primary); width: 32px; height: 32px;">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="manual-details-title" style="font-size: 1rem;">Next Steps</div>
                        </div>
                        <div style="color: var(--manual-text-secondary); line-height: 1.5; font-size: 0.875rem;">
                            You can track the status of your submission in the table below. The document will be processed by LHDN within 72 hours.
                        </div>
                    </div>
                </div>
            `,
            confirmButtonText: 'Close',
            width: 480,
            padding: '0',
            customClass: {
                popup: 'manual-submission-modal manual-success-modal',
                confirmButton: 'manual-btn manual-btn-primary'
            },
            buttonsStyling: false
        });
    }

    // Error modal for manual submissions
    async showLHDNErrorModalManual(error) {
        await Swal.fire({
            html: `
                <div class="manual-modal-header" style="background: linear-gradient(135deg, var(--manual-error) 0%, #b91c1c 100%); padding: 1.5rem;">
                    <div class="manual-modal-icon" style="width: 40px; height: 40px; margin: 0 auto 0.75rem auto;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="manual-modal-title" style="font-size: 1.25rem; margin-bottom: 0.25rem;">Submission Failed</div>
                    <div class="manual-modal-subtitle" style="font-size: 0.875rem;">There was an error processing your document</div>
                </div>
                <div class="manual-modal-body" style="padding: 1.25rem;">
                    <div class="manual-error-container" style="padding: 0.5rem 0;">
                        <div class="manual-error-icon" style="width: 50px; height: 50px; margin: 0 auto 0.75rem auto; font-size: 1.5rem;">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="manual-success-title" style="color: var(--manual-error); font-size: 1.125rem; margin-bottom: 0.5rem;">Submission Error</div>
                        <div class="manual-success-message" style="margin-bottom: 1rem; font-size: 0.875rem;">
                            Your document could not be submitted to LHDN. Please review the error details below.
                        </div>
                    </div>

                    <div class="manual-details-container" style="border: 2px solid var(--manual-error-light); background: var(--manual-error-light); margin-top: 1rem; padding: 1rem;">
                        <div class="manual-details-header" style="margin-bottom: 0.75rem; padding-bottom: 0.5rem;">
                            <div class="manual-details-icon" style="background: var(--manual-error-light); color: var(--manual-error); width: 32px; height: 32px;">
                                <i class="fas fa-bug"></i>
                            </div>
                            <div class="manual-details-title" style="color: var(--manual-error); font-size: 1rem;">Error Details</div>
                        </div>

                        <div class="manual-detail-row" style="padding: 0.375rem 0; font-size: 0.875rem;">
                            <span class="manual-detail-label">Error Code:</span>
                            <span class="manual-detail-value" style="color: var(--manual-error); font-weight: 700; font-size: 0.875rem;">
                                ${error.code || 'UNKNOWN'}
                            </span>
                        </div>
                        <div class="manual-detail-row" style="padding: 0.375rem 0; font-size: 0.875rem;">
                            <span class="manual-detail-label">Message:</span>
                            <span class="manual-detail-value" style="color: var(--manual-error); font-size: 0.875rem;">
                                ${error.message || 'An unknown error occurred'}
                            </span>
                        </div>
                        ${error.details && error.details.length > 0 ? `
                            <div class="manual-detail-row" style="padding: 0.375rem 0; font-size: 0.875rem;">
                                <span class="manual-detail-label">Details:</span>
                                <span class="manual-detail-value" style="color: var(--manual-error); font-size: 0.875rem;">
                                    ${error.details.map(d => `• ${d.message || d}`).join('<br>')}
                                </span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="manual-details-container" style="margin-top: 1rem; padding: 1rem;">
                        <div class="manual-details-header" style="margin-bottom: 0.75rem; padding-bottom: 0.5rem;">
                            <div class="manual-details-icon" style="background: var(--manual-warning-light); color: var(--manual-warning); width: 32px; height: 32px;">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="manual-details-title" style="font-size: 1rem;">Suggested Actions</div>
                        </div>
                        <div style="color: var(--manual-text-secondary); line-height: 1.5; font-size: 0.875rem;">
                            • Check your document format and ensure all required fields are filled<br>
                            • Verify your LHDN credentials and permissions<br>
                            • Try submitting again after a few minutes<br>
                            • Contact support if the issue persists
                        </div>
                    </div>
                </div>
            `,
            confirmButtonText: 'Close',
            width: 480,
            padding: '0',
            customClass: {
                popup: 'manual-submission-modal manual-error-modal',
                confirmButton: 'manual-btn manual-btn-primary'
            },
            buttonsStyling: false
        });
    }

}

// Add this function to handle address toggle
function toggleAddress(btn) {
    const container = btn.previousElementSibling;
    container.classList.toggle('expanded');
    btn.textContent = container.classList.contains('expanded') ? 'View less' : 'View more';
}

// Add this helper function to format address
function formatAddress(address) {
    if (!address) return null;

    const parts = [
        address.street1,
        address.street2,
        address.city,
        address.state,
        address.zip,
        address.country
    ].filter(part => part && part.trim());

    return parts.length > 0 ? parts.join(', ') : null;
}


// Add this to properly handle modal closing
document.addEventListener('DOMContentLoaded', function() {
    const viewDetailsModal = document.getElementById('viewDetailsModal');
    if (viewDetailsModal) {
        viewDetailsModal.addEventListener('hidden.bs.modal', function () {
            // Clear current invoice data
            window.invoiceTable._currentInvoice = null;

            // Reset modal content
            const loadingSpinner = document.getElementById('loadingSpinner');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'block';
            }

            const lineItemsContainer = document.getElementById('lineItemsBody');
            if (lineItemsContainer) {
                lineItemsContainer.innerHTML = '';
            }

            const modalFields = [
                'modalInvoiceNumber', 'modalInvoiceDate', 'modalInvoiceVersion', 'modalInvoiceType',
                'modalInvoiceStatus', 'modalSupplierName', 'modalSupplierTin', 'modalSupplierBrn',
                'modalSupplierSst', 'modalBuyerName', 'modalBuyerTin', 'modalBuyerBrn', 'modalBuyerAddress'
            ];

            modalFields.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = '-';
                }
            });

            const statusElement = document.getElementById('modalInvoiceStatus');
            if (statusElement) {
                statusElement.innerHTML = '';
            }

            // Reset currency indicator to default
            const currencyIndicator = document.getElementById('currency-indicator');
            if (currencyIndicator) {
                currencyIndicator.textContent = 'MYR';
                currencyIndicator.className = 'ms-2 badge bg-secondary';
                currencyIndicator.style.cursor = 'help';
                currencyIndicator.removeAttribute('data-bs-toggle');
                currencyIndicator.removeAttribute('data-bs-placement');
                currencyIndicator.removeAttribute('title');

                // Destroy tooltip if it exists
                const tooltip = bootstrap.Tooltip.getInstance(currencyIndicator);
                if (tooltip) {
                    tooltip.dispose();
                }
            }

            const submitButton = document.getElementById('submitToLhdnBtn');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="bi bi-send"></i> Submit to LHDN';
            }
        });
    }
});

window.InvoiceTableManager = InvoiceTableManager;