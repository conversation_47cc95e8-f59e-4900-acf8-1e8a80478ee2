const { Sequelize } = require('sequelize');
const dbConfig = require('../config/database.config.js');

// Create sequelize instance
const sequelize = new Sequelize(dbConfig.DB, dbConfig.USER, dbConfig.PASSWORD, {
    host: dbConfig.HOST,
    dialect: dbConfig.dialect,
    pool: dbConfig.pool,
    dialectOptions: dbConfig.dialectOptions,
    timezone: dbConfig.timezone,
    logging: console.log // Enable logging for migration
});

async function runCurrencyMigration() {
    try {
        console.log('🔄 Starting currency fields migration...');
        
        // Test database connection
        await sequelize.authenticate();
        console.log('✅ Database connection established successfully.');
        
        // Check if columns already exist
        const [results] = await sequelize.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'WP_INBOUND_STATUS' 
            AND COLUMN_NAME IN ('documentCurrency', 'currency')
        `);
        
        const existingColumns = results.map(row => row.COLUMN_NAME);
        console.log('📋 Existing currency columns:', existingColumns);
        
        // Add documentCurrency column if it doesn't exist
        if (!existingColumns.includes('documentCurrency')) {
            console.log('➕ Adding documentCurrency column...');
            await sequelize.query(`
                ALTER TABLE WP_INBOUND_STATUS 
                ADD documentCurrency VARCHAR(10) NULL DEFAULT 'MYR'
            `);
            console.log('✅ documentCurrency column added successfully');
        } else {
            console.log('ℹ️  documentCurrency column already exists');
        }
        
        // Add currency column if it doesn't exist
        if (!existingColumns.includes('currency')) {
            console.log('➕ Adding currency column...');
            await sequelize.query(`
                ALTER TABLE WP_INBOUND_STATUS 
                ADD currency VARCHAR(10) NULL DEFAULT 'MYR'
            `);
            console.log('✅ currency column added successfully');
        } else {
            console.log('ℹ️  currency column already exists');
        }
        
        // Update existing records to have default currency
        console.log('🔄 Updating existing records with default currency...');
        const [updateResult] = await sequelize.query(`
            UPDATE WP_INBOUND_STATUS 
            SET documentCurrency = 'MYR', currency = 'MYR' 
            WHERE documentCurrency IS NULL OR currency IS NULL
        `);
        console.log(`✅ Updated ${updateResult.rowsAffected || 0} records with default currency`);
        
        console.log('🎉 Currency fields migration completed successfully!');
        console.log('');
        console.log('📝 Next steps:');
        console.log('1. Restart your application');
        console.log('2. The inbound dashboard should now work without errors');
        console.log('3. Foreign currency support will be available');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        console.error('');
        console.error('🔧 Troubleshooting:');
        console.error('1. Check your database connection settings in .env');
        console.error('2. Ensure the database server is running');
        console.error('3. Verify you have ALTER TABLE permissions');
        process.exit(1);
    } finally {
        await sequelize.close();
    }
}

// Run the migration
if (require.main === module) {
    runCurrencyMigration();
}

module.exports = { runCurrencyMigration };
