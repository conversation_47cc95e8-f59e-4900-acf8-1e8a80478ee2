const express = require('express');
const router = express.Router();
const axios = require('axios');
const NodeCache = require('node-cache');
const { LoggingService, LOG_TYPES, MODULES, ACTIONS, STATUS } = require('../../services/logging.service');
const { WP_CONFIGURATION, WP_USER_REGISTRATION } = require('../../models');

// Initialize cache with 5 minutes standard TTL
const cache = new NodeCache({ stdTTL: 300 });

// Helper function to get LHDN config - reusing your existing pattern
async function getLHDNConfig() {
    const config = await WP_CONFIGURATION.findOne({
        where: {
            Type: 'LHDN',
            IsActive: 1
        },
        order: [['CreateTS', 'DESC']]
    });

    if (!config || !config.Settings) {
        throw new Error('LHDN configuration not found');
    }

    let settings = typeof config.Settings === 'string' ? JSON.parse(config.Settings) : config.Settings;
    
    const baseUrl = settings.environment === 'production' 
        ? settings.middlewareUrl || settings.middlewareUrl 
        : settings.middlewareUrl || settings.middlewareUrl;

    if (!baseUrl) {
        throw new Error('LHDN API URL not configured');
    }

    return {
        baseUrl,
        environment: settings.environment,
        timeout: parseInt(settings.timeout) || 60000
    };
}

// Fetch notifications with caching
const fetchNotifications = async (req, params = {}) => {
    try {
        if (!req.session?.accessToken) {
            throw new Error('No access token available');
        }

        const lhdnConfig = await getLHDNConfig();
        
        console.log('Fetching notifications with config:', {
            baseUrl: lhdnConfig.baseUrl,
            params: params,
            hasAccessToken: !!req.session.accessToken
        });

        // Build query parameters
        const queryParams = new URLSearchParams({
            pageSize: params.pageSize || 20,
            pageNo: params.pageNo || 1,
            ...(params.dateFrom && { dateFrom: params.dateFrom }),
            ...(params.dateTo && { dateTo: params.dateTo }),
            ...(params.type && { type: params.type }),
            ...(params.language && { language: params.language }),
            ...(params.status && { status: params.status })
        }).toString();

        const url = `${lhdnConfig.baseUrl}/notifications/taxpayer?${queryParams}`;
        console.log('Making request to:', url);

        // Set a reasonable timeout (30 seconds instead of 30ms)
        const timeout = 30000;

        const response = await axios({
            method: 'GET',
            url: url,
            headers: {
                'Authorization': `Bearer ${req.session.accessToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: timeout, // 30 seconds
            validateStatus: false, // Don't throw on any status
            // Add retry configuration
            'axios-retry': {
                retries: 3,
                retryDelay: (retryCount) => {
                    return retryCount * 1000; // Wait 1s, 2s, 3s between retries
                },
                retryCondition: (error) => {
                    return (
                        axios.isNetworkError(error) ||
                        error.code === 'ECONNABORTED' ||
                        error.response?.status === 429 ||
                        error.response?.status === 500
                    );
                }
            }
        });

        // Log response details for debugging
        console.log('Response received:', {
            status: response.status,
            statusText: response.statusText,
            hasData: !!response.data,
            dataType: typeof response.data,
            headers: response.headers
        });

        // Handle different status codes
        if (response.status === 401 || response.status === 403) {
            throw new Error('Authentication failed');
        }

        if (response.status !== 200) {
            throw new Error(`API returned status ${response.status}: ${response.data?.message || 'Unknown error'}`);
        }

        // Handle empty or invalid responses
        if (!response.data) {
            throw new Error('Empty response received from API');
        }

        // If the response is a string, try to parse it
        let data = response.data;
        if (typeof data === 'string') {
            try {
                data = JSON.parse(data);
            } catch (e) {
                console.error('Failed to parse response data:', e);
                throw new Error('Invalid JSON response from API');
            }
        }

        // Ensure we have the expected data structure
        if (!Array.isArray(data.result)) {
            console.error('Unexpected API response structure:', data);
            throw new Error('Invalid API response structure');
        }

        return {
            success: true,
            result: data.result,
            metadata: data.metadata || {}
        };

    } catch (error) {
        console.error('Error in fetchNotifications:', {
            message: error.message,
            response: error.response?.data,
            status: error.response?.status,
            config: error.config
        });

        // Return a structured error response
        return {
            success: false,
            error: {
                message: error.response?.data?.message || error.message,
                status: error.response?.status || 500,
                code: error.code
            }
        };
    }
};

// Get notifications with caching
const getCachedNotifications = async (req, params = {}) => {
    const cacheKey = `notifications_${req.session?.user?.TIN || 'default'}_${JSON.stringify(params)}`;
    const forceRefresh = req.query.forceRefresh === 'true';
    
    let data = forceRefresh ? null : cache.get(cacheKey);

    if (!data) {
        console.log('Cache miss or force refresh, fetching fresh data...');
        data = await fetchNotifications(req, params);
        
        if (data.success) {
            cache.set(cacheKey, data);
            console.log('Data cached successfully');
        } else {
            console.log('Not caching error response');
        }
    } else {
        console.log('Serving data from cache');
    }

    return data;
};

// Routes
router.get('/', async (req, res) => {
    try {
        if (!req.session?.user) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }

        console.log('Processing notifications request for user:', {
            username: req.session.user.username,
            hasAccessToken: !!req.session.accessToken
        });

        const params = {
            pageSize: parseInt(req.query.pageSize) || 20,
            pageNo: parseInt(req.query.pageNo) || 1,
            dateFrom: req.query.dateFrom,
            dateTo: req.query.dateTo,
            type: req.query.type,
            language: req.query.language,
            status: req.query.status
        };

        const data = await getCachedNotifications(req, params);

        // Log successful fetch
        await LoggingService.log({
            description: `Successfully fetched notifications`,
            username: req.session.user.username,
            userId: req.session.user.id,
            ipAddress: req.ip,
            logType: LOG_TYPES.INFO,
            module: MODULES.API,
            action: ACTIONS.READ,
            status: STATUS.SUCCESS,
            details: { count: data.result?.length || 0 }
        });

        res.json({
            success: true,
            result: data.result || [],
            metadata: data.metadata || {}
        });

    } catch (error) {
        console.error('Error in notifications route:', {
            error: error.message,
            stack: error.stack,
            user: req.session?.user?.username,
            responseData: error.response?.data
        });

        // Log the error
        await LoggingService.log({
            description: `Error fetching notifications: ${error.message}`,
            username: req.session?.user?.username || 'System',
            userId: req.session?.user?.id,
            ipAddress: req.ip,
            logType: LOG_TYPES.ERROR,
            module: MODULES.API,
            action: ACTIONS.READ,
            status: STATUS.FAILED,
            details: { 
                error: error.message,
                response: error.response?.data
            }
        });

        // Send appropriate error response
        const statusCode = error.response?.status || 500;
        const errorMessage = error.response?.data?.message || error.message || 'Internal server error';
        
        res.status(statusCode).json({
            success: false,
            message: errorMessage,
            error: {
                code: error.code || 'INTERNAL_SERVER_ERROR',
                details: error.response?.data || error.message
            }
        });
    }
});

// Get unread count
router.get('/unread-count', async (req, res) => {
    try {
        if (!req.session?.user) {
            return res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
        }

        const params = {
            status: '1', // New notifications
            pageSize: 1,
            pageNo: 1
        };

        const data = await getCachedNotifications(req, params);
        
        res.json({
            success: true,
            unreadCount: data.metadata?.totalCount || 0
        });

    } catch (error) {
        console.error('Error fetching unread count:', error);
        res.status(error.response?.status || 500).json({
            success: false,
            message: 'Failed to fetch unread count',
            error: {
                code: error.code || 'INTERNAL_SERVER_ERROR',
                details: error.response?.data || error.message
            }
        });
    }
});

// Debug route - remove in production
router.get('/debug-config', async (req, res) => {
    try {
        const config = await getLHDNConfig();
        res.json({
            success: true,
            config: {
                baseUrl: config.baseUrl,
                environment: config.environment,
                hasAccessToken: !!req.session?.accessToken,
                isAuthenticated: !!req.session?.user
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;