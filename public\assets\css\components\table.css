/* Column-specific styles */
.submituid-column a {
    color: #2563eb !important;
    font-family: 'SF Mono', SFMono-Regular, ui-monospace, Menlo, Monaco, Consolas, monospace !important;
    font-size: 0.75rem !important;
    text-decoration: none !important;
    transition: color 0.15s ease !important;
}

.submituid-column a:hover {
    color: #1d4ed8 !important;
}


/* Badge Styles */
.badge-type {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px !important;
    border-radius: 0.375rem;
    font-size: 0.80rem !important;
    font-weight: 500;
    text-align: center;
}

.badge-type.invoice {
    background: #dbeafe !important;
    color: #2563eb !important;
    padding: 4px 10px !important;
    font-size: 0.75rem !important;
}

.badge-type.invoiceType {
    background: #dbeafe !important;
    color: #2563eb !important;
    padding: 4px 10px !important;
    font-size: 0.75rem !important;
}

.badge-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px !important;
    border-radius: 0.375rem;
    font-weight: 500 !important;
    font-size: 0.75rem !important;
}

.badge-status.Valid {
    background: #dcfce7 !important;
    color: #15803d !important;
}

.badge-status.Cancelled {
    background: #fef9c3 !important;
    color: #ca8a04 !important;
}

.badge-status.Invalid {
    background: #fee2e2 !important;
    color: #dc2626 !important;
}


/* Utility Classes */
.text-center { text-align: center !important; }
.text-end { text-align: right !important; }

/* Amount Column */
.amount-column {
    font-family: 'SF Mono', SFMono-Regular, ui-monospace, Menlo, Monaco, Consolas, monospace !important;
    font-size: 0.813rem !important;
    color: #1e293b !important;
    font-weight: 500 !important;
    text-align: right !important;
    padding-right: 16px !important;
}

/* Row Hover Effect */
.table tbody tr:hover {
    background-color: #f8fafc !important;
    transition: background-color 0.15s ease-in-out !important;
}

/* Checkbox Styling */
.form-check {
    min-height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    display: flex !important;
    justify-content: center !important;
}

.form-check-input {
    width: 16px !important;
    height: 16px !important;
    margin: 0 !important;
    cursor: pointer !important;
    border: 2px solid #cbd5e1 !important;
    border-radius: 3px !important;
    transition: all 0.2s ease-in-out !important;
}

.form-check-input:checked {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1) !important;
}

/* Export Button */
#exportSelected {
    font-size: 0.813rem;
    padding: 6px 16px !important;
    background-color: #2563eb !important;
    border: none !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

#exportSelected:disabled {
    background: #94a3b8 !important;
    opacity: 0.7 !important;
}

#exportSelected .selected-count {
    font-size: 0.75rem;
    opacity: 0.9;
}

/* Spinner Animation */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

