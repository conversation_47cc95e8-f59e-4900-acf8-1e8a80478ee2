const path = require('path')
const axios = require('axios');
const CryptoJS = require('crypto-js');
const env = process.env.NODE_ENV || 'dev';
const fs = require('fs');
const forge = require('node-forge');
const jsonminify = require('jsonminify');
const crypto = require('crypto');
require('dotenv').config();
const { WP_CONFIGURATION } = require('../../models');
// Removed deprecated import: const { getTokenSession } = require('../token.service');

async function getConfig() {
  const config = await WP_CONFIGURATION.findOne({
    where: {
      Type: 'LHDN',
      IsActive: 1
    },
    order: [['CreateTS', 'DESC']]
  });

  if (!config) {
    throw new Error('LHDN configuration not found');
  }

  let settings = config.Settings;
  if (typeof settings === 'string') {
    settings = JSON.parse(settings);
  }

  return settings;
}

// DEPRECATED: Use BQE service getTokenAsTaxPayer instead
// This function is kept for backward compatibility but redirects to BQE service
async function getTokenAsIntermediary(req = null) {
  console.warn('[DEPRECATED] getTokenAsIntermediary is deprecated. Use BQE service getTokenAsTaxPayer instead.');

  if (!req) {
    throw new Error('Request context is required for token generation. Please use BQE service getTokenAsTaxPayer(req) instead.');
  }

  // Redirect to BQE service for consistent token generation
  const { getTokenAsTaxPayer } = require('../bqe/service');
  const tokenResponse = await getTokenAsTaxPayer(req);

  if (!tokenResponse?.access_token) {
    throw new Error('Failed to get authentication token from BQE service');
  }

  return tokenResponse;
}

// DEPRECATED: Use BQE service submitDocument instead
// This function is kept for backward compatibility but redirects to BQE service
async function submitDocument(docs, token) {
  console.warn('[DEPRECATED] LHDN service submitDocument is deprecated. Use BQE service submitDocument instead.');

  // Redirect to BQE service for consistent submission logic
  const { submitDocument: bqeSubmitDocument } = require('../bqe/service');
  return await bqeSubmitDocument(docs, token);
}

async function getDocumentDetails(irb_uuid, token) {
  try {
    const settings = await getConfig();
    const baseUrl = settings.environment === 'production' ?
      settings.middlewareUrl : settings.middlewareUrl;

    const response = await axios.get(
      `${baseUrl}/api/v1.0/documents/${irb_uuid}/details`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    return { status: 'success', data: response.data };
  } catch (err) {
    if (err.response?.status === 429) {
      const rateLimitReset = err.response.headers["x-rate-limit-reset"];
      if (rateLimitReset) {
        const resetTime = new Date(rateLimitReset).getTime();
        const currentTime = Date.now();
        const waitTime = resetTime - currentTime;

        console.log('=======================================================================================');
        console.log('              LHDN DocumentDetails API hitting rate limit HTTP 429                      ');
        console.log('                 Retrying for current iteration.................                       ');
        console.log(`                     (Waiting time: ${waitTime} ms)                                       `);
        console.log('=======================================================================================');

        if (waitTime > 0) {
          await new Promise(resolve => setTimeout(resolve, waitTime));
          return await getDocumentDetails(irb_uuid, token);
        }
      }
    }
    console.error(`Failed to get IRB document details for document UUID ${irb_uuid}:`, err.message);
    throw err;
  }
}

async function cancelValidDocumentBySupplier(irb_uuid, cancellation_reason, token) {
  try {
    const settings = await getConfig();
    const baseUrl = settings.environment === 'production' ?
      settings.middlewareUrl : settings.middlewareUrl;

    const payload = {
      status: 'cancelled',
      reason: cancellation_reason || 'NA'
    };

    const response = await axios.put(
      `${baseUrl}/api/v1.0/documents/state/${irb_uuid}/state`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }
    );

    return { status: 'success', data: response.data };
  } catch (err) {
    if (err.response?.status === 429) {
      const rateLimitReset = err.response.headers["x-rate-limit-reset"];
      if (rateLimitReset) {
        const resetTime = new Date(rateLimitReset).getTime();
        const currentTime = Date.now();
        const waitTime = resetTime - currentTime;

        console.log('=======================================================================================');
        console.log('              LHDN Cancel Document API hitting rate limit HTTP 429                      ');
        console.log('                 Retrying for current iteration.................                       ');
        console.log(`                     (Waiting time: ${waitTime} ms)                                       `);
        console.log('=======================================================================================');

        if (waitTime > 0) {
          await new Promise(resolve => setTimeout(resolve, waitTime));
          return await cancelValidDocumentBySupplier(irb_uuid, cancellation_reason, token);
        }
      }
    }
    console.error(`Failed to cancel document for IRB UUID ${irb_uuid}:`, err.message);
    throw err;
  }
}

function jsonToBase64(jsonObj) {
    const jsonString = JSON.stringify(jsonObj);
    const base64String = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(jsonString));
    return base64String;
}

function calculateSHA256(jsonObj) {
    const jsonString = JSON.stringify(jsonObj);
    const hash = CryptoJS.SHA256(jsonString);
    return hash.toString(CryptoJS.enc.Hex);
}

function getCertificatesHashedParams(documentJson) {
  //Note: Supply your JSON without Signature and UBLExtensions
  let jsonStringifyData = JSON.stringify(documentJson)
  const minifiedJsonData = jsonminify(jsonStringifyData);

  const sha256Hash = crypto.createHash('sha256').update(minifiedJsonData, 'utf8').digest('base64');
  const docDigest = sha256Hash;

  const privateKeyPath = path.join(__dirname, 'eInvoiceCertificates', process.env.PRIVATE_KEY_FILE_PATH);
  const certificatePath = path.join(__dirname, 'eInvoiceCertificates', process.env.PRIVATE_CERT_FILE_PATH);

  const privateKeyPem = fs.readFileSync(privateKeyPath, 'utf8');
  const certificatePem = fs.readFileSync(certificatePath, 'utf8');

  const privateKey = forge.pki.privateKeyFromPem(privateKeyPem);

  const md = forge.md.sha256.create();
  //NOTE DEV: 12/7/2024 - sign the raw json instead of hashed json
  // md.update(docDigest, 'utf8'); //disable this (no longer work)
  md.update(minifiedJsonData, 'utf8'); //enable this
  const signature = privateKey.sign(md);
  const signatureBase64 = forge.util.encode64(signature);

  // =============================================================
  // Calculate cert Digest
  // =============================================================
  const certificate = forge.pki.certificateFromPem(certificatePem);
  const derBytes = forge.asn1.toDer(forge.pki.certificateToAsn1(certificate)).getBytes();

  const sha256 = crypto.createHash('sha256').update(derBytes, 'binary').digest('base64');
  const certDigest = sha256;

  // =============================================================
  // Calculate the signed properties section digest
  // =============================================================
  let signingTime = new Date().toISOString()
  let signedProperties =
  {
    "Target": "signature",
    "SignedProperties": [
      {
        "Id": "id-xades-signed-props",
        "SignedSignatureProperties": [
            {
              "SigningTime": [
                {
                  "_": signingTime
                }
              ],
              "SigningCertificate": [
                {
                  "Cert": [
                    {
                      "CertDigest": [
                        {
                          "DigestMethod": [
                            {
                              "_": "",
                              "Algorithm": "http://www.w3.org/2001/04/xmlenc#sha256"
                            }
                          ],
                          "DigestValue": [
                            {
                              "_": certDigest
                            }
                          ]
                        }
                      ],
                      "IssuerSerial": [
                        {
                          "X509IssuerName": [
                            {
                              "_": process.env.X509IssuerName_VALUE
                            }
                          ],
                          "X509SerialNumber": [
                            {
                              "_": process.env.X509SerialNumber_VALUE
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
      }
    ]
  }

  const signedpropsString = JSON.stringify(signedProperties);
  const signedpropsHash = crypto.createHash('sha256').update(signedpropsString, 'utf8').digest('base64');

  // return ({
  //     docDigest, // docDigest
  //     signatureBase64, // sig,
  //     certDigest,
  //     signedpropsHash, // propsDigest
  //     signingTime
  // })

  let certificateJsonPortion_Signature = [
      {
          "ID": [
            {
                "_": "urn:oasis:names:specification:ubl:signature:Invoice"
            }
          ],
          "SignatureMethod": [
            {
                "_": "urn:oasis:names:specification:ubl:dsig:enveloped:xades"
            }
          ]
      }
  ]

  let certificateJsonPortion_UBLExtensions = [
    {
      "UBLExtension": [
        {
          "ExtensionURI": [
            {
              "_": "urn:oasis:names:specification:ubl:dsig:enveloped:xades"
            }
          ],
          "ExtensionContent": [
            {
              "UBLDocumentSignatures": [
                {
                  "SignatureInformation": [
                    {
                      "ID": [
                        {
                          "_": "urn:oasis:names:specification:ubl:signature:1"
                        }
                      ],
                      "ReferencedSignatureID": [
                        {
                          "_": "urn:oasis:names:specification:ubl:signature:Invoice"
                        }
                      ],
                      "Signature": [
                        {
                          "Id": "signature",
                          "SignedInfo": [
                            {
                              "SignatureMethod": [
                                {
                                  "_": "",
                                  "Algorithm": "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"
                                }
                              ],
                              "Reference": [
                                {
                                  "Id": "id-doc-signed-data",
                                  "URI": "",
                                  "DigestMethod": [
                                    {
                                      "_": "",
                                      "Algorithm": "http://www.w3.org/2001/04/xmlenc#sha256"
                                    }
                                  ],
                                  "DigestValue": [
                                    {
                                      "_": docDigest
                                    }
                                  ]
                                },
                                {
                                  "Id": "id-xades-signed-props",
                                  "Type": "http://uri.etsi.org/01903/v1.3.2#SignedProperties",
                                  "URI": "#id-xades-signed-props",
                                  "DigestMethod": [
                                    {
                                      "_": "",
                                      "Algorithm": "http://www.w3.org/2001/04/xmlenc#sha256"
                                    }
                                  ],
                                  "DigestValue": [
                                    {
                                      "_": signedpropsHash
                                    }
                                  ]
                                }
                              ]
                            }
                          ],
                          "SignatureValue": [
                            {
                              "_": signatureBase64
                            }
                          ],
                          "KeyInfo": [
                            {
                              "X509Data": [
                                {
                                  "X509Certificate": [
                                    {
                                      "_": process.env.X509Certificate_VALUE
                                    }
                                  ],
                                  "X509SubjectName": [
                                    {
                                      "_": process.env.X509SubjectName_VALUE
                                    }
                                  ],
                                  "X509IssuerSerial": [
                                    {
                                      "X509IssuerName": [
                                        {
                                          "_": process.env.X509IssuerName_VALUE
                                        }
                                      ],
                                      "X509SerialNumber": [
                                        {
                                          "_": process.env.X509SerialNumber_VALUE
                                        }
                                      ]
                                    }
                                  ]
                                }
                              ]
                            }
                          ],
                          "Object": [
                            {
                              "QualifyingProperties": [
                                {
                                  "Target": "signature",
                                  "SignedProperties": [
                                    {
                                      "Id": "id-xades-signed-props",
                                      "SignedSignatureProperties": [
                                        {
                                          "SigningTime": [
                                            {
                                              "_": signingTime
                                            }
                                          ],
                                          "SigningCertificate": [
                                            {
                                              "Cert": [
                                                {
                                                  "CertDigest": [
                                                    {
                                                      "DigestMethod": [
                                                        {
                                                          "_": "",
                                                          "Algorithm": "http://www.w3.org/2001/04/xmlenc#sha256"
                                                        }
                                                      ],
                                                      "DigestValue": [
                                                        {
                                                          "_": certDigest
                                                        }
                                                      ]
                                                    }
                                                  ],
                                                  "IssuerSerial": [
                                                    {
                                                      "X509IssuerName": [
                                                        {
                                                          "_": process.env.X509IssuerName_VALUE
                                                        }
                                                      ],
                                                      "X509SerialNumber": [
                                                        {
                                                          "_": process.env.X509SerialNumber_VALUE
                                                        }
                                                      ]
                                                    }
                                                  ]
                                                }
                                              ]
                                            }
                                          ]
                                        }
                                      ]
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]

  //Use this return value to inject back into your raw JSON Invoice[0] without Signature/UBLExtension earlier
  //Then, encode back to SHA256 and Base64 respectively for object value inside Submission Document payload.
  return ({
    certificateJsonPortion_Signature,
    certificateJsonPortion_UBLExtensions
  })

}

async function testIRBCall(data) {
  try {
    const response = await axios.post(`${process.env.PREPROD_BASE_URL}/connect/token`, httpOptions, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    if(response.status == 200) return response.data;
  } catch (err) {
    if (err.response.status == 429) {
      console.log('Current iteration hitting Rate Limit 429 of LHDN Taxpayer Token API, retrying...')
      const rateLimitReset = err.response.headers["x-rate-limit-reset"];

      if (rateLimitReset) {
        const resetTime = new Date(rateLimitReset).getTime();
        const currentTime = Date.now();
        const waitTime = resetTime - currentTime;

        if (waitTime > 0) {
          console.log('=======================================================================================');
          console.log('         (TEST API CALL) LHDN Taxpayer Token API hitting rate limit HTTP 429           ');
          console.log(`              Refetching................. (Waiting time: ${waitTime} ms)               `);
          console.log('=======================================================================================');
          await new Promise(resolve => setTimeout(resolve, waitTime));
          return await getTokenAsTaxPayer();
        }
      }
    } else {
      throw new Error(`Failed to get token: ${err.message}`);
    }
  }
}

async function validateCustomerTin(settings, tin, idType, idValue, token) {
  try {
    if (!['NRIC', 'BRN', 'PASSPORT', 'ARMY'].includes(idType)) {
      throw new Error(`Invalid ID type. Only 'NRIC', 'BRN', 'PASSPORT', 'ARMY' are allowed`);
    }

    if (!settings) {
      settings = await getConfig();
    }

    const baseUrl = settings.environment === 'production' ?
      settings.middlewareUrl : settings.middlewareUrl;

    const response = await axios.get(
      `${baseUrl}/api/v1.0/taxpayer/validate/${tin}?idType=${idType}&idValue=${idValue}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    if (response.status === 200) {
      return { status: 'success' };
    }
  } catch (err) {
    if (err.response?.status === 429) {
      const rateLimitReset = err.response.headers["x-rate-limit-reset"];
      if (rateLimitReset) {
        const resetTime = new Date(rateLimitReset).getTime();
        const currentTime = Date.now();
        const waitTime = resetTime - currentTime;

        if (waitTime > 0) {
          await new Promise(resolve => setTimeout(resolve, waitTime));
          return await validateCustomerTin(settings, tin, idType, idValue, token);
        }
      }
    }
    throw err;
  }
}

module.exports = {
    submitDocument,
    validateCustomerTin,
    getTokenAsIntermediary,
    cancelValidDocumentBySupplier,
    getDocumentDetails,
    jsonToBase64,
    calculateSHA256,
    getCertificatesHashedParams,
    testIRBCall
};

