'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WP_USER_REGISTRATION', {
      ID: { 
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      FullName: Sequelize.STRING,
      Email: Sequelize.STRING,
      Username: Sequelize.STRING,
      Password: Sequelize.STRING,
      UserType: Sequelize.STRING,
      TIN: Sequelize.STRING,
      IDType: Sequelize.STRING,
      IDValue: Sequelize.STRING,
      ClientID: Sequelize.STRING,
      ClientSecret: Sequelize.STRING,
      DigitalSignaturePath: Sequelize.STRING,
      DigitalSignatureFileName: Sequelize.STRING,
      Admin: Sequelize.INTEGER,
      CreateTS: Sequelize.DATE
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WP_USER_REGISTRATION');
  }
}; 