.submit-modal-content {
    padding: 1.5rem;
}
.submit-header {
    text-align: center;
    margin-bottom: 2rem;
}
.submit-header h5 {
    color: #405189;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}
.submit-progress {
    padding: 20px;
    text-align: center;
}
.progress-animation {
    margin-bottom: 2rem;
}
.progress-animation .spinner-border {
    width: 3rem;
    height: 3rem;
}
.progress-steps {
    display: flex;
    justify-content: space-around;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    padding: 20px 0;
}
.progress-steps .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #6c757d;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding: 10px;
    flex: 1;
    z-index: 1;
}
.progress-steps .step i {
    font-size: 24px;
    margin-bottom: 8px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}
.progress-steps .step span {
    font-size: 13px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
    opacity: 1;
}
.progress-steps .step.current {
    color: #405189;
    transform: scale(1.1);
}
.progress-steps .step.current i {
    transform: scale(1.2);
}
.progress-steps .step.done {
    color: #0ab39c;
}
.progress-steps .step.done i {
    transform: scale(1.1);
    animation: completedStep 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
.progress-steps .step.processing {
    animation: processingPulse 2s infinite;
}
.progress-steps .step.processing i {
    animation: processingRotate 2s linear infinite;
}
.progress-steps .step.error {
    color: #dc3545;
    animation: errorShake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}
.progress-steps .step:not(:last-child):after {
    content: '';
    position: absolute;
    top: 25px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: #e9ecef;
    z-index: 0;
    transition: background 0.8s ease;
}
.progress-steps .step.done:after {
    background: #0ab39c;
    animation: lineProgress 0.8s ease forwards;
}
#upload-message {
    transition: all 0.3s ease;
    margin-top: 1rem;
    font-size: 0.9rem;
    opacity: 1;
}
#upload-message.error {
    color: #dc3545;
    animation: messageShake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}
@keyframes completedStep {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1.1); }
}
@keyframes processingPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
@keyframes processingRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    20%, 60% { transform: translateX(-5px); }
    40%, 80% { transform: translateX(5px); }
}
@keyframes messageShake {
    0%, 100% { transform: translateX(0); }
    20%, 60% { transform: translateX(-3px); }
    40%, 80% { transform: translateX(3px); }
}
@keyframes lineProgress {
    0% { background: #e9ecef; clip-path: inset(0 100% 0 0); }
    100% { background: #0ab39c; clip-path: inset(0 0 0 0); }
}
.swal2-popup {
    width: 36em !important;
}
.swal2-title {
    font-size: 1.5rem !important;
}
.document-version-actions,
.swal2-actions {
    display: flex !important;
    justify-content: center !important;
    gap: 1rem !important;
    margin-top: 2rem !important;
    padding: 0 !important;
}
.btn-proceed,
.swal2-confirm {
    background-color: #405189 !important;
    color: #fff !important;
    border: none !important;
    padding: 0.5rem 1.5rem !important;
    border-radius: 0.25rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    box-shadow: none !important;
    margin: 0 !important;
}
.btn-back,
.swal2-cancel {
    background-color: #f3f6f9 !important;
    color: #405189 !important;
    border: 1px solid #e9ebec !important;
    padding: 0.5rem 1.5rem !important;
    border-radius: 0.25rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    box-shadow: none !important;
    margin: 0 !important;
}
.btn-proceed:hover,
.swal2-confirm:hover {
    background-color: #3a4a7e !important;
}
.btn-back:hover,
.swal2-cancel:hover {
    background-color: #e9ecef !important;
}
.btn-proceed:focus,
.swal2-confirm:focus,
.btn-back:focus,
.swal2-cancel:focus {
    box-shadow: none !important;
    outline: none !important;
}
.validation-error-content {
    padding: 1.5rem;
}
.error-summary {
    color: #405189;
    font-size: 1rem;
    margin-bottom: 1.5rem;
    text-align: center;
    font-weight: 500;
}
.validation-error-list {
    background-color: #fff;
    border-radius: 0.5rem;
}
.validation-error-item {
    background: white;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.1);
}
.validation-error-item:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transform: translateY(-1px);
}
.validation-error-item:last-child {
    margin-bottom: 0;
}
.error-title {
    color: #dc3545;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}
.error-description {
    color: #212529;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    padding-left: 1.75rem;
}
.error-help {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}
.error-help i {
    color: #ffc107;
    margin-right: 0.5rem;
}
.error-footer {
    margin-top: 1.5rem;
    text-align: center;
}
.error-tip {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.9rem;
    color: #405189;
    display: inline-flex;
    align-items: center;
}
.error-tip i {
    color: #405189;
}
/* Animation delays for error items */
.validation-error-item:nth-child(1) { animation-delay: 0.1s; }
.validation-error-item:nth-child(2) { animation-delay: 0.2s; }
.validation-error-item:nth-child(3) { animation-delay: 0.3s; }
.validation-error-item:nth-child(4) { animation-delay: 0.4s; }
.validation-error-item:nth-child(5) { animation-delay: 0.5s; }
@keyframes slideInError {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* LHDN Validation Error Modal Styles */
.lhdn-validation__modal {
    max-width: 600px !important;
}
.lhdn-validation__icon {
    color: #dc3545 !important;
    font-size: 2rem !important;
    margin-bottom: 1rem !important;
}
.lhdn-validation__title {
    color: #dc3545 !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}
.error-info-header {
    background-color: #dc35451a;
    color: #dc3545;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}
.error-path {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    margin-top: 0.75rem;
    font-family: 'Courier New', monospace;
}
.error-path code {
    color: #405189;
    word-break: break-all;
    margin-left: 0.5rem;
    flex: 1;
}
.error-path i {
    color: #405189;
    font-size: 1rem;
}

.validation-error-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(220, 53, 69, 0.05);
    border-radius: 0.5rem;
}

.error-section-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #dc3545;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(220, 53, 69, 0.2);
}

.error-section-header i {
    margin-right: 0.5rem;
    font-size: 1.1em;
}

.error-code {
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    background: rgba(220, 53, 69, 0.1);
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
    color: #dc3545;
}

.validation-error-item {
    background: white;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.1);
}

.validation-error-item:last-child {
    margin-bottom: 0;
}

.error-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #dc3545;
    margin-bottom: 0.5rem;
}

.error-description {
    color: #6c757d;
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.error-help {
    display: flex;
    align-items: center;
    color: #0d6efd;
    font-size: 0.9em;
    margin-bottom: 0.5rem;
}

.error-path {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.85em;
    color: #6c757d;
}

.error-path code {
    font-family: 'Courier New', monospace;
    color: #d63384;
}

.error-path i {
    margin-right: 0.5rem;
    color: #6c757d;
}

.error-footer {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.error-tip {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 0.9em;
}

.error-tip i {
    margin-right: 0.5rem;
    color: #0d6efd;
}

/* Animation for error items */
@keyframes slideInError {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.validation-error-item {
    animation: slideInError 0.3s ease-out forwards;
}

.validation-error-item:nth-child(2) {
    animation-delay: 0.1s;
}

.validation-error-item:nth-child(3) {
    animation-delay: 0.2s;
}

.validation-error-item:nth-child(4) {
    animation-delay: 0.3s;
}

.validation-error-item:nth-child(5) {
    animation-delay: 0.4s;
}