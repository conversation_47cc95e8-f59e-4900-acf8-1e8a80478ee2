module.exports = (sequelize, DataTypes) => {
  const PortalSettings = sequelize.define('PortalSettings', {
    ID: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    UserID: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    Settings: {
      type: DataTypes.TEXT,
      allowNull: false,
      get() {
        const rawValue = this.getDataValue('Settings');
        return rawValue ? JSON.parse(rawValue) : null;
      },
      set(value) {
        this.setDataValue('Settings', JSON.stringify(value));
      }
    },
    CreateTS: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdateTS: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'PortalSettings',
    timestamps: false
  });

  return PortalSettings;
}; 