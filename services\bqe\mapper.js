const fs = require('fs');
const path = require('path');
const { getCertificatesHashedParams } = require('./service');
/**
 * Logger configuration for mapping process
 */
const createLogger = () => {
  const logs = {
    steps: [],
    mappings: [],
    errors: []
  };

  const logStep = (step, data) => {
    logs.steps.push({
      timestamp: new Date().toISOString(),
      step,
      data
    });
  };

  const logMapping = (section, input, output) => {
    logs.mappings.push({
      timestamp: new Date().toISOString(),
      section,
      input,
      output
    });
  };

  const logError = (error, context) => {
    logs.errors.push({
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      context
    });
  };

  const writeLogs = (invoiceNo, lhdnFormat) => {
    try {
      const logsDir = path.join(process.cwd(), 'logs', 'lhdn');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }

      // Sanitize invoice number for filenames
      const safeInvoiceNo = invoiceNo.replace(/[\/\\:*?"<>|]/g, '_');

      // Write processing logs
      const processLogFileName = `lhdn_process_${safeInvoiceNo}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      const processLogPath = path.join(logsDir, processLogFileName);
      fs.writeFileSync(processLogPath, JSON.stringify(logs, null, 2));
      console.log(`[INFO] LHDN Processing logs written to: ${processLogPath}`);

      // Write LHDN format JSON
      const lhdnFileName = `lhdn_output_${safeInvoiceNo}_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      const lhdnPath = path.join(logsDir, lhdnFileName);
      fs.writeFileSync(lhdnPath, JSON.stringify(lhdnFormat, null, 2));
      console.log(`[INFO] LHDN Output JSON written to: ${lhdnPath}`);
    } catch (error) {
      console.error('[ERROR] Failed to write LHDN logs:', error);
    }
  };

  return {
    logStep,
    logMapping,
    logError,
    writeLogs,
    getLogs: () => logs
  };
};

// Helper functions
// Removed unused convertToBoolean function

// Helper function to convert country names to ISO 3166-1 alpha-3 codes
const convertToISO3166 = (country) => {
  if (!country) return "MYS"; // Default to Malaysia

  // If it's already a 3-letter code, return it uppercase
  if (country.length === 3) {
    return country.toUpperCase();
  }

  // Common country mappings
  const countryMap = {
    "malaysia": "MYS",
    "singapore": "SGP",
    "indonesia": "IDN",
    "thailand": "THA",
    "philippines": "PHL",
    "vietnam": "VNM",
    "brunei": "BRN",
    "myanmar": "MMR",
    "cambodia": "KHM",
    "laos": "LAO",
    "united states": "USA",
    "united kingdom": "GBR",
    "australia": "AUS",
    "china": "CHN",
    "japan": "JPN",
    "korea": "KOR",
    "india": "IND"
  };

  // Try to match the country name
  const normalizedCountry = country.toLowerCase().trim();
  return countryMap[normalizedCountry] || "MYS"; // Default to Malaysia if not found
};

const wrapValue = (value, currencyID = null) => {
  // For currency amounts, keep as numbers or return undefined if invalid
  if (currencyID) {
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      return [{
        "_": numValue,
        "currencyID": currencyID
      }];
    }
    return undefined;
  }

  // For non-currency fields, convert null/undefined to empty string
  if (value === null || value === undefined || value === '') {
    return [{
      "_": ""
    }];
  }

  // Convert everything else to string
  return [{
    "_": String(value)
  }];
};

// Removed unused wrapBoolean function

const wrapNumericValue = (value) => {
  if (value === null || value === undefined || value === '') {
    return undefined;
  }
  const numValue = Number(value);
  return isNaN(numValue) ? undefined : [{
    "_": numValue
  }];
};

// Removed unused formatDateTime function

// Helper function to create address lines from a string - used in mapPartyAddress
// Removed unused function

// Removed unused mapAllowanceCharges function

// Removed unused mapCommodityClassifications function

const mapPartyIdentifications = (identifications = []) => {
  const requiredTypes = ['TIN', 'BRN', 'SST', 'TTX'];

  const idMap = identifications.reduce((acc, id) => {
    if (id && id.schemeId) {
      acc[id.schemeId] = id.id || "";
    }
    return acc;
  }, {});

  return requiredTypes.map(schemeId => ({
    "ID": [{
      "_": idMap[schemeId] || "",
      "schemeID": schemeId,
    }]
  }));
};

const mapPartyAddress = (address) => {
  // Ensure we have valid address data
  if (!address) {
    address = {
      line1: 'NA',
      city: 'NA',
      state: '14',
      postcode: 'NA',
      country: 'MYS'
    };
  }

  // Get address lines from the address object
  // Make sure we capture the full address without truncation
  const addressLine1 = address.line1 || 'NA';
  const addressLine2 = address.line2 || '';
  const formattedAddress = address.formattedAddress || '';

  // Create address lines array
  const addressLines = [];

  // First try to use the full address line 1
  if (addressLine1 && addressLine1 !== 'NA') {
    addressLines.push({
      "Line": [{ "_": addressLine1 }]
    });
  } else {
    // Always include at least one address line
    addressLines.push({
      "Line": [{ "_": "NA" }]
    });
  }

  // Add address line 2 if it exists
  if (addressLine2 && addressLine2.trim() !== '') {
    addressLines.push({
      "Line": [{ "_": addressLine2 }]
    });
  }

  // If we have a formatted address but no address lines, use the formatted address
  if (addressLines.length === 0 && formattedAddress && formattedAddress !== 'NA') {
    // Split the formatted address by commas and add each part as a line
    const parts = formattedAddress.split(',').map(part => part.trim()).filter(Boolean);
    if (parts.length > 0) {
      addressLines.push({
        "Line": [{ "_": parts[0] }]
      });

      // Add remaining parts as additional lines if needed
      if (parts.length > 1) {
        addressLines.push({
          "Line": [{ "_": parts.slice(1).join(', ') }]
        });
      }
    }
  }

  // Convert country code to MYS (ISO 3166-1 alpha-3 code for Malaysia)
  // LHDN requires the ISO 3166-1 alpha-3 code, not the full country name
  let countryCode = "MYS"; // Default to Malaysia

  // If address.country is provided, ensure it's in the correct format
  if (address.country) {
    // If it's "Malaysia", convert to "MYS"
    if (address.country === "Malaysia") {
      countryCode = "MYS";
    }
    // If it's already a 3-letter code, use it
    else if (address.country.length === 3) {
      countryCode = address.country.toUpperCase();
    }
    // Otherwise, default to MYS
  }

  // Clean postcode: remove dashes, spaces, and any non-alphanumeric characters, limit to 5 characters for LHDN compliance
  const originalPostcode = address.postcode || 'NA';
  const cleanPostcode = originalPostcode.replace(/[-\s\W]/g, '').substring(0, 5);

  // Log if postcode was modified for debugging
  if (originalPostcode !== cleanPostcode && originalPostcode.length > 0 && originalPostcode !== 'NA') {
    console.log(`Mapper - Postcode cleaned: "${originalPostcode}" -> "${cleanPostcode}"`);
  }

  return {
    "CityName": wrapValue(address.city || 'NA'),
    "PostalZone": wrapValue(cleanPostcode),
    "CountrySubentityCode": wrapValue(address.state || '14'),
    "AddressLine": addressLines,
    "Country": [{
      "IdentificationCode": [{
        "_": countryCode,
        "listID": "ISO3166-1",
        "listAgencyID": "6"
      }]
    }]
  };
};

// Removed unused DEFAULT_VALUES constant

// Removed unused mapTaxScheme function

// Removed unused mapTaxCategory function

// Removed unused mapLineItem function

// Removed unused mapInvoiceLines function

// helper function  before mapToLHDNFormat
const cleanObject = (obj) => {
    if (!obj) return obj;

    if (Array.isArray(obj)) {
        return obj
            .map(item => cleanObject(item))
            .filter(item => item !== null && item !== undefined);
    }

    if (typeof obj === 'object') {
        const cleaned = Object.entries(obj)
            .reduce((acc, [key, value]) => {
                const cleanedValue = cleanObject(value);
                if (cleanedValue !== null && cleanedValue !== undefined) {
                    // Handle empty arrays
                    if (Array.isArray(cleanedValue) && cleanedValue.length === 0) {
                        return acc;
                    }
                    // Handle empty objects
                    if (typeof cleanedValue === 'object' &&
                        !Array.isArray(cleanedValue) &&
                        Object.keys(cleanedValue).length === 0) {
                        return acc;
                    }
                    acc[key] = cleanedValue;
                }
                return acc;
            }, {});

        return Object.keys(cleaned).length ? cleaned : undefined;
    }

    return obj;
};

// helper functions as well for better data cleaning
const isEmptyValue = (value) => {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string' && value.trim() === '') return true;
    if (Array.isArray(value) && value.length === 0) return true;
    if (typeof value === 'object' && Object.keys(value).length === 0) return true;
    return false;
};

const cleanEmptyFields = (obj) => {
    if (!obj) return obj;

    if (Array.isArray(obj)) {
        return obj
            .map(item => cleanEmptyFields(item))
            .filter(item => !isEmptyValue(item));
    }

    if (typeof obj === 'object') {
        const cleaned = {};
        for (const [key, value] of Object.entries(obj)) {
            const cleanedValue = cleanEmptyFields(value);
            if (!isEmptyValue(cleanedValue)) {
                cleaned[key] = cleanedValue;
            }
        }
        return cleaned;
    }

    return obj;
};

const getFormattedDateTime = () => {
  const currentDate = new Date();
  return {
    date: currentDate.toISOString().split('T')[0],
    time: currentDate.toISOString().split('T')[1].split('.')[0] + 'Z'
  };
};

/**
 * Maps processed BQE data to LHDN format
 * IMPORTANT: This function should ONLY use processed data from dataProcessor.js
 * NO raw invoice data access or calculations should be performed here
 */
const mapBQEToLHDNFormat = (bqeData, version) => {
  const logger = createLogger();

  if (!bqeData || !bqeData.invoice) {
    const error = new Error('No processed invoice data provided');
    logger.logError(error, { bqeData });
    throw error;
  }

  try {
    const { date, time } = getFormattedDateTime();
    // Use ONLY processed data from dataProcessor.js - NO raw invoice access
    const invoice = bqeData.invoice || {};  // Use processed invoice data
    const supplier = bqeData.supplier || {};
    const projTitle = bqeData.projTitle || '';
    const buyer = bqeData.buyer || {};
    const attention = bqeData.attention || '';

    // Use tax_info from the processed data
    const taxInfo = bqeData.tax_info || {
      taxRate: 0,
      taxType: '06',
      taxTypeDescription: 'Not Applicable',
      taxExemption: 'Not Applicable',
      isExempted: false,
      taxableAmount: 0,
      taxInclusiveAmount: 0,
      taxAmount: 0
    };

    // Get payment information from processed data only
    const projectCustomFields = bqeData.projectCustomFields || [];
    const companyCustomFields = bqeData.company?.customFields || [];

    // Check Use Remit Acct 2 field from project custom fields - exact case match
    const useRemitAcct2Field = projectCustomFields.find(f => f.label === 'Use Remit Acct 2');
    const useAltAccount = useRemitAcct2Field?.value === 'YES' || useRemitAcct2Field?.description === 'YES';

    // Log for debugging
    console.log('Mapper - Project custom fields:', projectCustomFields);
    console.log('Mapper - USE PAYMENT ACCT 2:', useAltAccount);
    console.log('Mapper - Company custom fields:', companyCustomFields);

    // Get payment info based on the flag
    const paymentField = companyCustomFields.find(f => {
        // Log each field for debugging
        console.log('Mapper - Checking field:', f.label);
        return f.label === (useAltAccount ? 'Remit Payment To (ALT)' : 'Remit Payment To');
    });

    // Log selected payment info
    console.log('Mapper - Selected payment field:', paymentField);
    const paymentInfo = paymentField?.value || '';
    console.log('Mapper - Selected payment info:', paymentInfo);
    console.log('Mapper - Use alternate account:', useAltAccount);

    // Split the payment information into lines
    const paymentInfoArray = paymentInfo ? paymentInfo.split('*').map(line => line.trim()) : [];

    // Use payment means code from processed data only
    const paymentMeansCode = invoice.paymentMeansCode || "08";

    logger.logStep('Starting BQE to LHDN mapping', { version });

    // Use processed invoice dates only
    const invoiceFrom = invoice.invoiceFrom || date;
    const invoiceTo = invoice.invoiceTo || date;

    // Ensure version is properly formatted
    let versionValue;
    if (typeof version === 'object' && version !== null) {
      versionValue = version.value || "1.0";
    } else {
      versionValue = version || "1.0";
    }

    // Get tax information directly from tax_info
    // Use the tax type from tax_info directly
    const taxType = taxInfo.taxType || '02';
    const taxRate = parseFloat(taxInfo.taxRate || 0);
    const isTaxExempt = taxType === 'E' || taxType === '06';

    // Get currency information from currency_info (set by dataProcessor.js)
    const currency = bqeData.currency_info?.currency || bqeData.currency_info?.currencyCode || 'MYR';
    const isForeignCurrency = bqeData.currency_info?.isForeignCurrency || false;
    const exchangeRate = parseFloat(bqeData.currency_info?.exchangeRate) || 1.0;

    // Use ONLY the values from processed invoice data - no additional calculations
    // For foreign currency, these are already in USD; for local currency, these are in MYR
    const invoiceAmount = invoice.amount?.total || taxInfo.taxInclusiveAmount;  // Total amount (with tax) - already in correct currency
    const taxableAmount = invoice.amount?.taxable || taxInfo.taxableAmount;     // Amount excluding tax - already in correct currency
    const taxAmount = invoice.amount?.tax || taxInfo.taxAmount;                 // Tax amount only - already in correct currency

    // For foreign currency, use MYR values from processed invoice data
    const invoiceAmountMYR = invoice.amountMYR?.total || taxInfo.taxInclusiveAmountMYR || invoiceAmount;
    const taxableAmountMYR = invoice.amountMYR?.taxable || taxInfo.taxableAmountMYR || taxableAmount;
    const taxAmountMYR = invoice.amountMYR?.tax || taxInfo.taxAmountMYR || taxAmount;

    // Log the values for debugging
    console.log('Mapper.js - Currency information received:', {
        currencyInfo: bqeData.currency_info,
        invoiceCurrency: invoice.currency,
        finalCurrency: currency,
        isForeignCurrency,
        exchangeRate
    });

    console.log('Monetary values mapped to LHDN format:', {
        invoiceAmount,
        taxableAmount,
        taxAmount,
        taxRate,
        isTaxExempt,
        taxType,
        currency,
        isForeignCurrency,
        exchangeRate,
        invoiceAmountMYR,
        taxableAmountMYR,
        taxAmountMYR,
        taxTypeDescription: taxInfo.taxTypeDescription,
        isExempted: taxInfo.isExempted,
        taxExemption: taxInfo.taxExemption
    });

    // Tax category following LHDN format - use actual tax rate from data
    const taxCategory = {
        "ID": wrapValue(taxType),
        "Percent": wrapNumericValue(taxInfo.originalTaxRate || 0),
        "TaxExemptionReason": isTaxExempt ? wrapValue(
            taxInfo.taxExemption || "Not Applicable"
        ) : undefined,
        "TaxScheme": [{
            "ID": [{
                "_": "OTH",
                "schemeID": "UN/ECE 5153",
                "schemeAgencyID": "6"
            }]
        }]
    };

    // Use processed data only - no raw data access
    const documentType = invoice.documentType || "Project Title";
    const attentionType = "Attention";
    const attentionDescription = attention ||
                            (buyer.firstName && buyer.lastName ?
                                `${buyer.firstName} ${buyer.lastName}`.trim() :
                                buyer.name) ||
                            "";

    // Use processed invoice message only
    const invoiceMessage = invoice.messageOnInvoice || "";

    // Use tax amount directly from dataProcessor.js - no additional calculations
    // For foreign currency, tax is always in MYR; for local currency, tax is in local currency
    const finalTaxAmount = isForeignCurrency ? taxAmountMYR : taxAmount;

    const taxTotal = [{
        "TaxAmount": [
            {
                "_": finalTaxAmount,
                "currencyID": "MYR"  // Tax is always in MYR according to LHDN sample
            }
        ],
        "TaxSubtotal": [{
            "TaxableAmount": [
                {
                    "_": taxableAmount,
                    "currencyID": currency  // Taxable amount in document currency
                }
            ],
            "TaxAmount": [
                {
                    "_": finalTaxAmount,
                    "currencyID": "MYR"  // Tax amount is always in MYR
                }
            ],
            "TaxCategory": [taxCategory]
        }]
    }];

    // Update LegalMonetaryTotal with correct monetary values
    // Note: ExchangeRate should NOT be in LegalMonetaryTotal according to LHDN schema
    const legalMonetaryTotal = [{
        "LineExtensionAmount": wrapValue(taxableAmount, currency),
        "TaxExclusiveAmount": wrapValue(taxableAmount, currency),
        "TaxInclusiveAmount": wrapValue(invoiceAmount, currency),
        "PayableAmount": wrapValue(invoiceAmount, currency)
    }];

    // Map line items with consistent tax handling
    const finalLineItems = bqeData.line_items && bqeData.line_items.length > 0
      ? bqeData.line_items.map((item, index) => {
          // Use line item amounts directly from dataProcessor.js - no additional calculations
          const lineItemAmount = item.amount || 0;  // Already tax-exclusive from dataProcessor
          const lineItemTaxAmount = item.tax?.types?.[0]?.amount || 0;  // Tax amount from dataProcessor
          const lineItemTaxAmountMYR = isForeignCurrency ? (item.tax?.amountMYR || 0) : lineItemTaxAmount;

          // Create the line item object
          const lineItem = {
            "ID": wrapValue(index + 1),
            "InvoicedQuantity": [{
              "_": item.quantity || 1,
              "unitCode": item.unitCode || "EA"
            }],
            "LineExtensionAmount": wrapValue(lineItemAmount, currency),
            "TaxTotal": [{
              "TaxAmount": wrapValue(lineItemTaxAmountMYR, "MYR"),  // Tax amount always in MYR
              "TaxSubtotal": [{
                "TaxableAmount": wrapValue(lineItemAmount, currency),  // Taxable amount in document currency
                "TaxAmount": wrapValue(lineItemTaxAmountMYR, "MYR"),  // Tax amount always in MYR
                "TaxCategory": [taxCategory]
              }]
            }],
            "Item": [{
              "CommodityClassification": [{
                "ItemClassificationCode": [{
                  "_": item.classifications?.invoice ||
                       item.classificationCode ||
                       "022",
                  "listID": "CLASS"
                }]
              }],
              "Description": wrapValue(item.description || "NA"),
              "OriginCountry": [{
                "IdentificationCode": [{
                  "_": convertToISO3166(item.originCountry) || "MYS",
                  "listID": "ISO3166-1",
                  "listAgencyID": "6"
                }]
              }]
            }],
            "Price": [{
              "PriceAmount": wrapValue(lineItemAmount, currency)
            }],
            "ItemPriceExtension": [{
              "Amount": [{
                "_": lineItemAmount,
                "currencyID": currency
              }]
            }]
          };

          // Note: No need for duplicate TaxTotal entries
          // Tax amounts are already in MYR and taxable amounts in document currency

          return lineItem;
        })
      : [{
          "ID": wrapValue("1"),
          "InvoicedQuantity": [{
            "_": 1,
            "unitCode": invoice.unitCode || "EA"
          }],
          "LineExtensionAmount": wrapValue(taxableAmount, currency),
          "TaxTotal": [{
            "TaxAmount": wrapValue(finalTaxAmount, "MYR"),  // Tax amount always in MYR
            "TaxSubtotal": [{
              "TaxableAmount": wrapValue(taxableAmount, currency),  // Taxable amount in document currency
              "TaxAmount": wrapValue(finalTaxAmount, "MYR"),  // Tax amount always in MYR
              "TaxCategory": [taxCategory]
            }]
          }],
          "Item": [{
            "CommodityClassification": [{
              "ItemClassificationCode": [{
                "_": invoice.classificationCode || "022",
                "listID": "CLASS"
              }]
            }],
            "Description": wrapValue(invoice.description || "NA"),
            "OriginCountry": [{
              "IdentificationCode": [{
                "_": convertToISO3166(invoice.originCountry || supplier.countryCode) || "MYS",
                "listID": "ISO3166-1",
                "listAgencyID": "6"
              }]
            }]
          }],
          "Price": [{
            "PriceAmount": wrapValue(taxableAmount, currency)
          }],
          "ItemPriceExtension": [{
            "Amount": [{
              "_": taxableAmount,
              "currencyID": currency
            }]
          }]
        }];

    // Note: No need for duplicate TaxTotal entries in default line item
    // Tax amounts are already in MYR and taxable amounts in document currency

    const lhdnFormat = {
      "_D": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
      "_A": "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
      "_B": "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
      "Invoice": [{
        "ID": wrapValue(invoice.invoiceNumber || invoice.number || invoice.id || ""),
        "IssueDate": [{
          "_": date
        }],
        "IssueTime": [{
          "_": time
        }],
        "InvoiceTypeCode": [{
          "_": invoice.invoiceTypeCode || '01',
          "listVersionID": versionValue
        }],
        "AdditionalDocumentReference": [{
          "ID": wrapValue(invoice.number || invoice.invoiceNumber || ""),
          "DocumentType": wrapValue(documentType),
          "DocumentDescription": wrapValue(projTitle),
        },{
          "ID": wrapValue(invoice.number || invoice.invoiceNumber || ""),
          "DocumentType": wrapValue(attentionType),
          "DocumentDescription": wrapValue(attentionDescription),
        }],
        "DocumentCurrencyCode": wrapValue(currency),
        "TaxCurrencyCode": wrapValue(isForeignCurrency ? "MYR" : currency),
        "InvoicePeriod": [{
          "StartDate": wrapValue(invoiceFrom),
          "EndDate": wrapValue(invoiceTo),
          "Description": wrapValue(invoice.periodDescription || "")
        }],
        "AccountingSupplierParty": [{
          "AdditionalAccountID": [{
            "_": supplier.certExId || "",
            "schemeAgencyName": "CertEX"
          }],
          "Party": [{
            "IndustryClassificationCode": [{
              "_": supplier.msicCode || "",
              "name": supplier.businessActivity || ""
            }],
            "PartyIdentification": mapPartyIdentifications([
              { id: supplier.tin, schemeId: 'TIN' },
              { id: supplier.registrationNumber, schemeId: 'BRN' },
              { id: supplier.sstId, schemeId: 'SST' },
              { id: supplier.ttxId, schemeId: 'TTX' }
            ]),
            "PostalAddress": [mapPartyAddress(supplier.address)],
            "PartyLegalEntity": [{
              "RegistrationName": wrapValue(supplier.name || "")
            }],
            "Contact": [{
              "Telephone": wrapValue(supplier.phone || ""),
              "ElectronicMail": wrapValue(supplier.email || "")
            }]
          }]
        }],
        "AccountingCustomerParty": [{
          "Party": [{
            "PartyIdentification": mapPartyIdentifications([
              { id: buyer.tin, schemeId: 'TIN' },
              { id: buyer.registrationNumber, schemeId: 'BRN' },
              { id: buyer.sstId, schemeId: 'SST' },
              { id: buyer.ttxId, schemeId: 'TTX' }
            ]),
            "PostalAddress": [mapPartyAddress(buyer.address)],
            "PartyLegalEntity": [{
              "RegistrationName": wrapValue(buyer.name || "")
            }],
            "Contact": [{
              "Telephone": wrapValue(buyer.phone || ""),
              "ElectronicMail": wrapValue(buyer.email || "")
            }]
          }]
        }],
        "PaymentMeans": [{
          "PaymentMeansCode": wrapValue(paymentMeansCode),
          "PayeeFinancialAccount": [{
            "ID": wrapValue(paymentInfoArray.join('\n'))
          }]
        }],
        "PaymentTerms": [{
          "Note": wrapValue(invoiceMessage)
        }],
        // Add TaxExchangeRate for foreign currency invoices (conditionally)
        ...(isForeignCurrency && {
          "TaxExchangeRate": [{
            "SourceCurrencyCode": wrapValue(currency),
            "TargetCurrencyCode": wrapValue("MYR"),
            "CalculationRate": [{
              "_": exchangeRate  // Direct numeric value as per LHDN sample
            }]
          }]
        }),
        "TaxTotal": taxTotal,
        "LegalMonetaryTotal": legalMonetaryTotal,
        "InvoiceLine": finalLineItems
      }],
    };

    // Clean up the format by removing undefined values
    const cleanedFormat = cleanObject(lhdnFormat);

    // Write logs
    logger.writeLogs(invoice.number || invoice.invoiceNumber || "unknown", cleanedFormat);
    console.log('Mapped to LHDN format:', cleanedFormat);

    return cleanedFormat;
  } catch (error) {
    logger.logError(error, { bqeData });
    console.error('Error mapping BQE to LHDN format:', error);
    throw error;
  }
};


module.exports = {
  mapBQEToLHDNFormat,
  cleanObject,
  cleanEmptyFields,
  isEmptyValue
};