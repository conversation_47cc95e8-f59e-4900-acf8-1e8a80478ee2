const { WP_USER_REGISTRATION } = require('../models');

module.exports = async (req, res, next) => {
  try {
    // Check if user is authenticated
    if (!req.session?.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Get user details
    const user = await WP_USER_REGISTRATION.findOne({
      where: { Username: req.session.user.username }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if user is admin (assuming Role field exists in your user table)
    if (user.Role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    // Add user object to request for use in routes
    req.user = user;
    next();
  } catch (error) {
    console.error('Error in admin middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}; 