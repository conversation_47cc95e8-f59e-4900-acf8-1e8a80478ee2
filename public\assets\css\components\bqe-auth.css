/* BQE Authorization Button Styles */
.auth-btn {
    background-color: #405189;
    color: #fff;
    border: none;
    padding: 0.5rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.auth-btn:hover {
    background-color: #0a3d8a;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(10, 61, 138, 0.2);
}

.auth-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(10, 61, 138, 0.1);
}

.auth-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(64, 81, 137, 0.3);
}

/* Disconnected state */
.auth-btn.disconnected {
    background-color: #dc3545;
    color: #fff;
}

.auth-btn.disconnected:hover {
    background-color: #c82333;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

/* Connection status indicator */
.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.connection-status.connected {
    background-color: rgba(255, 255, 255, 0.1);
    color: #28a745;
}

.connection-status.not-connected {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.connection-status i {
    font-size: 1rem;
}

/* Authorization tooltip */
.auth-tooltip {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    width: 250px;
    z-index: 10;
    margin-top: 0.5rem;
    display: none;
}

.auth-tooltip::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #fff;
}

.auth-tooltip-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #405189;
}

.auth-tooltip-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.75rem;
}

.auth-tooltip-actions {
    display: flex;
    justify-content: flex-end;
}

.auth-tooltip-btn {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    background-color: #405189;
    color: #fff;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.auth-tooltip-btn:hover {
    background-color: #0a3d8a;
}
