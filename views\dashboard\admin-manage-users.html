{% extends 'layout.html' %}

{% block head %}
<title>User Management - eInvoice Portal</title>
<link href="/assets/css/pages/admin/manage-user-settings.css" rel="stylesheet">
<link href="/assets/css/pages/profile.css" rel="stylesheet">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<style>
body{
    background: #f8fafc;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-2 px-md-2 px-lg-2">
  <!-- Header -->
    <div class="header-left">
      <div class="header-icon">
        <i class="fas fa-users-cog"></i>
      </div>
      <div class="header-content">
        <h2>User Management</h2>
        <p>Add, edit, and manage user accounts and their access levels</p>
      </div>
    </div>
    <div class="header-info">
      <div class="time">
        <i class="far fa-clock"></i>
        <span id="currentTime">12:23:09 AM</span>
      </div>
      <div class="date">
        <i class="far fa-calendar"></i>
        <span id="currentDate">Thursday, January 16, 2025</span>
      </div>
    </div>


  <!-- Statistics Cards -->
  <div class="stats-container">
    <div class="stats-card outbound">
      <div class="stats-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="stats-info">
        <span class="stats-label">Total Users</span>
        <h3 id="totalUsers">0</h3>
        <span class="stats-type">Total</span>
      </div>
    </div>

    <div class="stats-card inbound">
      <div class="stats-icon">
        <i class="fas fa-user-shield"></i>
      </div>
      <div class="stats-info">
        <span class="stats-label">Admin Users</span>
        <h3 id="adminUsers">0</h3>
        <span class="stats-type">Admin</span>
      </div>
    </div>

    <div class="stats-card active">
      <div class="stats-icon">
        <i class="fas fa-user"></i>
      </div>
      <div class="stats-info">
        <span class="stats-label">Regular Users</span>
        <h3 id="regularUsers">0</h3>
        <span class="stats-type">Regular</span>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section">
    <div class="row g-3">
      <div class="col-md-4">
        <div class="input-group">
          <span class="input-group-text">
            <i class="fas fa-search"></i>
          </span>
          <input type="text" class="form-control" id="userSearch" placeholder="Search users..." onkeyup="filterUsers()">
        </div>
      </div>
      <div class="col-md-3">
        <select class="form-select" id="statusFilter" onchange="filterUsers()">
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>
      <div class="col-md-3">
        <select class="form-select" id="roleFilter" onchange="filterUsers()">
          <option value="">All Roles</option>
          <option value="admin">Admin</option>
          <option value="user">User</option>
        </select>
      </div>
      <div class="col-md-2">
        <button class="btn btn-primary w-100" onclick="openAddUserModal()">
          <i class="fas fa-plus me-2"></i>Add User
        </button>
      </div>
    </div>
  </div>

  <!-- User List Table -->
  <div class="content-card">
    <div class="card-header">
      <h5>User List</h5>
      <button type="button" class="btn btn-outline-primary" onclick="exportToExcel()">
        <i class="fas fa-download me-2"></i>Export to Excel
      </button>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center">No</th>
              <th>Full Name</th>
              <th>Username</th>
              <th>Email</th>
              <th class="text-center">Status</th>
              <th class="text-center">Role</th>
              <th class="text-center">Actions</th>
            </tr>
          </thead>
          <tbody id="userTableBody">
            <!-- Users will be populated here -->
          </tbody>
        </table>
      </div>
    </div>
    <!-- Empty State -->
    <div id="emptyState" class="empty-state d-none">
      <i class="fas fa-users"></i>
      <h5>No Users Found</h5>
      <p>Try adjusting your search or filter criteria</p>
    </div>
  </div>
</div>

<!-- Add User Modal -->
<div class="modal fade user-modal" id="addUserModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-user-plus"></i>
          Add New User
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="addUserForm" class="row g-3">
          <!-- Basic Information -->
          <div class="col-12">
            <h6 class="section-title">
              <i class="fas fa-info-circle"></i> Basic Information
            </h6>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Full Name <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-user"></i></span>
              <input type="text" class="form-control" name="fullName" required>
            </div>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Username <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-at"></i></span>
              <input type="text" class="form-control" name="username" required>
            </div>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Email <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-envelope"></i></span>
              <input type="email" class="form-control" name="email" required>
            </div>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Phone</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-phone"></i></span>
              <input type="tel" class="form-control" name="phone">
            </div>
          </div>

          <!-- Security Settings -->
          <div class="col-12 mt-4">
            <h6 class="section-title">
              <i class="fas fa-shield-alt"></i> Security Settings
            </h6>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Password <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-lock"></i></span>
              <input type="password" class="form-control" name="password" required>
              <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this)">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">User Type <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-users"></i></span>
              <select class="form-select" name="userType" required>
                <option value="">Select User Type</option>
                <option value="Internal">Internal</option>
                <option value="External">External</option>
              </select>
            </div>
          </div>

          <!-- User Permissions -->
          <div class="col-12">
            <h6 class="section-title">
              <i class="fas fa-key"></i> Permissions & Settings
            </h6>
          </div>
          
          <div class="col-12">
            <div class="row g-3">
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="addisAdmin" id="addisAdmin">
                  <label class="form-check-label" for="addisAdmin">
                    <i class="fas fa-user-shield"></i> Admin User
                  </label>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="twoFactorEnabled" id="addTwoFactor">
                  <label class="form-check-label" for="addTwoFactor">
                    <i class="fas fa-key"></i> Two-Factor Authentication
                  </label>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="notificationsEnabled" id="addNotifications">
                  <label class="form-check-label" for="addNotifications">
                    <i class="fas fa-bell"></i> Enable Notifications
                  </label>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="validStatus" id="addValidStatus" checked>
                  <label class="form-check-label" for="addValidStatus">
                    <i class="fas fa-toggle-on"></i> Account Active
                  </label>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times"></i> Cancel
        </button>
        <button type="button" class="btn btn-primary" onclick="addUser()">
          <i class="fas fa-save"></i> Save
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade user-modal" id="editUserModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-user-edit"></i>
          Edit User
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="editUserForm" class="row g-3">
          <input type="hidden" id="editUserId">
          
          <!-- User Information Section -->
          <div class="col-12">
            <h6 class="section-title">
              <i class="fas fa-info-circle"></i> Basic Information
            </h6>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Full Name <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-user"></i></span>
              <input type="text" class="form-control" name="fullName" required>
            </div>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Username <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-at"></i></span>
              <input type="text" class="form-control" name="username" disabled>
            </div>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Email <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-envelope"></i></span>
              <input type="email" class="form-control" name="email" required>
            </div>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">Phone</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-phone"></i></span>
              <input type="tel" class="form-control" name="phone">
            </div>
          </div>

          <!-- Security Section -->
          <div class="col-12 mt-4">
            <h6 class="section-title">
              <i class="fas fa-shield-alt"></i> Security Settings
            </h6>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">New Password</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-lock"></i></span>
              <input type="password" class="form-control" name="password" autocomplete="new-password">
              <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this)">
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <small class="text-muted">Leave blank to keep current password</small>
          </div>
          
          <div class="col-md-6">
            <label class="form-label">User Type</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-users"></i></span>
              <select class="form-select" name="userType">
                <option value="">Select User Type</option>
                <option value="Internal">Internal</option>
                <option value="External">External</option>
              </select>
            </div>
          </div>

          <!-- User Permissions Section -->
          <div class="col-12">
            <h6 class="section-title">
              <i class="fas fa-key"></i> Permissions & Settings
            </h6>
          </div>
          <div class="col-12">
            <div class="row g-3">
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="editisAdmin" id="editIsAdmin">
                  <label class="form-check-label" for="editIsAdmin">
                    <i class="fas fa-user-shield"></i> Admin User
                  </label>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="twoFactorEnabled" id="editTwoFactor">
                  <label class="form-check-label" for="editTwoFactor">
                    <i class="fas fa-key"></i> Two-Factor Authentication
                  </label>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="notificationsEnabled" id="editNotifications">
                  <label class="form-check-label" for="editNotifications">
                    <i class="fas fa-bell"></i> Enable Notifications
                  </label>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input type="checkbox" class="form-check-input" name="validStatus" id="editValidStatus">
                  <label class="form-check-label" for="editValidStatus">
                    <i class="fas fa-toggle-on"></i> Account Active
                  </label>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times"></i> Cancel
        </button>
        <button type="button" class="btn btn-primary" onclick="updateUser()">
          <i class="fas fa-save"></i> Update
        </button>
      </div>
    </div>
  </div>
</div>

{% endblock %} 