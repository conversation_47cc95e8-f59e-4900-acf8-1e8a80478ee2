const moment = require('moment');
const BQELogger = require('./bqeLogger');

class BQEDataProcessor {
  constructor() {
    this.logger = new BQELogger();
    this.processLogs = {
      steps: [],
      documents: []
    };
  }

  getPaymentInfo(customFields) {
    if (!customFields || !Array.isArray(customFields)) {
      return null;
    }

    // Look for payment information in custom fields
    const remitPaymentField = customFields.find(f => f.label === 'Remit Payment To');
    return remitPaymentField?.value || null;
  }

  extractSupplierInfo(companyData) {
    if (!companyData) {
      return {
        name: 'NA',
        tin: 'NA',
        registrationNumber: 'NA',
        sstId: 'NA',
        ttxId: 'NA',
        msicCode: 'NA',
        businessActivity: 'NA',
        countryCode: 'MYS',
        stateCode: '14',
        phone: 'NA',
        email: 'NA',
        certExId: 'NA',
        address: {
          line1: 'NA',
          line2: '',
          city: 'NA',
          state: 'NA',
          postcode: 'NA',
          country: 'MYS',
          formattedAddress: 'NA'
        }
      };
    }
  
    const customFields = companyData.customFields || [];
    const phone = companyData.phone?.replace(/\s+/g, '') || 'NA';
    const email = companyData.email || 'NA';
    
    // Format address components
    const addressLine1 = companyData.addressLine1 || '';
    const addressLine2 = companyData.addressLine2 || '';
    const city = companyData.city?.trim() || '';
    //const state = companyData.stateCode || '';
    const state = '14';
    const postcode = companyData.zip || '';
    const country = companyData.country || 'MYS';
    
    // Create formatted address
    const formattedAddress = [
      addressLine1,
      addressLine2,
      city,
      state,
      postcode,
      country
    ].filter(part => part).join(', ');
  
    return {
      name: companyData.name || 'NA',
      tin: customFields.find(f => f.label === "Supplier's TIN")?.value || 'NA',
      registrationNumber: customFields.find(f => f.label === "Supplier's Registration No")?.value || 'NA',
      sstId: customFields.find(f => f.label === "Supplier's SST No")?.value || 'NA',
      ttxId: customFields.find(f => f.label === "Supplier's Tourism Tax No")?.value || 'NA',
      msicCode: customFields.find(f => f.label === "Supplier's MSIC Code")?.value || 'NA',
      businessActivity: customFields.find(f => f.label === "Supplier's Business Activity")?.value || 'NA',
      countryCode: customFields.find(f => f.label === "COUNTRY CODE")?.description || 'MYS',
      stateCode: customFields.find(f => f.label === "State Code")?.description || '17',
      phone: phone,
      email: email,
      certExId: customFields.find(f => f.label === "Company Other 1")?.value || 'NA',
      address: {
        line1: addressLine1,
        line2: addressLine2,
        city: city,
        state: state,
        postcode: postcode,
        country: country,
        formattedAddress: formattedAddress
      }
    };
  }
  
  extractBuyerInfo(clientData) {
    if (!clientData) {
      return {
        name: 'NA',
        tin: 'NA',
        registrationNumber: 'NA',
        sstId: 'NA',
        ttxId: 'NA',
        msicCode: 'NA',
        businessActivity: 'NA',
        countryCode: 'MYS',
        stateCode: '14',
        phone: 'NA',
        email: 'NA',
        certExId: 'NA',
        address: {
          line1: 'NA',
          line2: '',
          city: 'NA',
          state: '14',
          postcode: 'NA',
          country: 'MYS',
          formattedAddress: 'NA'
        }
      };
    }
  
    const communications = clientData.communications || [];
    const phone = communications.find(c => c.typeName === 'Phone')?.value?.replace(/\s+/g, '') || 'NA';
    const email = communications.find(c => c.typeName === 'Email')?.value || 'NA';
    const customFields = clientData.customFields || [];
    
    // Format address components
    const addressLine1 = clientData.address?.street1 || '';
    const addressLine2 = clientData.address?.street2 || '';
    const city = clientData.address?.city || '';
    //const state = clientData.address?.state || '';
    const state = '14';
    const postcode = clientData.address?.zip || '';
    const country = clientData.address?.country || 'MYS';
    
    // Create formatted address
    const formattedAddress = [
      addressLine1,
      addressLine2,
      city,
      state,
      postcode,
      country
    ].filter(part => part).join(', ');
  
    return {
      name: clientData.company || 'NA',
      tin: clientData.taxId || 'NA',
      sstId: customFields.find(f => f.label === "Buyer's SST No")?.value || 'NA',
      ttxId: customFields.find(f => f.label === "Buyer's Tourism Tax No")?.value || 'NA',
      registrationNumber: customFields.find(f => f.label === "Buyer's Registration No")?.value || clientData.registrationNumber || 'NA',
      msicCode: customFields.find(f => f.label === "Buyer's MSIC Code")?.value || clientData.msicCode || 'NA',
      businessActivity: customFields.find(f => f.label === "Buyer's Business Activity")?.value || 'NA',
      countryCode: customFields.find(f => f.label === "BUYER'S COUNTRY CODE")?.description || 'MYS',
      stateCode: customFields.find(f => f.label === "BUYER'S ADDRESS STATE CODE")?.description || '14',
      phone: phone,
      email: email,
      certExId: customFields.find(f => f.label === "Client Other 1")?.value || 'NA',
      address: {
        line1: addressLine1,
        line2: addressLine2,
        city: city,
        state: state,
        postcode: postcode,
        country: country,
        formattedAddress: formattedAddress
      }
    };
  }

  getTaxRate(invoice, projectDetails, projectCustomFields) {
    try {
      // Check if tax type is 'E' (Exempt) - if exempt, return 0 immediately
      const taxTypeField = projectCustomFields?.find(f => f.label === 'TAX TYPE (CODE)');
      if (taxTypeField?.description?.includes('E')) {
        return 0;
      }

      // PRIORITY 1: Check project level tax rate first
      
      // 1.1: Check project details mainServiceTax
      if (typeof projectDetails?.mainServiceTax === 'number' && 
          !isNaN(projectDetails.mainServiceTax) && 
          projectDetails.mainServiceTax !== 0) {
        return projectDetails.mainServiceTax;
      }
      
      // 1.2: Check project custom fields
      const projectTaxRateField = projectCustomFields?.find(f => 
        f.label === 'Tax Rate' || 
        f.label === 'SERVICE TAX RATE'
      );
      if (projectTaxRateField?.value && parseFloat(projectTaxRateField.value) !== 0) {
        return parseFloat(projectTaxRateField.value);
      }

      // PRIORITY 2: Check invoice custom fields
      const invoiceCustomFields = invoice?.customFields || [];
      const taxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');
      if (taxRateField?.value) {
        return parseFloat(taxRateField.value);
      }

      // PRIORITY 3: Check raw invoice tax rate
      if (typeof invoice?.mainServiceTax === 'number' && 
          !isNaN(invoice.mainServiceTax)) {
        return invoice.mainServiceTax;
      }

      // Default to standard service tax rate if no other rate found
      return 8.00;
    } catch (error) {
      console.error('Error getting tax rate:', error);
      return 8.00; // Default to standard rate on error
    }
  }

  extractTaxInfo(rawInvoice, projectCustomFields, projectDetails) {
    try {
        // Get tax type and exemption information
        const taxTypeField = projectCustomFields?.find(f => f.label === 'TAX TYPE (CODE)');
        const taxExemptionField = projectCustomFields?.find(f => f.label === 'Details of Tax Exemption');
        
        // Extract and validate tax type code
        let rawTaxType = taxTypeField?.description?.split('|')?.[0]?.trim() || '02';
        
        // Check for tax exemption or Not Applicable
        const isExempted = rawTaxType === 'E' || rawTaxType === '06';
        
        return {
            taxRate: 0, // Always 0 for exempt/not applicable
            originalTaxRate: 0, // Store original rate as 0
            taxType: rawTaxType,
            taxTypeDescription: rawTaxType === '06' ? 'Not Applicable' : 
                              (rawTaxType === 'E' ? 'Tax Exemption' : 'Service Tax'),
            taxExemption: isExempted ? 
                (taxExemptionField?.description || 'Not Applicable') : 
                undefined,
            isExempted: isExempted,
            taxableAmount: rawInvoice?.invoiceAmount || 0,
            taxAmount: 0 
        };
    } catch (error) {
        console.error('Error extracting tax info:', error);
        return {
            taxRate: 0,
            originalTaxRate: 0,
            taxType: '06',
            taxTypeDescription: 'Not Applicable',
            taxExemption: 'Not Applicable',
            isExempted: true,
            taxableAmount: 0,
            taxAmount: 0
        };
    }
  }

  processLineItems(invoiceDetails, projectDetailsArray) {
    if (!invoiceDetails || !Array.isArray(invoiceDetails) || invoiceDetails.length === 0) {
        return [{
            id: 'default',
            amount: 0,
            quantity: 1,
            unitCode: "EA",
            description: 'No line items available',
            project: 'NA',
            projectTitle: 'NA',
            mainServiceTax: 0,
            classifications: {
                invoice: "022",
                department: "01-HIGHWAY",
                lead: "NA"
            },
            tax: {
                types: [{
                    type: '02',
                    rate: 0,
                    originalRate: 0,
                    amount: 0,
                    potentialAmount: 0
                }],
                exemption: 'Not Applicable',
                isExempted: false,
                potentialTaxAmount: 0
            },
            accountInfo: {
                income: 'NA',
                code: 'NA'
            }
        }];
    }

    return invoiceDetails.map((detail, index) => {
        // Find matching project details
        const project = projectDetailsArray?.find(p => p.projectId === detail.projectId)?.details || {};
        const customFields = project?.customFields || [];

        // Get tax information from custom fields
        const taxTypeField = customFields.find(f => f.label === "TAX TYPE (CODE)");
        const customProjTitle = customFields.find(f => f.label === "Project Title");
        const taxExemptionField = customFields.find(f => f.label === "Details of Tax Exemption");
        const taxRateField = customFields.find(f => f.label === "Tax Rate");
        
        // Parse tax type - could be multiple types separated by comma
        const taxTypes = taxTypeField?.description?.split('|')?.[0]?.split(',')
            .map(t => t.trim()) || ['02'];
            
        // Get original tax rate (before exemption)
        let originalTaxRate = 0;
        
        // First check invoice custom fields
        if (detail.customFields && Array.isArray(detail.customFields)) {
            const invoiceTaxRateField = detail.customFields.find(f => f.label === 'Tax rate');
            if (invoiceTaxRateField?.value && !isNaN(parseFloat(invoiceTaxRateField.value))) {
                originalTaxRate = parseFloat(invoiceTaxRateField.value);
            }
        }
        
        // If not found in invoice, check project custom fields
        if (originalTaxRate === 0 && taxRateField?.value && !isNaN(parseFloat(taxRateField.value))) {
            originalTaxRate = parseFloat(taxRateField.value);
        }
        
        // If still not found, use project mainServiceTax
        if (originalTaxRate === 0 && project?.mainServiceTax) {
            originalTaxRate = project.mainServiceTax;
        }
        
        // Default to 8% if still not found (common service tax rate)
        if (originalTaxRate === 0) {
            originalTaxRate = 8.0;
        }
        
        // Get tax rate - 0 if exempt, otherwise use original rate
        const taxRate = taxTypes.includes('E') ? 0 : originalTaxRate;
        
        // Clean description
        const description = this.cleanHtmlContent(project?.memo);
        
        // Get project title
        const projectTitle = this.cleanHtmlContent(customProjTitle?.value) || 'NA';
        
        // Get classifications
        const invoiceClassification = customFields.find(f => f.label === "Invoice Classification")?.description || "022";
        const projectDepartment = customFields.find(f => f.label === "Project Department")?.description || "01-HIGHWAY";
        const projectLead = customFields.find(f => f.label === "Project LEAD DEPARTMENT")?.description || "NA";

        // Calculate tax amount based on tax rate
        const taxAmount = taxTypes.includes('E') ? 0 : (detail.amount * taxRate / 100);
        
        // Calculate what the tax would have been if not exempt (for display only)
        const potentialTaxAmounts = taxTypes.includes('E') || taxTypes.includes('06') ? 0 : (detail.amount * taxRate / 100);

        return {
            id: detail.projectId || `line-${index + 1}`,
            lineId: index + 1,
            amount: detail.amount || 0,
            quantity: 1,
            unitCode: "EA",
            description: detail.memo1 || description,
            project: project?.displayName || detail.project || 'NA',
            projectTitle: projectTitle,
            mainServiceTax: project?.mainServiceTax || 0,
            classifications: {
                invoice: invoiceClassification,
                department: projectDepartment,
                lead: projectLead
            },
            tax: {
                types: taxTypes.map(type => ({
                    type,
                    rate: type === 'E' ? 0 : taxRate,
                    originalRate: originalTaxRate,
                    amount: type === 'E' || type === '06' ? 0 : (detail.amount * taxRate / 100),
                    potentialAmount: type === 'E' ? (detail.amount * originalTaxRate / 100) : taxAmount
                })),
                exemption: taxTypes.includes('E') ? 
                    (taxExemptionField?.description || 'Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018') : 
                    undefined,
                isExempted: taxTypes.includes('E') || taxTypes.includes('06'),
                potentialTaxAmount: taxTypes.includes('E') || taxTypes.includes('06') ? 0 : (detail.amount * taxRate / 100) || potentialTaxAmounts
            },
            accountInfo: {
                income: project?.incomeAccount || 'NA',
                code: project?.code || 'NA'
            },
            lineExtensionAmount: detail.serviceAmount || 0,
            price: {
                amount: detail.serviceAmount || 0,
                extension: detail.serviceAmount || 0
            },
            taxTotal: {
                amount: detail.serviceTaxAmount || 0,
                taxableAmount: detail.serviceAmount || 0,
                taxAmount: detail.mainServiceTax || 0,
                percent: project?.mainServiceTax || 0,
                category: {
                    id: taxTypes[0] || '02',
                    exemptionReason: taxTypes.includes('E') ? (taxExemptionField?.description || 'Tax exemption (where applicable)') : undefined
                }
            },
            item: {
                description: detail.memo1 || description,
                classification: {
                    code: invoiceClassification,
                    type: 'CLASS'
                },
                originCountry: 'MYS'
            },
            allowanceCharges: []
        };
    });
}

  cleanHtmlContent(html) {
    if (!html) return '';
    return html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
  }

  async processRawBQEData(data) {
    console.log('Processing BQE data:', data);
  
    if (!data || !data._rawInvoice) {
      throw new Error('Invalid BQE data');
    }
  
    try {
      // Create initial log entry
      const initialLog = {
        stage: 'initial',
        timestamp: moment().format(),
        rawData: data
      };
  
      const currentDate = new Date();
      const formattedDate = currentDate.toISOString().split('T')[0];
      const formattedTime = currentDate.toISOString().split('T')[1].split('.')[0] + 'Z';
      
      // Ensure we have the necessary arrays
      if (!data._projectDetailsArray) data._projectDetailsArray = [];
      if (!data._invoiceDetails) data._invoiceDetails = [];
      
      // Get the first project details if available
      const projectDetails = data._projectDetailsArray[0]?.details || {};
      const projectCustomFields = projectDetails?.customFields || [];
      
      // Extract tax information
      const taxInfo = this.extractTaxInfo(
        data._rawInvoice, 
        projectCustomFields,
        projectDetails
      );
      
      // Extract supplier and buyer information
      const supplier = this.extractSupplierInfo(data.company);
      const buyer = this.extractBuyerInfo(data._clientDetails);
      
      // Get project title
      const projTitle = this.cleanHtmlContent(
        projectCustomFields.find(f => f.label === "Project Title")?.value
      ) || 'NA';
      
      // Get payment information
      const paymentInfo = this.getPaymentInfo(data.company?.customFields);
      
      // Process line items
      const lineItems = this.processLineItems(data._invoiceDetails, data._projectDetailsArray);
  
      // Process data as before
      const processedData = {
        ...data,
        supplier,
        buyer,
        projTitle,
        tax_info: taxInfo,
        payment_info: paymentInfo,
        invoice: {
          EinvoiceTypeCode: '01',
          number: data._rawInvoice?.invoiceNumber || '',
          date: moment(data._rawInvoice?.date || currentDate).format('YYYY-MM-DD'),
          issueDate: [{ _: formattedDate }],
          issueTime: [{ _: formattedTime }],
          currency: data._rawInvoice?.currency || 'MYR',
          version: data._rawInvoice?.customFields?.find(f => 
            f.label === 'Invoice Version')?.value || '1.0',
          amount: {
            total: data._rawInvoice?.invoiceAmount || 0,
            taxable: data._rawInvoice?.serviceAmount || 0,
            tax: data._rawInvoice?.mainServiceTax || 0
          },
          messageOnInvoice: data._rawInvoice?.messageOnInvoice || '',
          invoiceFrom: data._rawInvoice?.invoiceFrom ? moment(data._rawInvoice.invoiceFrom).format('YYYY-MM-DD') : formattedDate,
          invoiceTo: data._rawInvoice?.invoiceTo ? moment(data._rawInvoice.invoiceTo).format('YYYY-MM-DD') : formattedDate
        },
        line_items: lineItems
      };
      
      // Ensure the raw invoice has all necessary fields for the mapper
      if (!processedData._rawInvoice.issuanceDate) {
        processedData._rawInvoice.issuanceDate = formattedDate;
      }
      
      if (!processedData._rawInvoice.issuanceTime) {
        processedData._rawInvoice.issuanceTime = formattedTime;
      }
  
      // Create final log entry
      const finalLog = {
        stage: 'processed',
        timestamp: moment().format(),
        processedData,
        validation: {
          hasSupplier: !!processedData.supplier,
          hasBuyer: !!processedData.buyer,
          hasTaxInfo: !!processedData.tax_info,
          hasInvoice: !!processedData.invoice,
          hasLineItems: processedData.line_items && processedData.line_items.length > 0
        }
      };
  
      // Write processing log
      await this.logger.writeProcessingLog(
        processedData.invoice.number,
        {
          initial: initialLog,
          final: finalLog
        }
      );
  
      // Add attention to the processed data
      const attention = data._clientDetails?.attention || 
      (data._clientDetails?.firstName && data._clientDetails?.lastName ? 
        `${data._clientDetails.firstName} ${data._clientDetails.lastName}`.trim() : 
        data._clientDetails?.name) || 
      '';
      processedData.attention = attention;
  
      (data._clientDetails?.firstName && data._clientDetails?.lastName ? 
        `${data._clientDetails.firstName} ${data._clientDetails.lastName}`.trim() : 
        data._clientDetails?.name) || 
      '';
      processedData.attention = attention;
  
      return processedData;
  
    } catch (error) {
      console.error('Error in processRawBQEData:', error);
      
      // Log error
      await this.logger.writeProcessingLog(
        data._rawInvoice?.invoiceNumber || 'unknown',
        {
          stage: 'error',
          timestamp: moment().format(),
          error: {
            message: error.message,
            stack: error.stack
          },
          rawData: data
        }
      );
  
      throw error;
    }
  }
}

module.exports = BQEDataProcessor;