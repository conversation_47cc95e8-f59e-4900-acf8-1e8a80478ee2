{% extends '../layout.html' %}

{% block head %}
<title>User Management - eInvoice Portal</title>
<link href="/assets/css/pages/user-settings.css" rel="stylesheet">
<link href="/assets/css/pages/profile.css" rel="stylesheet">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="profile-container">
  <!-- Header -->
  <div class="profile-welcome-section">
    <div class="profile-welcome-card">
    <h2>
      <i class="fas fa-users-cog"></i>
      User Management
    </h2>
    <p>Add, edit, and manage user accounts and their access levels</p>
  </div>

  <div class="user-settings-content">
    <!-- Left Sidebar - User Stats -->
    <div class="user-stats-card">
      <h6 class="user-stats-title">
        <i class="fas fa-chart-pie"></i>
        User Statistics
      </h6>
      
      <div class="user-stat-item">
        <div class="user-stat-icon total">
          <i class="fas fa-users"></i>
        </div>
        <div class="user-stat-details">
          <h4 class="user-stat-number" id="totalUsers">0</h4>
          <p class="user-stat-label">Total Users</p>
        </div>
      </div>

      <div class="user-stat-item">
        <div class="user-stat-icon admin">
          <i class="fas fa-user-shield"></i>
        </div>
        <div class="user-stat-details">
          <h4 class="user-stat-number" id="adminUsers">0</h4>
          <p class="user-stat-label">Admin Users</p>
        </div>
      </div>

      <div class="user-stat-item">
        <div class="user-stat-icon regular">
          <i class="fas fa-user"></i>
        </div>
        <div class="user-stat-details">
          <h4 class="user-stat-number" id="regularUsers">0</h4>
          <p class="user-stat-label">Regular Users</p>
        </div>
      </div>
    </div>

    <!-- Right Content - User List -->
    <div class="user-list-section">
      <div class="user-list-header">
        <h3 class="user-list-title">
          <i class="fas fa-list"></i>
          User List
        </h3>
        <button class="btn btn-primary user-add-btn" onclick="openAddUserModal()">
          <i class="fas fa-plus"></i>
          Add User
        </button>
      </div>

      <div class="table-responsive">
        <table class="user-table">
          <thead>
            <tr>
              <th>Full Name</th>
              <th>Username</th>
              <th>Email</th>
              <th>Role</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="userTableBody">
            <!-- Users will be populated here -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Add User Modal -->
<div class="modal fade user-modal" id="addUserModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-user-plus"></i>
          Add New User
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="addUserForm">
          <div class="user-form-group">
            <label class="user-form-label">
              Full Name <span class="text-danger">*</span>
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="User's full name as it will appear in the system"></i>
            </label>
            <input type="text" class="user-form-control" name="fullName" required>
          </div>
          <div class="user-form-group">
            <label class="user-form-label">
              Username <span class="text-danger">*</span>
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Unique username for system login"></i>
            </label>
            <input type="text" class="user-form-control" name="username" required>
          </div>
          <div class="user-form-group">
            <label class="user-form-label">
              Email <span class="text-danger">*</span>
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Email address for notifications and password recovery"></i>
            </label>
            <input type="email" class="user-form-control" name="email" required>
          </div>
          <div class="user-form-group">
            <label class="user-form-label">
              Password <span class="text-danger">*</span>
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Strong password required for account security"></i>
            </label>
            <input type="password" class="user-form-control" name="password" required>
          </div>
          <div class="user-form-group">
            <div class="user-checkbox-group">
              <input type="checkbox" name="isAdmin" id="addIsAdmin">
              <label class="user-form-label mb-0" for="addIsAdmin">
                Admin User
                <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Grant administrative privileges to this user"></i>
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="user-modal-btn cancel" data-bs-dismiss="modal">
          <i class="fas fa-times"></i>
          Cancel
        </button>
        <button type="button" class="user-modal-btn save" onclick="saveUser()">
          <i class="fas fa-save"></i>
          Save
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade user-modal" id="editUserModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-user-edit"></i>
          Edit User
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="editUserForm">
          <input type="hidden" id="editUserId">
          <div class="user-form-group">
            <label class="user-form-label">
              Full Name <span class="text-danger">*</span>
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="User's full name as it will appear in the system"></i>
            </label>
            <input type="text" class="user-form-control" name="fullName" required>
          </div>
          <div class="user-form-group">
            <label class="user-form-label">
              Username <span class="text-danger">*</span>
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Unique username for system login"></i>
            </label>
            <input type="text" class="user-form-control" name="username" required>
          </div>
          <div class="user-form-group">
            <label class="user-form-label">
              Email <span class="text-danger">*</span>
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Email address for notifications and password recovery"></i>
            </label>
            <input type="email" class="user-form-control" name="email" required>
          </div>
          <div class="user-form-group">
            <label class="user-form-label">
              New Password
              <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Leave blank to keep current password"></i>
            </label>
            <input type="password" class="user-form-control" name="password">
          </div>
          <div class="user-form-group">
            <div class="user-checkbox-group">
              <input type="checkbox" name="isAdmin" id="editIsAdmin">
              <label class="user-form-label mb-0" for="editIsAdmin">
                Admin User
                <i class="fas fa-info-circle" data-bs-toggle="tooltip" title="Grant administrative privileges to this user"></i>
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="user-modal-btn cancel" data-bs-dismiss="modal">
          <i class="fas fa-times"></i>
          Cancel
        </button>
        <button type="button" class="user-modal-btn save" onclick="updateUser()">
          <i class="fas fa-save"></i>
          Update
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let users = [];
const addUserModal = new bootstrap.Modal(document.getElementById('addUserModal'));
const editUserModal = new bootstrap.Modal(document.getElementById('editUserModal'));

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    loadUsers();
});

function loadUsers() {
    fetch('/api/users-list')
        .then(response => response.json())
        .then(data => {
            users = data;
            renderUsers();
            updateUserStats();
        })
        .catch(error => {
            console.error('Error loading users:', error);
            Swal.fire({
                icon: 'error',
                title: 'Failed to load users',
                text: error.message
            });
        });
}

function renderUsers() {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';

    users.forEach(user => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${user.FullName}</td>
            <td>${user.Username}</td>
            <td>${user.Email}</td>
            <td>
                <span class="user-role-badge ${user.Admin ? 'admin' : 'regular'}">
                    ${user.Admin ? 'Admin' : 'User'}
                </span>
            </td>
            <td>
                <div class="user-actions">
                    <button class="user-action-btn edit" onclick="openEditUserModal(${user.ID})" title="Edit User">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="user-action-btn delete" onclick="deleteUser(${user.ID})" title="Delete User">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

function updateUserStats() {
    const totalUsers = users.length;
    const adminUsers = users.filter(user => user.Admin).length;
    const regularUsers = totalUsers - adminUsers;

    document.getElementById('totalUsers').textContent = totalUsers;
    document.getElementById('adminUsers').textContent = adminUsers;
    document.getElementById('regularUsers').textContent = regularUsers;
}

function openAddUserModal() {
    document.getElementById('addUserForm').reset();
    addUserModal.show();
}

function openEditUserModal(userId) {
    const user = users.find(u => u.ID === userId);
    if (!user) return;

    const form = document.getElementById('editUserForm');
    document.getElementById('editUserId').value = user.ID;
    form.querySelector('[name="fullName"]').value = user.FullName;
    form.querySelector('[name="username"]').value = user.Username;
    form.querySelector('[name="email"]').value = user.Email;
    form.querySelector('[name="isAdmin"]').checked = user.Admin;
    form.querySelector('[name="password"]').value = '';

    editUserModal.show();
}

function saveUser() {
    const formData = new FormData(document.getElementById('addUserForm'));
    const userData = {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        username: formData.get('username'),
        password: formData.get('password'),
        admin: formData.get('isAdmin') === 'on'
    };

    // Default company settings
    const companySettings = {
        CompanyName: userData.fullName + "'s Company", // Default company name
        Industry: "Not Specified",
        Country: "Malaysia", // Default country
        Email: userData.email,
        ValidStatus: "1",
        About: "Default company profile"
    };

    // Show loading alert
    Swal.fire({
        title: 'Saving...',
        text: 'Please wait while we create the new user',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // First create the user
    fetch('/api/users-add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false) {
            throw new Error(data.message);
        }
        
        // After user is created, create company settings
        return fetch('/api/company-settings/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...companySettings,
                UserID: data.userId // Use the ID of the newly created user
            })
        });
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false) {
            throw new Error(data.message);
        }
        addUserModal.hide();
        loadUsers();
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'User and company settings created successfully'
        });
    })
    .catch(error => {
        console.error('Error adding user:', error);
        Swal.fire({
            icon: 'error',
            title: 'Failed to add user',
            text: error.message
        });
    });
}

function updateUser() {
    const formData = new FormData(document.getElementById('editUserForm'));
    const userId = document.getElementById('editUserId').value;
    const userData = {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        username: formData.get('username'),
        password: formData.get('password'),
        admin: formData.get('isAdmin') === 'on'
    };

    // Show loading alert
    Swal.fire({
        title: 'Updating...',
        text: 'Please wait while we update the user details',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    fetch(`/api/users-update/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false) {
            throw new Error(data.message);
        }
        editUserModal.hide();
        loadUsers();
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'User updated successfully'
        });
    })
    .catch(error => {
        console.error('Error updating user:', error);
        Swal.fire({
            icon: 'error',
            title: 'Failed to update user',
            text: error.message
        });
    });
}

function deleteUser(userId) {
    Swal.fire({
        title: 'Are you sure?',
        text: "This action cannot be undone!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc2626',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, delete user',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/api/users-delete/${userId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success === false) {
                    throw new Error(data.message);
                }
                loadUsers();
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'User deleted successfully'
                });
            })
            .catch(error => {
                console.error('Error deleting user:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Failed to delete user',
                    text: error.message
                });
            });
        }
    });
}
</script>
{% endblock %} 