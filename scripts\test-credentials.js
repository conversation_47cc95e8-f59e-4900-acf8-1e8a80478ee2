const { execSync } = require('child_process');

async function testDirectAccess() {
    const config = {
        path: '\\\\EP-SVRERP\\SFTPRoot\\SAP B1 Extracted Files',
        username: 'EP-SVRERP\\elian_einvoice',
        password: 'ep@4227219A'
    };

    try {
        console.log('=== Network Access Test ===\n');

        // First, try to list current connections
        console.log('Current connections:');
        try {
            const connections = execSync('net use', { encoding: 'utf8' });
            console.log(connections);
        } catch (e) {
            console.log('No existing connections');
        }

        // Try to delete any existing connections
        console.log('\nCleaning up existing connections...');
        try {
            execSync('net use * /delete /y', { encoding: 'utf8' });
            console.log('Cleanup successful');
        } catch (e) {
            console.log('No connections to clean up');
        }

        // Attempt new connection
        console.log('\nAttempting connection...');
        const connectCommand = `net use "${config.path}" /USER:${config.username} "${config.password}" /PERSISTENT:NO`;
        
        try {
            execSync(connectCommand, { encoding: 'utf8' });
            console.log('Connection successful!');
        } catch (error) {
            console.log('Connection failed:', error.message);
            if (error.stdout) console.log('stdout:', error.stdout);
            if (error.stderr) console.log('stderr:', error.stderr);
            throw error;
        }

        // If connection successful, try to list directory
        console.log('\nTesting directory access...');
        try {
            const dirResult = execSync(`dir "${config.path}"`, { encoding: 'utf8' });
            console.log('Directory listing successful:');
            console.log(dirResult);
        } catch (error) {
            console.log('Directory listing failed:', error.message);
        }

        return { success: true };
    } catch (error) {
        return { 
            success: false, 
            error: error.message 
        };
    } finally {
        // Cleanup
        try {
            execSync('net use * /delete /y', { encoding: 'utf8' });
            console.log('\nConnection cleanup completed');
        } catch (e) {
            console.log('\nCleanup not needed');
        }
    }
}

module.exports = testDirectAccess;