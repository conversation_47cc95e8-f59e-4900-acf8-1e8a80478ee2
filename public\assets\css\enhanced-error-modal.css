/* Enhanced <PERSON><PERSON><PERSON> */
:root {
  --error-primary: #dc3545;
  --error-primary-light: #f8d7da;
  --error-primary-dark: #b02a37;
  --error-text: #212529;
  --error-text-light: #6c757d;
  --error-border: #dee2e6;
  --error-background: #fff;
  --error-shadow: rgba(220, 53, 69, 0.2);
  --error-success: #198754;
  --error-warning: #ffc107;
  --error-info: #0dcaf0;
}

.enhanced-error-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.enhanced-error-modal.show {
  opacity: 1;
  visibility: visible;
}

.enhanced-error-dialog {
  width: 100%;
  max-width: 500px;
  background-color: var(--error-background);
  border-radius: 12px;
  box-shadow: 0 10px 25px var(--error-shadow);
  overflow: hidden;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.enhanced-error-modal.show .enhanced-error-dialog {
  transform: translateY(0);
}

.enhanced-error-header {
  padding: 1.5rem;
  background-color: var(--error-primary-light);
  text-align: center;
  position: relative;
}

.enhanced-error-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background-color: var(--error-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.enhanced-error-icon i {
  font-size: 2rem;
}

.enhanced-error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--error-primary-dark);
  margin: 0;
}

.enhanced-error-subtitle {
  font-size: 1rem;
  color: var(--error-text-light);
  margin: 0.5rem 0 0;
}

.enhanced-error-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: var(--error-text-light);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.enhanced-error-body {
  padding: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.enhanced-error-message {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid var(--error-primary);
}

.enhanced-error-code {
  font-family: monospace;
  background-color: #f1f1f1;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  overflow-x: auto;
  font-size: 0.9rem;
}

.enhanced-error-details {
  margin-top: 1rem;
}

.enhanced-error-details-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--error-text);
  display: flex;
  align-items: center;
}

.enhanced-error-details-title i {
  margin-right: 0.5rem;
}

.enhanced-error-property {
  display: flex;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid var(--error-border);
  padding-bottom: 0.5rem;
}

.enhanced-error-property:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.enhanced-error-property-name {
  font-weight: 500;
  width: 40%;
  color: var(--error-text);
}

.enhanced-error-property-value {
  width: 60%;
  color: var(--error-text-light);
  word-break: break-word;
}

.enhanced-error-footer {
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-top: 1px solid var(--error-border);
  display: flex;
  justify-content: flex-end;
}

.enhanced-error-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.enhanced-error-btn-primary {
  background-color: var(--error-primary);
  color: white;
}

.enhanced-error-btn-primary:hover {
  background-color: var(--error-primary-dark);
}

.enhanced-error-btn-secondary {
  background-color: #f8f9fa;
  color: var(--error-text);
  border: 1px solid var(--error-border);
  margin-right: 0.5rem;
}

.enhanced-error-btn-secondary:hover {
  background-color: #e9ecef;
}

/* Animation for the error icon */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.enhanced-error-icon {
  animation: pulse 2s infinite;
}

/* Responsive styles */
@media (max-width: 576px) {
  .enhanced-error-dialog {
    max-width: 90%;
  }
  
  .enhanced-error-property {
    flex-direction: column;
  }
  
  .enhanced-error-property-name,
  .enhanced-error-property-value {
    width: 100%;
  }
  
  .enhanced-error-property-name {
    margin-bottom: 0.25rem;
  }
}
