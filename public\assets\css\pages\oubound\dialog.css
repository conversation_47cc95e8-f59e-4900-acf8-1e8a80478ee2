.confirmation-dialog {
    text-align: left;
    max-width: 450px;
    margin: 0 auto;
}
.invoice-icon i {
    font-size: 1.5rem;
}

.verification-list {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    text-align: left;
}
.form-check {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: flex-start;
}
.form-check:last-child {
    margin-bottom: 0;
}
.form-check-input {
    margin-top: 0.3rem;
}
.form-check-label {
    margin-left: 0.5rem;
    font-size: 0.95rem;
    line-height: 1.4;
}
.alert {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}
.badge {
    padding: 0.5em 0.8em;
}


/* Action Button Styles */
.outbound-action-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 1rem;
    font-weight: 500;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    min-width: 90px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.outbound-action-btn.submit {
    background-color: #405189;
    color: #fff;
}

.outbound-action-btn.submit:hover {
    background-color: #364574;
}

.outbound-action-btn.cancel {
    background-color: #dc2626;
    color: #fff;
}

.outbound-action-btn.cancel:hover {
    background-color: #b91c1c;
}

.outbound-action-btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}