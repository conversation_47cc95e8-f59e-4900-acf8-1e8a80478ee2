var AuthResponseModel = require(__dirname + "/../BQEmodels/AuthResponseModel.js");
var HttpHeaderModel = require(__dirname + "/../BQEmodels/HttpHeaderModel.js");
var HttpResponseModel = require(__dirname + "/../BQEmodels/HttpResponseModel.js");
var APIHelper = require(__dirname + "/../shared/APIHelper.js");
var GeneralMethods = require(__dirname + "/../shared/GeneralMethods.js");
const Result = require(__dirname + "/../shared/Result.js");

const fs = require('fs')

class AuthManager {


    constructor() {
        try {
            this.config = GeneralMethods.GetConfig();
            this.authResponse = new AuthResponseModel();
            this.httpResponse = new HttpResponseModel();
            this.httpHeader = new HttpHeaderModel();
            
            const savedAuth = this.GetAuthResponse();
            if (savedAuth) {
                this.authResponse = savedAuth;
            }
        } catch (error) {
            console.warn('Warning: Error initializing AuthManager:', error.message);
            // Continue with default/empty auth response
        }
    }

    ConnectToCore(req, res) {
        try {
            this.config = GeneralMethods.GetConfig();
            // Generate a random state parameter
            const state = Math.random().toString(36).substring(7);
            // Save state in session
            req.session.state = state;
            
            var url = this.config.authUri + "?";
            url += "response_type=code";
            url += "&client_id=" + this.config.clientId;
            url += "&redirect_uri=" + this.config.redirectUri;
            url += "&scope=" + this.config.scope;
            url += "&state=" + state;  // Add state parameter
            
            res.redirect(url);
        } catch (error) {
            throw error;
        }
    }

    DisconnectFromCore(callback) {
        try {
            // First try to revoke the token
            this.httpHeader.contentType = "application/x-www-form-urlencoded";   
            let data = 'token=' + this.authResponse.access_token + 
                      '&client_id=' + this.config.ClientID + 
                      '&client_secret=' + this.config.Secret;

            APIHelper.Post(this.config.CoreIdentityBaseUrl + '/connect/revocation', 
                data, 
                this.httpHeader, 
                (response) => {
                    this.httpResponse = response;
                    if (this.httpResponse.header_code === 200 || this.httpResponse.header_code === 204) {
                        // Clear the stored auth data
                        this.SaveAuthResponse(null);
                        callback(Result.Success, this.httpResponse);
                    } else {
                        callback(Result.Error, this.httpResponse);
                    }
                }
            );
        } catch (error) {
            console.error('Disconnect error:', error);
            callback(Result.Error, { body: error.message });
        }
    }

    Authorize(code, callback) {
        try {
            this.httpHeader.contentType = "application/x-www-form-urlencoded";

            let data = 'code=' + encodeURIComponent(code) + 
                      '&redirect_uri=' + encodeURIComponent(this.config.RedirectURI) + 
                      '&grant_type=authorization_code' + 
                      '&client_id=' + encodeURIComponent(this.config.ClientID) + 
                      '&client_secret=' + encodeURIComponent(this.config.Secret);

            console.log('Making token request with data:', {
                url: this.config.CoreIdentityBaseUrl + '/connect/token',
                code: code,
                redirect_uri: this.config.RedirectURI,
                client_id: this.config.ClientID
            });

            APIHelper.Post(this.config.CoreIdentityBaseUrl + '/connect/token', data, this.httpHeader, function(response) {
                this.httpResponse = response;
                console.log('Token response status:', this.httpResponse.header_code);
                
                if(this.httpResponse.header_code == 200) {
                    this.authResponse = JSON.parse(this.httpResponse.body);
                    console.log('Successfully obtained token');
                    callback(Result.Success, this.authResponse);             
                } else {
                    console.error('Token request failed:', this.httpResponse.body);
                    callback(Result.Error, this.httpResponse);
                }
            }.bind(this));

        } catch (error) {
            console.error('Authorization error:', error);
            callback(Result.Error, error);
        }
    }

    ReAuthorize(callback) {
        try {
            if(this.GetAuthResponse() != null) {
                let auth = this.GetAuthResponse();

                this.httpHeader.contentType = 'application/x-www-form-urlencoded';

                let data = 'refresh_token=' + auth.refresh_token + '&grant_type=refresh_token' + '&client_id=' + this.config.ClientID + '&client_secret=' + this.config.Secret;

                APIHelper.Post(this.config.CoreIdentityBaseUrl + '/connect/token', data, this.httpHeader, function(response){
                    this.httpResponse = response;
                    if(this.httpResponse.header_code == 200) {
                        this.authResponse = JSON.parse(this.httpResponse.body)
                        this.SaveAuthResponse(this.authResponse);
                        callback(Result.Success, this.authResponse);             
                    }      
                    else
                        callback(Result.Error, this.httpResponse);
                }.bind(this));
            }                                                        
        } catch (error) {
            throw new Error(error)
        }        
    }

    IsValidState(req, res, returnedState) {
        // Check if state matches what we stored in session
        if (!req.session.state || req.session.state !== returnedState) {
            return false;
        }
        // Clear the state after checking
        delete req.session.state;
        return true;
    }

    SaveAuthResponse(authResponse) {
        try {
            if (!authResponse) {
                if (fs.existsSync(__dirname + '/../AuthResponse.ini')) {
                    fs.unlinkSync(__dirname + '/../AuthResponse.ini');
                }
                return;
            }

            // Add creation timestamp if not present
            if (!authResponse.created_at) {
                authResponse.created_at = new Date().toISOString();
            }

            // Determine the correct endpoint based on region
            if (!authResponse.endpoint) {
                // Default to Australia endpoint if not specified
                authResponse.endpoint = 'https://api.bqecore.com/api';
            } else if (authResponse.endpoint.includes('api.bqecore.com')) {
                // If global endpoint is returned, switch to Australia endpoint
                authResponse.endpoint = 'https://api.bqecore.com/api';
            }

            // Ensure endpoint is properly formatted
            if (authResponse.endpoint.endsWith('/')) {
                authResponse.endpoint = authResponse.endpoint.slice(0, -1);
            }

            // Create directory if it doesn't exist
            const dir = __dirname + '/../';
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // Save the auth response
            fs.writeFileSync(__dirname + '/../AuthResponse.ini', JSON.stringify(authResponse), 'utf-8');
            
            console.log('Saved auth response:', {
                created_at: authResponse.created_at,
                expires_in: authResponse.expires_in,
                token_type: authResponse.token_type,
                endpoint: authResponse.endpoint // Log the endpoint
            });
        } catch (error) {
            console.error('Error saving auth response:', error);
            throw new Error('Failed to save authentication response: ' + error.message);
        }
    }

    GetAuthResponse() {
        try {
            if (!fs.existsSync(__dirname + '/../AuthResponse.ini')) {
                return null;
            }

            const authResponse = fs.readFileSync(__dirname + '/../AuthResponse.ini', 'utf8');
            if (!authResponse) {
                return null;
            }

            const parsed = JSON.parse(authResponse);
            
            // Add expiry time if not present
            if (parsed.expires_in && !parsed.expiryTime) {
                parsed.expiryTime = new Date(Date.now() + (parsed.expires_in * 1000)).toISOString();
            }

            return parsed;
        } catch (error) {
            console.error('Error reading auth response:', error);
            return null;
        }
    }

}
module.exports = AuthManager;