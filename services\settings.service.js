const { WP_SETTINGS } = require('../models');

class SettingsService {
    async getSettings(companyId) {
        try {
            // Get settings from database
            let settings = await WP_SETTINGS.findOne({
                where: { company_id: companyId }
            });

            // If no settings exist, create default settings
            if (!settings) {
                settings = await this.createDefaultSettings(companyId);
            }

            return {
                success: true,
                data: {
                    company: {
                        name: settings.company_name,
                        rocNumber: settings.roc_number,
                        taxNumber: settings.tax_number,
                        sstNumber: settings.sst_number,
                        address: settings.business_address,
                        contactEmail: settings.contact_email,
                        contactPhone: settings.contact_phone
                    },
                    api: {
                        environment: settings.api_environment,
                        key: settings.api_key,
                        secret: settings.api_secret,
                        endpoint: settings.api_endpoint,
                        useCustomEndpoint: settings.use_custom_endpoint,
                        timeout: settings.request_timeout,
                        verifySSL: settings.verify_ssl
                    },
                    invoice: {
                        format: settings.invoice_format,
                        currency: settings.currency,
                        autoConvert: settings.auto_convert
                    },
                    validation: {
                        validateInvoiceNo: settings.validate_invoice_no,
                        validateTaxId: settings.validate_tax_id,
                        validateDates: settings.validate_dates,
                        validateTotals: settings.validate_totals,
                        validateTaxCalc: settings.validate_tax_calc,
                        checkDuplicates: settings.check_duplicates,
                        validateCurrency: settings.validate_currency,
                        errorAction: settings.error_action
                    },
                    logging: {
                        level: settings.log_level,
                        storeLocal: settings.store_local,
                        retentionDays: settings.retention_days,
                        notifyErrors: settings.notify_errors,
                        notifyEmails: settings.notify_emails?.split(','),
                        monitorPerformance: settings.monitor_performance,
                        monitorQuota: settings.monitor_quota,
                        auditChanges: settings.audit_changes,
                        auditAccess: settings.audit_access
                    }
                }
            };
        } catch (error) {
            console.error('Error getting settings:', error);
            throw error;
        }
    }

    async saveSettings(companyId, settings) {
        try {
            // Convert settings object to database format
            const dbSettings = {
                company_id: companyId,
                // Company settings
                company_name: settings.company?.name,
                roc_number: settings.company?.rocNumber,
                tax_number: settings.company?.taxNumber,
                sst_number: settings.company?.sstNumber,
                business_address: settings.company?.address,
                contact_email: settings.company?.contactEmail,
                contact_phone: settings.company?.contactPhone,
                
                // API settings
                api_environment: settings.api?.environment,
                api_key: settings.api?.key,
                api_secret: settings.api?.secret,
                api_endpoint: settings.api?.endpoint,
                use_custom_endpoint: settings.api?.useCustomEndpoint,
                request_timeout: settings.api?.timeout,
                verify_ssl: settings.api?.verifySSL,
                
                // Invoice settings
                invoice_format: settings.invoice?.format,
                currency: settings.invoice?.currency,
                auto_convert: settings.invoice?.autoConvert,
                
                // Validation settings
                validate_invoice_no: settings.validation?.validateInvoiceNo,
                validate_tax_id: settings.validation?.validateTaxId,
                validate_dates: settings.validation?.validateDates,
                validate_totals: settings.validation?.validateTotals,
                validate_tax_calc: settings.validation?.validateTaxCalc,
                check_duplicates: settings.validation?.checkDuplicates,
                validate_currency: settings.validation?.validateCurrency,
                error_action: settings.validation?.errorAction,
                
                // Logging settings
                log_level: settings.logging?.level,
                store_local: settings.logging?.storeLocal,
                retention_days: settings.logging?.retentionDays,
                notify_errors: settings.logging?.notifyErrors,
                notify_emails: settings.logging?.notifyEmails?.join(','),
                monitor_performance: settings.logging?.monitorPerformance,
                monitor_quota: settings.logging?.monitorQuota,
                audit_changes: settings.logging?.auditChanges,
                audit_access: settings.logging?.auditAccess,
                
                updated_at: new Date()
            };

            // Update or create settings
            const [settings_record, created] = await WP_SETTINGS.upsert(dbSettings);

            return {
                success: true,
                message: created ? 'Settings created successfully' : 'Settings updated successfully'
            };
        } catch (error) {
            console.error('Error saving settings:', error);
            throw error;
        }
    }

    async createDefaultSettings(companyId) {
        try {
            return await WP_SETTINGS.create({
                company_id: companyId,
                // Default API settings
                api_environment: 'sandbox',
                request_timeout: 60,
                verify_ssl: true,
                
                // Default invoice settings
                invoice_format: 'json',
                currency: 'MYR',
                auto_convert: true,
                
                // Default validation settings
                validate_invoice_no: true,
                validate_tax_id: true,
                validate_dates: true,
                validate_totals: true,
                validate_tax_calc: true,
                check_duplicates: true,
                validate_currency: true,
                error_action: 'reject',
                
                // Default logging settings
                log_level: 'error',
                store_local: true,
                retention_days: 30,
                notify_errors: true,
                monitor_performance: true,
                monitor_quota: true,
                audit_changes: true,
                audit_access: true,
                
                created_at: new Date(),
                updated_at: new Date()
            });
        } catch (error) {
            console.error('Error creating default settings:', error);
            throw error;
        }
    }

    async testConnection(companyId) {
        try {
            const settings = await this.getSettings(companyId);
            
            // Test LHDN API connection using settings
            // This is a placeholder - implement actual API test
            const testResult = await this.testLHDNConnection(settings.data.api);
            
            return {
                success: true,
                message: 'Connection test successful',
                details: testResult
            };
        } catch (error) {
            console.error('Error testing connection:', error);
            throw error;
        }
    }

    async testLHDNConnection(apiSettings) {
        // Implement actual LHDN API connection test
        // This is a placeholder
        return {
            endpoint: apiSettings.endpoint,
            status: 'connected',
            latency: '120ms'
        };
    }
}

module.exports = new SettingsService(); 