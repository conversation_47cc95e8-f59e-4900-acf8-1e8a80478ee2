# 🚨 URGENT FIX - Inbound Database Error

## Problem
The inbound dashboard is failing because the database is missing `documentCurrency` and `currency` columns in the `WP_INBOUND_STATUS` table.

## ✅ IMMEDIATE SOLUTION (Choose One)

### Option 1: Quick Migration Script (RECOMMENDED)
```bash
npm run migrate:currency
```

### Option 2: Manual SQL (If script fails)
Run this SQL in your database management tool:

```sql
-- Add currency columns to WP_INBOUND_STATUS table
ALTER TABLE WP_INBOUND_STATUS ADD documentCurrency VARCHAR(10) NULL DEFAULT 'MYR';
ALTER TABLE WP_INBOUND_STATUS ADD currency VARCHAR(10) NULL DEFAULT 'MYR';

-- Update existing records
UPDATE WP_INBOUND_STATUS SET documentCurrency = 'MYR', currency = 'MYR' 
WHERE documentCurrency IS NULL OR currency IS NULL;

-- Verify columns were added
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'WP_INBOUND_STATUS' 
AND COLUMN_NAME IN ('documentCurrency', 'currency');
```

### Option 3: Sequelize CLI
```bash
npx sequelize-cli db:migrate
```

## 📋 STEP-BY-STEP INSTRUCTIONS

1. **Stop the application** (if running)
   ```bash
   # Stop the application
   pm2 stop eInvoice
   # OR if using nodemon
   Ctrl+C
   ```

2. **Run the migration** (choose one option above)
   ```bash
   npm run migrate:currency
   ```

3. **Restart the application**
   ```bash
   # Start the application
   pm2 start eInvoice
   # OR if using nodemon
   npm start
   ```

4. **Test the inbound dashboard**
   - Navigate to the inbound dashboard
   - Check if documents load without errors
   - Verify currency indicators appear correctly

## 🎯 WHAT WAS FIXED

✅ **Database Error**: Added missing currency columns
✅ **CF405 Postcode Error**: Comprehensive postcode cleaning (max 5 characters)  
✅ **Foreign Currency Display**: Added currency indicators
✅ **Temporary Workaround**: Application will work even before migration

## 🔧 FILES MODIFIED

- `models/WP_INBOUND_STATUS.js` - Added currency field definitions
- `routes/api/lhdn.routes.js` - Temporary fix + currency support
- `services/bqe/dataProcessor.js` - Postcode cleaning
- `services/bqe/mapper.js` - LHDN-compliant postcode mapping
- `scripts/run-migration.js` - Database migration tool
- `package.json` - Added migration scripts

## 🚀 VERIFICATION

After running the migration, you should see:
- ✅ Inbound dashboard loads without errors
- ✅ Documents display with currency indicators (MYR for now)
- ✅ No more database column errors in logs
- ✅ Postcode validation passes LHDN requirements

## 🆘 IF MIGRATION FAILS

If the migration script fails, you can:

1. **Check database connection**:
   ```bash
   # Verify .env file has correct database settings
   cat .env | grep DB_
   ```

2. **Run SQL manually** using your database management tool (SSMS, etc.)

3. **Contact support** with the error message

## 📞 SUPPORT

If you encounter any issues:
1. Check the application logs for specific error messages
2. Verify database connectivity
3. Ensure you have ALTER TABLE permissions on the database

The application is now configured to work both before and after the migration!
