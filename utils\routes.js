process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const fsp = require('fs').promises;


const bcrypt = require('bcrypt');
const saltRounds = 10;
const axios = require('axios');
const db = require('../models');  
const moment = require('moment');
const Sequelize = require('sequelize'); 
const https = require('https'); // Import https module
const router = express.Router();
const { User, DocHeader, WP_SUBMISSION_STATUS, WP_LOGS, WP_COMPANY_SETTINGS, WP_USER_REGISTRATION, WP_INBOUND_STATUS, WP_SFTP_CONFIG } = db;

// const xmlFolderPath = path.join(__dirname, '..', 'src', 'xml');
const xml2js = require('xml2js');
const NodeCache = require('node-cache');
const { logError } = require('../services/logger');
const { processExcelData } = require('./processExcelData');
const { validateExcelRows, processAndValidateExcel } = require('./validateExcelRows');
require('dotenv').config();

const axiosRetry = require('axios-retry').default;
axiosRetry(axios, { retries: 3, retryDelay: axiosRetry.exponentialDelay });
const { sequelize , Op } = require('sequelize');
const parser = new xml2js.Parser({ explicitArray: false, mergeAttrs: true });

const sftpService = require('../utils/sftpService');  // Make sure the path is correct

const lhdnService = require('../utils/lhdnService');  

// Configure paths
const excelOutboundPath = path.join(__dirname, '../excel/outbound');
const jsonOutputPath = path.join(__dirname, '../output/json');


// Define base directory - this should be your project root
const baseDir = path.join(__dirname, '..');
console.log('Base directory:', baseDir);

// Configure all required paths
const paths = {
  outbound: path.join(__dirname, '..', 'excel', 'outbound'),
  logs: path.join(baseDir, 'logs'),
  processLogs: path.join(baseDir, 'logs', 'process'),
  errorLogs: path.join(baseDir, 'logs', 'error'),
  jsonOutput: path.join(baseDir, 'logs', 'json')
};

// Create directories if they don't exist
Object.values(paths).forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

const MAX_RETRIES = 3;
const INITIAL_RETRY_DELAY = 1000; // 1 second

// Helper function for delay
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Add this retry wrapper function
async function retryRequest(requestFn, retries = MAX_RETRIES, baseDelay = INITIAL_RETRY_DELAY) {
  for (let i = 0; i < retries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      if (error.response?.status === 429 && i < retries - 1) {
        const waitTime = baseDelay * Math.pow(2, i); // Exponential backoff
        console.log(`Rate limited. Retrying in ${waitTime}ms...`);
        await delay(waitTime);
        continue;
      }
      throw error;
    }
  }
}

const cache = new NodeCache({ stdTTL: 600, checkperiod: 120 }); // Cache for 10 minutes

const agent = new https.Agent({
  rejectUnauthorized: false,
  secureProtocol: 'TLSv1_2'  // Forcing TLSv1.2
});


const { API_BASE_URL } = process.env;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../public/images');
    // Ensure the directory exists
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});
const upload = multer({ storage });

// FUNCTIONS //


//       if (err) {
//         return reject(err);
//       }
//       xml2js.parseString(data, (err, result) => {
//         if (err) {
//           return reject(err);
//         }
//         resolve(result);
//       });
//     });
//   });
// };


// const parseXmlFile = (filePath) => {
//   return new Promise((resolve, reject) => {
//     fs.readFile(filePath, 'utf8', (err, data) => {
//       if (err) {
//         return reject(new Error(`Failed to read file: ${filePath}, ${err.message}`));
//       }

//       xml2js.parseString(data, (err, result) => {
//         if (err) {
//           return reject(new Error(`XML Parsing Error in file ${filePath}: ${err.message}`));
//         }

//         resolve(result);
//       });
//     });
//   });
// };

// Fetch activity logs
router.get('/api/logs', async (req, res) => {
  try {
    const logs = await WP_LOGS.findAll();
    res.json(logs);
  } catch (error) {
    res.status(500).send(error.message);
  }
});






const fetchRecentDocuments = async (req) => {
  const allDocuments = [];
  let pageNo = 1; 
  const pageSize = 100;
  let totalPages = 999; 
  let hasMorePages = true;

  // Add delay function
  const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

  const config = {
    method: 'get',
    headers: {
      'Authorization': `Bearer ${req.session.accessToken}`
    },
    timeout: 30000 // Increase timeout to 30 seconds
  };

  try {
    while (pageNo <= totalPages && hasMorePages) {
      try {
        // Add delay between requests to avoid rate limiting
        await delay(1000); // 1 second delay between requests

        config.url = `https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/recent?pageNo=${pageNo}&pageSize=${pageSize}`;

        const response = await axios(config);
        const data = response.data;

        // Debug log to check raw API response
        console.log('Raw API Response:', JSON.stringify(data.result[0], null, 2));

        if (!data.result || data.result.length === 0) {
          console.log(`No more documents found on page ${pageNo}.`);
          hasMorePages = false;
          break;
        }

        const mappedDocuments = data.result.map(doc => {
          // Debug log for each document's financial values
          console.log('Document financial values:', {
            uuid: doc.uuid,
            total: doc.total,
            totalSales: doc.totalSales,
            netAmount: doc.netAmount
          });

          return {
            ...doc,
            typeName: doc.typeName,
            typeVersionName: doc.typeVersionName,
            totalSales: doc.total || doc.totalSales || doc.netAmount || 0 // Use total as primary value
          };
        });

        allDocuments.push(...mappedDocuments);
        console.log(`Fetched page ${pageNo} with ${data.result.length} documents.`);

        if (data.meta && data.meta.totalPages) {
          totalPages = data.meta.totalPages;
        }

        pageNo++;
      } catch (error) {
        if (error.response?.status === 429) {
          // If rate limited, wait for longer and retry
          const retryAfter = parseInt(error.response.headers['retry-after']) || 60;
          console.log(`Rate limited. Waiting ${retryAfter} seconds before retry...`);
          await delay(retryAfter * 1000);
          // Don't increment pageNo, retry the same page
          continue;
        }
        throw error; // Throw other errors
      }
    }

    return { result: allDocuments };
  } catch (error) {
    console.error('Error fetching recent documents:', error);
    throw error;
  }
};



const getCachedDocuments = async (req) => {
  const cacheKey = 'recentDocuments';
  
  let data = cache.get(cacheKey);

  if (!data) {
    // If data is not in the cache, fetch it from the source
    data = await fetchRecentDocuments(req);
    
    // Store the fetched data in the cache
    cache.set(cacheKey, data);
    
    console.log('Fetched documents and cached the result');
  } else {
    console.log('Serving documents from cache');
  }

  return data;
};

const saveInboundStatus = async (data) => {
  try {
    if (!data.result || !Array.isArray(data.result)) {
      console.warn("No valid data to process");
      return;
    }

    for (const item of data.result) {
      try {
        // Parse dates properly
        const dateTimeReceived = item.dateTimeReceived ? new Date(item.dateTimeReceived) : null;
        const dateTimeValidated = item.dateTimeValidated ? new Date(item.dateTimeValidated) : null;
        const dateTimeIssued = item.dateTimeIssued ? new Date(item.dateTimeIssued) : null;
        const cancelDateTime = item.cancelDateTime ? new Date(item.cancelDateTime) : null;
        const rejectRequestDateTime = item.rejectRequestDateTime ? new Date(item.rejectRequestDateTime) : null;

        // Convert numeric values
        const totalExcludingTax = parseFloat(item.totalExcludingTax) || null;
        const totalDiscount = parseFloat(item.totalDiscount) || null;
        const totalNetAmount = parseFloat(item.totalNetAmount) || null;
        const totalPayableAmount = parseFloat(item.totalPayableAmount) || null;

        // Create/update record with exact field matches
        await WP_INBOUND_STATUS.upsert({
          uuid: item.uuid,
          submissionUid: item.submissionUid || null,
          longId: item.longId || null,
          internalId: item.internalId || null,
          typeName: item.typeName || null,
          typeVersionName: item.typeVersionName || null,
          issuerTin: item.issuerTin || null,
          issuerName: item.issuerName || null,
          receiverId: item.receiverId || null,
          receiverName: item.receiverName || null,
          dateTimeReceived,
          dateTimeValidated,
          status: item.status || null,
          documentStatusReason: item.documentStatusReason || null,
          cancelDateTime,
          rejectRequestDateTime,
          createdByUserId: item.createdByUserId || null,
          dateTimeIssued,
          totalExcludingTax,
          totalDiscount,
          totalNetAmount,
          totalPayableAmount
        });

        console.log(`Successfully processed item with uuid: ${item.uuid}`);

      } catch (err) {
        console.error(`Error processing item with uuid ${item.uuid}:`, err);
        console.error('Item data:', JSON.stringify(item, null, 2));
      }
    }
  } catch (error) {
    console.error('Error in saveInboundStatus:', error);
    throw error;
  }
};


// const saveInboundStatus = async (data) => {
//   try {
//     if (!data.result || data.result.length === 0) {
//       console.warn("No inbound statuses to process");
//       return;
//     }

//     const inboundStatuses = data.result;

//     for (const status of inboundStatuses) {
//       try {
//         const {
//           submissionUid,
//           internalId,
//           typeName,
//           supplierName,
//           receiverName,
//           dateTimeIssued,
//           dateTimeReceived,
//           totalSales,
//           status: statusLabel, // This should capture statuses like "Cancelled"
//           uuid,
//         } = status;

//         // Ensure submissionUid is not null or empty
//         if (!submissionUid) {
//           console.warn(`Skipping record with missing submissionUid: ${JSON.stringify(status)}`);
//           continue; // Skip this record
//         }

//         // Log the document being processed
//         console.log(`Processing submissionUid: ${submissionUid}`);
//         console.log(`Processing uuid: ${uuid}`);

//         // Check if the record exists by submissionUid
//         const existingStatus = await WP_INBOUND_STATUS.findOne({
//           where: { submissionUid }
//         });

//         if (existingStatus) {
//           // Update the existing record
//           await WP_INBOUND_STATUS.update(
//             {
//               internalId,
//               typeName,
//               supplierName,
//               receiverName,
//               dateTimeIssued,
//               dateTimeReceived,
//               totalSales: parseFloat(totalSales),
//               status: statusLabel,
//               uuid,
//             },
//             { where: { submissionUid } } // Update by submissionUid
//           );
//           console.log(`Updated status for submissionUid ${submissionUid}`);
//         } else {
//           console.warn(`No record found for submissionUid ${submissionUid}. Skipping update.`);
//         }
//       } catch (docError) {
//         // Log detailed document processing errors
//         console.error(`Error processing document for SubmissionUID ${status.submissionUid}: ${docError.message}`, docError.stack);
//       }
//     }
//   } catch (error) {
//     console.error(`Error in saveInboundStatus: ${error.message}`, error.stack);
//     throw error; // Rethrow the error to be caught by the caller
//   }
// };

// const getCachedDocuments = async (req) => {
//   const cacheKey = 'recentDocuments';
//   let data = cache.get(cacheKey);

//   if (!data) {
//     data = await fetchRecentDocuments(req);
//     cache.set(cacheKey, data);
//   }

//   return data;
// };

// const fetchRecentDocuments = async (req) => {
//   const config = {
//     method: 'get',
//     url: 'https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/recent?pageNo=1&pageSize=100',
//     headers: {
//       'Authorization': `Bearer ${req.session.accessToken}`
//     },
//     timeout: 10000 // Increase timeout to 10 seconds
//   };

//   for (let attempt = 1; attempt <= 3; attempt++) {
//     try {
//       const response = await axios(config);
//       return response.data;
//     } catch (error) {
//       if (attempt === 3) {
//         console.error('Error fetching recent documents:', error);
//         throw error;
//       }
//       //console.log(`Attempt ${attempt} failed. Retrying...`);
//     }
//   }
// };



// const saveInboundStatus = async (data) => {
//   try {
//     if (!data.result || data.result.length === 0) {
//       console.warn("No inbound statuses to process");
//       return;
//     }

//     const inboundStatuses = data.result;

//     for (const status of inboundStatuses) {
//       try {
//         const {
//           submissionUid,
//           internalId,
//           typeName,
//           supplierName,
//           receiverName,
//           dateTimeIssued,
//           dateTimeReceived,
//           totalSales,
//           status: statusLabel,
//           uuid,
//         } = status;

//         // Ensure submissionUid is not null or empty
//         if (!submissionUid) {
//           console.warn(`Skipping record with missing submissionUid: ${JSON.stringify(status)}`);
//           continue; // Skip this record
//         }

//         // Log the document being processed
//         console.log(`Processing submissionUid: ${submissionUid}`);

//          // Log the document being processed
//          console.log(`Processing uuid: ${uuid}`);

//         // Check if the record exists by submissionUid
//         const existingStatus = await WP_INBOUND_STATUS.findOne({
//           where: { submissionUid }
//         });

//         if (!existingStatus) {
//           // Create the record if it doesn't exist
//           await WP_INBOUND_STATUS.create({
//             submissionUid,
//             internalId,
//             typeName,
//             supplierName,
//             receiverName,
//             dateTimeIssued,
//             dateTimeReceived,
//             totalSales: parseFloat(totalSales),
//             status: statusLabel,
//             uuid,
//           });
//           console.log(`Created status for submissionUid ${submissionUid}`);
//         } else {
//           console.log(`SubmissionUid ${submissionUid} already exists. Skipping.`);
//         }
//       } catch (docError) {
//         // Log detailed document processing errors
//         console.error(`Error processing document for SubmissionUID ${status.submissionUid}: ${docError.message}`, docError.stack);
//       }
//     }
//   } catch (error) {
//     console.error(`Error in saveInboundStatus: ${error.message}`, error.stack);
//     throw error; // Rethrow the error to be caught by the caller
//   }
// };



router.get('/api/documents/recent', async (req, res) => {
  try {
    // Get auth token using lhdnService
    const authResponse = await lhdnService.getTokenAsTaxPayer(req);
    if (!authResponse?.access_token) {
      throw new Error('Failed to get authentication token');
    }

    // Get current user's TIN from session
    const currentUser = req.session.user;
    if (!currentUser?.TIN) {
      res.redirect('/login');
      return;
    }

    // Fetch from MyInvois API
    const response = await axios.get(
      `${process.env.API_BASE_URL}/api/v1.0/documents/recent?pageSize=100`, 
      {
        headers: {
          'Authorization': `Bearer ${authResponse.access_token}`,
          'X-Invoicing-TIN': currentUser.TIN // Add TIN to headers
        }
      }
    );

    if (!response.data?.result) {
      return res.json({
        success: true,
        result: []
      });
    }

    // Map API response to our database structure
    const documents = response.data.result.map(doc => ({
      uuid: doc.uuid,
      internalId: doc.internalId, 
      typeName: doc.typeName,
      supplierName: doc.supplierName,
      receiverName: doc.receiverName,
      dateTimeIssued: doc.dateTimeIssued,
      dateTimeReceived: doc.dateTimeReceived,
      status: doc.status,
      totalSales: doc.totalSales || doc.total || doc.netAmount || 0,
      longId: doc.longId,
      typeVersionName: doc.typeVersionName
    }));

    // Save to WP_INBOUND_STATUS with TIN
    for (const doc of documents) {
      await WP_INBOUND_STATUS.upsert({
        ...doc,
        TIN: currentUser.TIN,
        uuid: doc.uuid  // Make sure uuid is included
      });
    }

    // Return the results
    res.json({
      success: true,
      result: documents
    });

  } catch (error) {
    console.error('Error fetching documents:', error);
    res.status(500).json({ 
      success: false, 
      error: {
        message: error.message,
        name: error.name,
        details: error.response?.data?.error || error.original?.message || null
      }
    });
  }
});



router.get('/api/documents/recent-total', async (req, res) => {
  try {
    const data = await getCachedDocuments(req);
    const totalCount = data.metadata.totalCount;
    res.json({ totalCount });
  } catch (error) {
    res.json({ totalCount: 0, success: false, message: 'Failed to fetch recent documents' });
  }
});

// const saveInboundStatus = async (data) => {
//   try {
//     if (!data.result) {
//       throw new Error("Expected data.result to be an array");
//     }

//     const inboundStatuses = data.result;
//     // console.log('Inbound statuses to process:', inboundStatuses.length);

//     for (const status of inboundStatuses) {
//       try {
//         const {
//           dateTimeIssued: IssuedDate,
//           dateTimeReceived: ReceivedDate,
//           status: InboundStatus,
//           typeName: InvoiceType,
//           internalId: InvoiceID
//         } = status;

//         // console.log('Processing status:', { IssuedDate, ReceivedDate, InboundStatus, InvoiceType, InvoiceID });

//         if (!InvoiceID) {
//           const errorMessage = `Missing InvoiceID: ${JSON.stringify(status)}`;
//           console.error(errorMessage);
//           logError(errorMessage);
//           continue; // Skip this document if InvoiceID is missing
//         }

//         // Check if the record exists
//         const existingStatus = await WP_INBOUND_STATUS.findOne({
//           where: { InvoiceID }
//         });

//         if (!existingStatus) {
//           // If the record does not exist, create it
//           await WP_INBOUND_STATUS.create({
//             IssuedDate,
//             ReceivedDate,
//             InboundStatus,
//             InvoiceType,
//             InvoiceID
//           });
//           // console.log(`Created status for InvoiceID ${InvoiceID}`);
//         } else {
//           // console.log(`Status for InvoiceID ${InvoiceID} already exists`);
//         }
//       } catch (docError) {
//         const errorMessage = `Error processing document: ${JSON.stringify(status)} - ${docError.message}`;
//         console.error(errorMessage, docError.stack);
//         logError(errorMessage);
//       }
//     }
//   } catch (error) {
//     const errorMessage = `Error in saveInboundStatus: ${error.message}`;
//     console.error(errorMessage, error.stack);
//     logError(errorMessage);
//     throw error; // Rethrow the error to be caught by the caller
//   }
// };

// const saveInboundStatus = async (data) => {
//   try {
//     if (!data.result) {
//       throw new Error("Expected data.result to be an array");
//     }

//     const inboundStatuses = data.result;
//     console.log('Inbound statuses to process:', inboundStatuses.length);

//     for (const status of inboundStatuses) {
//       try {
//         const {
//           dateTimeIssued: IssuedDate,
//           dateTimeReceived: ReceivedDate,
//           status: InboundStatus,
//           typeName: InvoiceType,
//           internalId: InvoiceID
//         } = status;

//         console.log('Processing status:', { IssuedDate, ReceivedDate, InboundStatus, InvoiceType, InvoiceID });

//         if (!InvoiceID) {
//           const errorMessage = `Missing InvoiceID: ${JSON.stringify(status)}`;
//           console.error(errorMessage);
//           logError(errorMessage);
//           continue; // Skip this document if InvoiceID is missing
//         }

//         // Insert the record (without checking if it exists)
//         await WP_INBOUND_STATUS.create({
//           IssuedDate,
//           ReceivedDate,
//           InboundStatus,
//           InvoiceType,
//           InvoiceID
//         });

//         console.log(`Processed status for InvoiceID ${InvoiceID}`);
//       } catch (docError) {
//         const errorMessage = `Error processing document: ${JSON.stringify(status)} - ${docError.message}`;
//         console.error(errorMessage, docError.stack);
//         logError(errorMessage);
//       }
//     }
//   } catch (error) {
//     const errorMessage = `Error in saveInboundStatus: ${error.message}`;
//     console.error(errorMessage, error.stack);
//     logError(errorMessage);
//     throw error; // Rethrow the error to be caught by the caller
//   }
// };

// router.get('/api/documents/recent', async (req, res) => {
//   try {
//     const data = await getCachedDocuments(req);
//     console.log('Fetched data:', data);

//     await saveInboundStatus(data);
//     console.log('Successfully saved inbound statuses.');

//     res.json(data);
//   } catch (error) {
//     const errorMessage = `Error in /api/documents/recent: ${error.message}`;
//     console.error(errorMessage, error.stack);
//     logError(errorMessage);
//     res.status(500).json({ success: false, message: 'Failed to fetch recent documents' });
//   }
// });







// router.get('/api/documents/recent', async (req, res) => {
//   const fetchRecentDocuments = async () => {
//     const config = {
//       method: 'get',
//       url: 'https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/recent?pageNo=1&pageSize=100',
//       headers: {
//         'Authorization': `Bearer ${req.session.accessToken}`
//       },
//       timeout: 10000 // Increase timeout to 10 seconds
//     };

//     for (let attempt = 1; attempt <= 3; attempt++) {
//       try {
//         const response = await axios(config);
//         return response.data;
//       } catch (error) {
//         if (attempt === 3) {
//           console.error('Error fetching recent documents:', error);
//           throw error;
//         }
//         console.log(`Attempt ${attempt} failed. Retrying...`);
//       }
//     }
//   };

//   try {
//     const data = await fetchRecentDocuments();
//     console.log('Response from API:', data);
//     res.json(data);
//   } catch (error) {
//     res.status(500).json({ success: false, message: 'Failed to fetch recent documents' });
//   }
// });


// router.get('/api/documents/recent-total', async (req, res) => {
//   const fetchRecentDocuments = async () => {
//     const config = {
//       method: 'get',
//       url: 'https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/recent?pageNo=1&pageSize=100',
//       headers: {
//         'Authorization': `Bearer ${req.session.accessToken}`
//       },
//       timeout: 10000 // Increase timeout to 10 seconds
//     };

//     for (let attempt = 1; attempt <= 3; attempt++) {
//       try {
//         const response = await axios(config);
//         return response.data;
//       } catch (error) {
//         if (attempt === 3) {
//           console.error('Error fetching recent documents:', error);
//           throw error;
//         }
//         console.log(`Attempt ${attempt} failed. Retrying...`);
//       }
//     }
//   };

//   try {
//     const data = await fetchRecentDocuments();
//     const totalCount = data.metadata.totalCount;

//     res.json({ totalCount });
//   } catch (error) {
//     res.json({ totalCount: 0, success: false, message: 'Failed to fetch recent documents' });
//   }
// });
// router.get('/api/documents/recent-total', async (req, res) => {
//   try {
//     const response = await axios.get('https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/recent', {
//       headers: {
//         'Authorization': `Bearer ${req.session.accessToken}`
//       }
//     });
    
//     const totalCount = response.data.metadata.totalCount; 
//     res.json({
//       totalCount: totalCount
//     });
//   } catch (error) {
//     console.error('Error fetching recent documents:', error);
//     res.status(500).json({ success: false, message: 'Failed to fetch recent documents' });
//   }
// });


// // Fetch recent documents
// router.get('/api/documents/recent-total', async (req, res) => {
//   try {
//     const response = await axios.get('https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/recent?pageNo=1&pageSize=100', {
//       headers: {
//         'Authorization': `Bearer ${req.session.accessToken}`
//       }
//     });
    
//     const totalCount = response.data.metadata.totalCount; 
//     res.json({
//       documents: response.data.documents,
//       totalCount: totalCount
//     });
//   } catch (error) {
//     console.error('Error fetching recent documents:', error);
//     res.status(500).json({ success: false, message: 'Failed to fetch recent documents' });
//   }
// });




// Reject document by UUID
router.post('/api/reject-invoice', async (req, res) => {
  const { uuid, reason } = req.body;

  //console.log(uuid);

  if (!uuid || !reason) {
    return res.status(400).json({ success: false, message: 'UUID and reason are required' });
  }

  try {
    const response = await axios.put(
      `${API_BASE_URL}/api/v1.0/documents/state/${uuid}/state`,
      {
        status: 'rejected',
        reason: reason
      },
      {
        headers: {
          'Authorization': `Bearer ${req.session.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('Invoice rejected successfully:', response.data);
    res.json({ success: true, message: 'Invoice rejected successfully' });
  } catch (error) {
    if (error.response) {
      // Extract and display the specific error message
      const errorData = error.response.data;
      console.error('Error details:', JSON.stringify(errorData, null, 2));
      
      if (errorData.error && errorData.error.code === 'AccessDenied') {
        res.status(403).json({
          success: false,
          message: 'Access Denied: You are not authorized to update document status.',
          error: errorData.error.details
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to reject invoice',
          error: errorData
        });
      }
    } else {
      console.error('Error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to reject invoice',
        error: error.message
      });
    }
  }
});


// router.get('/api/documents/recent', async (req, res) => {
//   try {
//     const data = await getCachedDocuments(req);
//     // console.log('Fetched data:', data);

//     await saveInboundStatus(data);
//     // console.log('Successfully saved inbound statuses.');

//     res.json(data);
//   } catch (error) {
//     const errorMessage = `Error in /api/documents/recent: ${error.message}`;
//     console.error(errorMessage, error.stack);
//     logError(errorMessage);
//     res.status(500).json({ success: false, message: 'Failed to fetch recent documents' });
//   }
// });





// Route to fetch XML file data
// router.get('/api/xml', async (req, res) => {
//   try {
//     const files = await fs.promises.readdir(xmlFolderPath);
//     const xmlData = await Promise.all(files.map(async (file) => {
//       const filePath = path.join(xmlFolderPath, file);
//       const xmlContent = await parseXmlFile(filePath);
//       const invoiceId = xmlContent.Invoice['cbc:ID'][0];
//       const dateUploaded = getFileCreationDate(filePath).toISOString().split('T')[0];
      
//       // Fetch submission data from WP_SUBMISSION_STATUS
//       const submissionStatus = await WP_SUBMISSION_STATUS.findOne({
//         where: { DocNum: invoiceId }
//       });

//       return {
//         id: invoiceId,
//         fileName: file,
//         filePath: `/xml/${file}`,
//         dateUploaded,
//         dateSubmitted: submissionStatus ? submissionStatus.DateTimeSent : '',
//         status: submissionStatus ? submissionStatus.SubmissionStatus : 'Pending',
//         uuid: submissionStatus ? submissionStatus.UUID : '' // Add uuid to response
//       };
//     }));

//     res.json(xmlData);
//   } catch (error) {
//     console.error('Error reading XML files:', error);
//     res.status(500).json({ error: 'Internal Server Error' });
//   }
// });

// router.get('/api/xml', async (req, res) => {
//   try {
//     const files = await fs.promises.readdir(xmlFolderPath);
//     const xmlData = await Promise.all(files.map(async (file) => {
//       const filePath = path.join(xmlFolderPath, file);

//       try {
//         const xmlContent = await parseXmlFile(filePath);
//         const invoiceId = xmlContent.Invoice?.['cbc:ID']?.[0];
//         const dateUploaded = getFileCreationDate(filePath).toISOString().split('T')[0];
        
//         // Fetch submission data from WP_SUBMISSION_STATUS
//         const submissionStatus = await WP_SUBMISSION_STATUS.findOne({
//           where: { DocNum: invoiceId }
//         });

//         return {
//           id: invoiceId,
//           fileName: file,
//           filePath: `/xml/${file}`,
//           dateUploaded,
//           dateSubmitted: submissionStatus ? submissionStatus.DateTimeSent : '',
//           status: submissionStatus ? submissionStatus.SubmissionStatus : 'Pending',
//           uuid: submissionStatus ? submissionStatus.UUID : '' // Add uuid to response
//         };
//       } catch (error) {
//         console.error(`Error parsing file ${file}:`, error.message); // Log which file is causing issues
//         return null; // Return null for problematic files
//       }
//     }));

//     // Filter out null entries (problematic files)
//     const filteredData = xmlData.filter(item => item !== null);
//     res.json(filteredData);
//   } catch (error) {
//     console.error('Error reading XML files:', error);
//     res.status(500).json({ error: 'Internal Server Error' });
//   }
// });

// router.get('/api/xml', async (req, res) => {
//   try {
//     const { type } = req.query;
//     const files = await fs.promises.readdir(xmlFolderPath);

//     const xmlData = await Promise.all(
//       files.map(async (file) => {
//         const filePath = path.join(xmlFolderPath, file);

//         try {
//           const xmlContentRaw = await fs.promises.readFile(filePath, 'utf-8');
//           const xmlContent = await parser.parseStringPromise(xmlContentRaw);

//           console.log('Parsed XML Content for file:', file, xmlContent); // Log to see the full structure

//           // Improved extraction of InvoiceTypeCode using xml2js
//           let invoiceTypeCode = '01'; // Default to '01'
//           const invoiceTypeCodeElement = xmlContent.Invoice?.['cbc:InvoiceTypeCode']?.['_'] || xmlContent.Invoice?.['cbc:InvoiceTypeCode'];
//           if (invoiceTypeCodeElement) {
//             invoiceTypeCode = invoiceTypeCodeElement.trim(); // Ensure proper extraction
//             console.log("InvoiceTypeCode: ", invoiceTypeCode)
//           } else {
//             console.error(`InvoiceTypeCode not found in file: ${file}`);
//           }

//           const invoiceId = xmlContent.Invoice?.['cbc:ID'] || 'Unknown';
//           const dateUploaded = getFileCreationDate(filePath).toISOString().split('T')[0];

//           const accountingCustomerParty = xmlContent.Invoice?.['cac:AccountingCustomerParty']?.['cac:Party']?.['cac:PartyLegalEntity']?.['cbc:RegistrationName'] || 'Unknown';

//           const submissionStatus = await WP_SUBMISSION_STATUS.findOne({
//             where: { DocNum: invoiceId }
//           });

//           return {
//             id: invoiceId,
//             fileName: file,
//             filePath: `/xml/${file}`,
//             dateUploaded,
//             dateSubmitted: submissionStatus ? submissionStatus.DateTimeSent : '',
//             status: submissionStatus ? submissionStatus.SubmissionStatus : 'Pending',
//             uuid: submissionStatus ? submissionStatus.UUID : '',
//             registrationName: accountingCustomerParty,
//             invoiceType: invoiceTypeCode // Add this to differentiate between tabs
//           };
//         } catch (error) {
//           console.error(`Error parsing file ${file}:`, error.message);
//           return null;
//         }
//       })
//     );

//     const filteredData = xmlData.filter((item) => item !== null && item.invoiceType === type);
//     res.json(filteredData);
//   } catch (error) {
//     console.error('Error reading XML files:', error);
//     res.status(500).json({ error: 'Internal Server Error' });
//   }
// });




// router.get('/api/xml', async (req, res) => {
//   try {
//     const files = await fs.promises.readdir(xmlFolderPath);
//     const xmlData = await Promise.all(files.map(async (file) => {
//       const filePath = path.join(xmlFolderPath, file);

//       try {
//         const xmlContent = await parseXmlFile(filePath);

//         // Extract Invoice ID and uploaded date
//         const invoiceId = xmlContent.Invoice?.['cbc:ID']?.[0];
//         const dateUploaded = getFileCreationDate(filePath).toISOString().split('T')[0];

//         // Extract Registration Name and TIN (Tax Identification Number)
//         const accountingCustomerParty = xmlContent.Invoice?.['cac:AccountingCustomerParty']?.[0];
//         const registrationName = accountingCustomerParty?.['cac:Party']?.[0]?.['cac:PartyLegalEntity']?.[0]?.['cbc:RegistrationName']?.[0] || 'Unknown';
        
//         // Check if the TIN exists with schemeID="TIN"
//         const tinElement = accountingCustomerParty?.['cac:Party']?.[0]?.['cac:PartyIdentification']?.find(party => party?.['cbc:ID']?.[0]?._attributes?.schemeID === 'TIN');
//         const tin = tinElement ? tinElement['cbc:ID']?.[0]?._text || 'Unknown' : 'Unknown';

//         // Fetch submission data from WP_SUBMISSION_STATUS
//         const submissionStatus = await WP_SUBMISSION_STATUS.findOne({
//           where: { DocNum: invoiceId }
//         });

//         return {
//           id: invoiceId,
//           fileName: file,
//           filePath: `/xml/${file}`,
//           dateUploaded,
//           dateSubmitted: submissionStatus ? submissionStatus.DateTimeSent : '',
//           status: submissionStatus ? submissionStatus.SubmissionStatus : 'Pending',
//           uuid: submissionStatus ? submissionStatus.UUID : '',
//           registrationName,  // Add registration name to the response
//           tin                // Add TIN to the response
//         };
//       } catch (error) {
//         console.error(`Error parsing file ${file}:`, error.message); // Log the error
//         return null; // Return null for problematic files
//       }
//     }));

//     // Filter out null entries (problematic files)
//     const filteredData = xmlData.filter(item => item !== null);
//     res.json(filteredData);
//   } catch (error) {
//     console.error('Error reading XML files:', error);
//     res.status(500).json({ error: 'Internal Server Error' });
//   }
// });



// New route to cancel the invoice
// Cancel document by UUID
// router.post('/api/cancel-invoice', async (req, res) => {
//   const { uuid } = req.body;

//   if (!uuid) {
//     return res.status(400).json({ success: false, message: 'UUID is required' });
//   }

//   try {
//     const response = await axios.put(
//       `${API_BASE_URL}/api/v1.0/documents/state/${uuid}/state`,
//       {
//         status: 'cancelled',
//         reason: 'some reason for cancelled document'
//       },
//       {
//         headers: {
//           'Authorization': `Bearer ${req.session.accessToken}`,
//           'Content-Type': 'application/json',
//         },
//       }
//     );

//     console.log('Invoice cancelled successfully:', response.data);
//     res.json({ success: true, message: 'Invoice cancelled successfully' });

//   } catch (error) {
//     if (error.response) {
//       // Extract and display the specific error message
//       const errorData = error.response.data;
//       console.error('Error details:', JSON.stringify(errorData, null, 2));
//       res.status(500).json({
//         success: false,
//         message: 'Failed to cancel invoice',
//         error: errorData
//       });
//     } else {
//       console.error('Error:', error.message);
//       res.status(500).json({
//         success: false,
//         message: 'Failed to cancel invoice',
//         error: error.message
//       });
//     }
//   }
// });

// router.post('/api/cancel-invoice', async (req, res) => {
//   const { uuid } = req.body;

//   if (!uuid) {
//     return res.status(400).json({ success: false, message: 'UUID is required' });
//   }

//   try {
//     const response = await axios.put(
//       `${API_BASE_URL}/api/v1.0/documents/state/${uuid}/state`,
//       {
//         status: 'cancelled',
//         reason: 'some reason for cancelled document'
//       },
//       {
//         headers: {
//           'Authorization': `Bearer ${req.session.accessToken}`,
//           'Content-Type': 'application/json',
//         },
//       }
//     );

//     console.log('Invoice cancelled successfully:', response.data);
//     res.json({ success: true, message: 'Invoice cancelled successfully' });

//   } catch (error) {
//     if (error.response) {
//       const errorData = error.response.data;

//       // Check if the error is due to the document already being cancelled
//       const isAlreadyCancelled = errorData?.error?.code === 'ValidationError' &&
//         errorData?.error?.details?.some(detail =>
//           detail.code === 'IncorrectState' &&
//           detail.message === 'The document is already cancelled.'
//         );

//       if (isAlreadyCancelled) {
//         console.log('Document is already cancelled, marking as cancelled.');
//         return res.json({ success: true, message: 'The document was already cancelled.' });
//       }

//       console.error('Error details:', JSON.stringify(errorData, null, 2));
//       res.status(500).json({
//         success: false,
//         message: 'Failed to cancel invoice',
//         error: errorData
//       });

//     } else {
//       console.error('Error:', error.message);
//       res.status(500).json({
//         success: false,
//         message: 'Failed to cancel invoice',
//         error: error.message
//       });
//     }
//   } finally {
//     // Log the action regardless of success or failure
//     await saveLog(`Attempt to cancel invoice ${invoiceId}.`, loggedUser);
//   }
// });



router.post('/api/cancel-invoice', async (req, res) => {
  const loggedUser = req.session.user?.username;
  const { uuid, id } = req.body;

  console.log('Cancel request received:', { uuid, id, loggedUser });

  if (!uuid || !id) {
    return res.status(400).json({ 
      success: false, 
      message: 'Missing required parameters: uuid or id' 
    });
  }

  try {
    // First check if the document exists
    const documentResponse = await axios.get(
      `${API_BASE_URL}/api/v1.0/documents/${uuid}/details`,
      {
        headers: {
          'Authorization': `Bearer ${req.session.accessToken}`
        }
      }
    );

    if (!documentResponse.data) {
      throw new Error('Document not found');
    }

    // Proceed with cancellation
    const cancelResponse = await axios.put(
      `${API_BASE_URL}/api/v1.0/documents/${uuid}/cancel`,
      {
        reason: 'Cancelled via portal',
        additionalInformation: `Cancelled by user ${loggedUser}`
      },
      {
        headers: {
          'Authorization': `Bearer ${req.session.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('Cancel API Response:', cancelResponse.data);

    if (cancelResponse.status === 200 || cancelResponse.status === 202) {
      // Update local database statuses
      await Promise.all([
        WP_SUBMISSION_STATUS.update(
          {
            SubmissionStatus: 'Cancelled',
            DateTimeUpdated: moment().format('YYYY-MM-DD HH:mm:ss')
          },
          { 
            where: { 
              DocNum: id,
              UUID: uuid 
            } 
          }
        ),
        WP_INBOUND_STATUS.update(
          {
            status: 'Cancelled',
            dateTimeReceived: moment().format('YYYY-MM-DD HH:mm:ss')
          },
          { 
            where: { uuid } 
          }
        )
      ]);

      // Log success
      await WP_LOGS.create({
        Description: `Invoice ${id} cancelled successfully`,
        CreateTS: new Date(),
        LoggedUser: loggedUser || 'System'
      });

      return res.json({
        success: true,
        message: 'Invoice cancelled successfully'
      });
    }

    throw new Error('Unexpected response from cancellation API');

  } catch (error) {
    console.error('Error cancelling invoice:', error);

    // Handle specific error cases
    if (error.response) {
      const errorData = error.response.data;
      
      // Check if document is already cancelled
      if (errorData?.error?.code === 'ValidationError' && 
          errorData?.error?.details?.some(d => d.message?.includes('already cancelled'))) {
        
        // Update local status
        await Promise.all([
          WP_SUBMISSION_STATUS.update(
            { SubmissionStatus: 'Cancelled' },
            { where: { DocNum: id, UUID: uuid } }
          ),
          WP_INBOUND_STATUS.update(
            { status: 'Cancelled' },
            { where: { uuid } }
          )
        ]);

        return res.json({
          success: true,
          message: 'Document was already cancelled'
        });
      }

      // Handle other API errors
      return res.status(error.response.status).json({
        success: false,
        message: 'Failed to cancel invoice',
        error: errorData?.error?.message || error.message
      });
    }

    // Log the error
    await WP_LOGS.create({
      Description: `Failed to cancel invoice ${id}: ${error.message}`,
      CreateTS: new Date(),
      LoggedUser: loggedUser || 'System'
    });

    return res.status(500).json({
      success: false,
      message: 'Failed to cancel invoice',
      error: error.message
    });
  }
});


router.get('/api/inbound-status', async (req, res) => {
  try {
    // Ensure proper Sequelize instance
    const results = await WP_INBOUND_STATUS.findAll({
      where: {
        dateTimeReceived: {
          [Op.not]: null // Only get records with valid dates
        }
      },
      attributes: ['dateTimeReceived', 'status'],
      raw: true
    });

    // Send successful response with data wrapped in expected format
    res.json({
      success: true,
      data: results // Wrap the results in a data property
    });

  } catch (error) {
    console.error('Error fetching inbound status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// router.post('/api/cancel-invoice', async (req, res) => {
//   const { uuid } = req.body;

//   if (!uuid) {
//     return res.status(400).json({ success: false, message: 'UUID is required' });
//   }

//   try {
//     const response = await axios.put(
//       `${API_BASE_URL}/api/v1.0/documents/state/${uuid}/state`,
//       {
//         status: 'cancelled',
//         reason: 'some reason for cancelled document'
//       },
//       {
//         headers: {
//           'Authorization': `Bearer ${req.session.accessToken}`,
//           'Content-Type': 'application/json',
//         },
//       }
//     );

//     console.log('Invoice cancelled successfully:', response.data);
//     res.json({ success: true, message: 'Invoice cancelled successfully' });
//   } catch (error) {
//     if (error.response) {
//       // Extract and display the specific error message
//       const errorData = error.response.data;
//       console.error('Error details:', JSON.stringify(errorData, null, 2));
//       res.status(500).json({
//         success: false,
//         message: 'Failed to cancel invoice',
//         error: errorData
//       });
//     } else {
//       console.error('Error:', error.message);
//       res.status(500).json({
//         success: false,
//         message: 'Failed to cancel invoice',
//         error: error.message
//       });
//     }
//   }
// });



// Route to handle issuing credit note, debit note, or refund note
router.post('/api/issue-note/:id', async (req, res) => {
  const invoiceId = req.params.id;
  const { noteType, justification } = req.body;

  res.json({ success: true });
});

// // Doc Header XML
// router.get('/api/doc-header', async (req, res) => {
//   try {
//     const files = fs.readdirSync(xmlFolderPath).filter(file => file.endsWith('.XML'));

//     const data = files.map(file => ({
//       filePath: path.join(xmlFolderPath, file),
//       fileName: file,
//     }));

//     res.json(data);
//   } catch (error) {
//     console.error('Error fetching data:', error);
//     res.status(500).json({ error: 'Internal Server Error' });
//   }
// });


// Route to get the total count of outbound invoices
router.get('/doc-header/count', async (req, res) => {
  try {
    const count = await DocHeader.count();
    res.json({ count });
  } catch (error) {
    console.error('Error fetching count:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// END FUNCTIONS //

// Route for login page
router.get('/login', (req, res) => {
  res.sendFile(path.join(__dirname, '../src', 'pages', 'auth', 'login.html'));
});


// Route for register page
router.get('/auth/register', (req, res) => {
  res.sendFile(path.join(__dirname, '../src', 'pages', 'auth', 'pages-register.html'));
});

// // Route to handle login



// Route to handle registration
router.post('/auth/register', async (req, res) => {
  const { name, email, username, password, TIN, IDType, ID } = req.body;

  try {
    // Check if username or email already exists
    const existingUser = await User.findOne({
      where: {
        [Sequelize.Op.or]: [
          { Username: username },
          { Email: email }
        ]
      }
    });

    if (existingUser) {
      return res.json({ success: false, message: 'Username or email already exists' });
    }

    // Hash the password and create the new user
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await User.create({
      FullName: name,
      Email: email,
      Username: username,
      Password: hashedPassword,
      TIN: TIN,
      IDType: IDType,
      IDValue: ID
    });

    res.json({ success: true, message: 'User registered successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Error creating user' });
  }
});

// Logout route
// router.get('/auth/logout', (req, res) => {
//   req.session.destroy(err => {
//     if (err) {
//       return res.redirect('/dashboard');
//     }
//     res.clearCookie('connect.sid'); // Clear the session cookie
//     res.redirect('/auth/login');
//   });
// });

// Route for get documents page
router.get('/get-documents', (req, res) => {
  res.sendFile(path.join(__dirname, '../src', 'pages', 'dashboard', 'get-documents.html'));
});

// Route for submit documents page
router.get('/submit-documents', (req, res) => {
  res.sendFile(path.join(__dirname, '../src', 'pages', 'dashboard', 'submit-documents.html'));
});


// Route for inbound page
// router.get('/inbound', (req, res) => {
//   res.sendFile(path.join(__dirname, '../src', 'pages', 'dashboard', 'inbound.html'));
// });


// Route for users page
router.get('/users', (req, res) => {
  res.sendFile(path.join(__dirname, '../src', 'pages', 'dashboard', 'users.html'));
});

// router.get('/api/inbound-status', async (req, res) => {
//   try {
//     const results = await WP_INBOUND_STATUS.findAll({
//       attributes: ['ReceivedDate', 'InboundStatus']
//     });
//     res.json(results.map(result => result.toJSON()));
//   } catch (error) {
//     console.error('Error fetching data:', error);
//     res.status(500).send('Internal Server Error');
//   }
// });

// router.get('/api/inbound-status', async (req, res) => {
//   const { period } = req.query;
//   let dateFilter = new Date(0); // Default to epoch time

//   switch (period) {
//     case 'today':
//       dateFilter = new Date();
//       dateFilter.setHours(0, 0, 0, 0);
//       break;
//     case 'this-month':
//       dateFilter = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
//       break;
//     case 'this-year':
//       dateFilter = new Date(new Date().getFullYear(), 0, 1);
//       break;
//   }

//   try {
//     const results = await WP_INBOUND_STATUS.findAll({
//       attributes: ['ReceivedDate', 'InboundStatus']
//     });

//     const filteredResults = results.filter(result => new Date(result.ReceivedDate) >= dateFilter);

//     res.json(filteredResults.map(result => result.toJSON()));
//   } catch (error) {
//     console.error('Error fetching data:', error);
//     res.status(500).send('Internal Server Error');
//   }
// });


// Route for users page
router.get('/sftp-settings', (req, res) => {
  res.sendFile(path.join(__dirname, '../src', 'pages', 'dashboard', 'sftp-configuration.html'));
});



// FUNCTION FOR PROFILE PAGE
router.get('/api/get-profile', async (req, res) => {
  try {
    const currentUser = req.session.user.username; // Get the current user from the session
   // console.log('Current session user:', currentUser);
    
    const users = await WP_USER_REGISTRATION.findAll(); // Fetch all records
 //   console.log('Fetched users:', users);

    if (users) {
      const currentUserData = users.find(user => user.dataValues.Username === currentUser); // Find the current user's data
  //    console.log('Current user data:', currentUserData);
      res.json({ users, currentUser: currentUserData });
    } else {
      res.status(404).send('No users found');
    }
  } catch (error) {
    console.error('Error fetching profiles:', error); // Log the error
    res.status(500).send('Server error');
  }
});

// Add or update the SFTP test endpoint
router.post('/api/sftp/test', async (req, res) => {
  try {
      const { host, port, username, password } = req.body;
      
      // Validate required fields
      if (!host || !username || !password) {
          return res.status(400).json({
              success: false,
              error: 'Missing required credentials'
          });
      }

      console.log('Testing SFTP connection with config:', {
          host,
          port,
          username,
          hasPassword: !!password
      });

      // Test the connection using sftpService
      await sftpService.testConnection({
          host,
          port: port || '22',
          username,
          password
      });

      res.json({ success: true });
  } catch (error) {
      console.error('SFTP test failed:', error);
      res.status(500).json({
          success: false,
          error: error.message || 'Failed to test SFTP connection'
      });
  }
});
router.post('/api/sftp/config', async (req, res) => {
  try {
    console.log('Received SFTP config save request:', {
      host: req.body.host,
      port: req.body.port,
      username: req.body.username,
      hasPassword: !!req.body.password,
      templates: {
        root_path: req.body.root_path,
        incoming_manual: req.body.incoming_manual_template,
        incoming_schedule: req.body.incoming_schedule_template,
        outgoing_manual: req.body.outgoing_manual_template,
        outgoing_schedule: req.body.outgoing_schedule_template
      }
    });

    // First try to save the config
    const config = await sftpService.saveConfig(req.body);
    console.log('SFTP config saved successfully:', config.id);

    // Then try to establish connection
    const testConnection = await sftpService.testConnection({
      host: req.body.host,
      port: req.body.port,
      username: req.body.username,
      password: req.body.password
    });

    if (testConnection) {
      // If connection successful, try to create directory structure
      try {
        await sftpService.createDirectoryStructure();
        console.log('Directory structure created successfully');
      } catch (dirError) {
        console.warn('Warning: Could not create directory structure:', dirError.message);
      }
    }

    res.json({ 
      success: true, 
      message: 'Configuration saved successfully',
      config: {
        id: config.id,
        host: config.host,
        port: config.port,
        username: config.username,
        root_path: config.root_path,
        incoming_manual_template: config.incoming_manual_template,
        incoming_schedule_template: config.incoming_schedule_template,
        outgoing_manual_template: config.outgoing_manual_template,
        outgoing_schedule_template: config.outgoing_schedule_template
      }
    });
  } catch (error) {
    console.error('Failed to save SFTP config:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to save configuration',
      details: error.original?.message || error.stack
    });
  }
});

router.get('/api/sftp/config', async (req, res) => {
  try {
    const config = await sftpService.getConfig();
    res.json(config);
  } catch (error) {
    console.error('Failed to get SFTP config:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to get configuration' 
    });
  }
});

// Add these routes for SFTP configurations
router.get('/api/sftp/configs', async (req, res) => {
    try {
        const configs = await WP_SFTP_CONFIG.findAll({
            attributes: [
                'id', 
                'host', 
                'port', 
                'username', 
                'is_active',
                'root_path',
                'incoming_manual_template',
                'incoming_schedule_template',
                'outgoing_manual_template',
                'outgoing_schedule_template'
            ]
        });
        
        res.json({ 
            success: true, 
            configs: configs.map(config => ({
                ...config.toJSON(),
                password: undefined // Don't send password to frontend
            }))
        });
    } catch (error) {
        console.error('Error fetching SFTP configs:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Failed to fetch SFTP configurations' 
        });
    }
});

// Route to activate a specific configuration
router.post('/api/sftp/config/:id/activate', async (req, res) => {
    try {
        // First deactivate all configs
        await WP_SFTP_CONFIG.update(
            { is_active: false }, 
            { where: {} }
        );
        
        // Then activate the selected one
        await WP_SFTP_CONFIG.update(
            { is_active: true }, 
            { where: { id: req.params.id } }
        );
        
        res.json({ success: true });
    } catch (error) {
        console.error('Error activating SFTP config:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Failed to activate configuration' 
        });
    }
});

// Route to delete a configuration
router.delete('/api/sftp/config/:id', async (req, res) => {
  try {
      const config = await WP_SFTP_CONFIG.findByPk(req.params.id);
      if (!config) {
          return res.status(404).json({
              success: false,
              error: 'Configuration not found'
          });
      }

      await config.destroy();
      res.json({ success: true });
  } catch (error) {
      console.error('Error deleting SFTP config:', error);
      res.status(500).json({ 
          success: false, 
          error: 'Failed to delete configuration' 
      });
  }
});

// Route to save a new configuration
router.post('/api/sftp/config', async (req, res) => {
  try {
      const { 
          host, 
          port, 
          username, 
          password,
          root_path,
          incoming_manual_template,
          incoming_schedule_template,
          outgoing_manual_template,
          outgoing_schedule_template
      } = req.body;

      // Validate required fields
      if (!host || !username || !password) {
          return res.status(400).json({
              success: false,
              error: 'Missing required fields'
          });
      }

      // Deactivate all existing configs
      await WP_SFTP_CONFIG.update(
          { is_active: false },
          { where: {} }
      );

      // Create new config
      const config = await WP_SFTP_CONFIG.create({
          host,
          port: port || 22,
          username,
          password,
          root_path: root_path || '/',
          is_active: true,
          incoming_manual_template,
          incoming_schedule_template,
          outgoing_manual_template,
          outgoing_schedule_template
      });

      res.json({
          success: true,
          config: {
              id: config.id,
              host: config.host,
              port: config.port,
              username: config.username,
              is_active: config.is_active,
              root_path: config.root_path,
              incoming_manual_template: config.incoming_manual_template,
              incoming_schedule_template: config.incoming_schedule_template,
              outgoing_manual_template: config.outgoing_manual_template,
              outgoing_schedule_template: config.outgoing_schedule_template
          }
      });
  } catch (error) {
      console.error('Error saving SFTP config:', error);
      res.status(500).json({
          success: false,
          error: 'Failed to save configuration'
      });
  }
});

// Add this route to handle directory listing
router.get('/api/sftp/list', async (req, res) => {
  try {
      const path = req.query.path || '/SFTP_DATA';
      console.log('Listing directory:', path);
      
      const result = await sftpService.listDirectoryStructure(path);
      console.log('Directory listing result:', result);
      
      res.json(result);
  } catch (error) {
      console.error('Failed to list directory:', error);
      res.status(500).json({
          success: false,
          error: error.message || 'Failed to list directory'
      });
  }
});




router.post('/api/saveCompanySettings', upload.single('companyImage'), async (req, res) => {
  const { 
    username, password, clientId, clientSecret, companyName, industry, country, about, address, phone, email, tin, brn, idValue
  } = req.body;

  try {
    const currentUsername = req.session.user.username;

    let user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    if (!user.TIN) {
      return res.status(400).json({ success: false, message: 'User does not have a TIN' });
    }

    // Update user data
    user.ClientID = clientId;
    user.ClientSecret = clientSecret;

    // Hash the password if provided and update it
    if (password) {
      const hashedPassword = await bcrypt.hash(password, saltRounds);
      user.Password = hashedPassword;
    }

    await user.save();

    let companySettings = await WP_COMPANY_SETTINGS.findOne({ where: { TIN: user.TIN } });
    if (!companySettings) {
      return res.status(404).json({ success: false, message: 'Company settings not found for the given TIN' });
    }

    // Update company settings data
    if (req.file) {
      companySettings.CompanyImage = '/images/' + req.file.filename; // Save the file path
    }
    companySettings.CompanyName = companyName;
    companySettings.Industry = industry;
    companySettings.Country = country;
    companySettings.About = about;
    companySettings.Address = address;
    companySettings.Phone = phone;
    companySettings.Email = email;

    await companySettings.save();

    res.json({ success: true });
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});



router.post('/api/addCompanySettings', upload.single('companyImage'), async (req, res) => {
  console.log('Request body:', req.body);  // Log the request body to check if userID is received correctly

  const { 
    companyName, industry, country, about, address, phone, email, tin, brn, userID
  } = req.body;

  // Validate mandatory fields
  if (!tin || !brn) {
    return res.status(400).json({ success: false, message: 'TIN and BRN are mandatory' });
  }

  try {
    if (!req.session.user) {
      return res.status(403).json({ success: false, message: 'User session not found' });
    }

    const currentUsername = req.session.user.username;
    console.log('Current Username:', currentUsername);

    let user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Check if company with the same TIN or BRN already exists
    const existingCompany = await WP_COMPANY_SETTINGS.findOne({
      where: {
        [Op.or]: [
          { TIN: tin },
          { BRN: brn }
        ]
      }
    });

    if (existingCompany) {
      return res.status(409).json({ success: false, message: 'Company with the same TIN or BRN already exists' });
    }

    // Build the new company settings
    let companySettings = WP_COMPANY_SETTINGS.build({
      CompanyName: companyName,
      Industry: industry,
      Country: country,
      About: about,
      Address: address,
      Phone: phone,
      Email: email,
      TIN: tin,
      BRN: brn,
      UserID: userID,
    });

    // Update company image if provided
    if (req.file) {
      companySettings.CompanyImage = '/images/' + req.file.filename; // Save the file path
    }

    console.log('Company settings data:', companySettings);
    await companySettings.save();

    res.json({ success: true });
  } catch (error) {
    console.error('Error:', error.message, error.stack);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// router.post('/api/addCompanySettings', upload.single('companyImage'), async (req, res) => {
//   console.log('Request body:', req.body);  // Log the request body to check if userID is received correctly

//   const { 
//     companyName, industry, country, about, address, phone, email, tin, brn, userID
//   } = req.body;

//   // Validate mandatory fields
//   if (!tin || !brn) {
//     return res.status(400).json({ success: false, message: 'TIN and BRN are mandatory' });
//   }

//   try {
//     if (!req.session.user) {
//       return res.status(403).json({ success: false, message: 'User session not found' });
//     }

//     const currentUsername = req.session.user.username;
//     console.log('Current Username:', currentUsername);

//     let user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });

//     if (!user) {
//       return res.status(404).json({ success: false, message: 'User not found' });
//     }

//     let companySettings = WP_COMPANY_SETTINGS.build({
//       CompanyName: companyName,
//       Industry: industry,
//       Country: country,
//       About: about,
//       Address: address,
//       Phone: phone,
//       Email: email,
//       TIN: tin,
//       BRN: brn,
//       UserID: userID,
//     });

//     // Update company image if provided
//     if (req.file) {
//       companySettings.CompanyImage = '/images/' + req.file.filename; // Save the file path
//     }

//     console.log('Company settings data:', companySettings);
//     await companySettings.save();


//     res.json({ success: true });
//   } catch (error) {
//     console.error('Error:', error.message, error.stack);
//     res.status(500).json({ success: false, message: 'Server error', error: error.message });
//   }
// });



// FUNCTION COMPANY SETTINGS
router.get('/api/getCompanySettings', async (req, res) => {
  try {
    const currentUsername = req.session.user.username;
    const user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    const tin = user.TIN;

    if (!tin) {
      return res.status(400).json({ success: false, message: 'TIN not found for user' });
    }

    const companySettings = await WP_COMPANY_SETTINGS.findOne({ where: { TIN: tin } });
    if (!companySettings) {
      return res.status(404).json({ success: false, message: 'Company settings not found' });
    }

    res.json({ success: true, companySettings });
  } catch (error) {
    console.error('Error fetching company settings:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}); // Remove the extra }); after this line








router.get('/api/getFilteredCompanySettings', async (req, res) => {
  try {
    const currentUsername = req.session.user.username;
   // console.log('Current session user:', currentUsername);

    const user = await WP_USER_REGISTRATION.findOne({ where: { Username: currentUsername } });
    //console.log('Fetched user:', user);

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    const tin = user.TIN;
   /// console.log('User TIN:', tin);

    if (!tin) {
      return res.status(400).json({ success: false, message: 'TIN not found for user' });
    }

    // Fetch all company settings
    const allCompanySettings = await WP_COMPANY_SETTINGS.findAll();
   // console.log('All company settings:', allCompanySettings);

    // Apply filtering logic
    const filteredCompanySettings = allCompanySettings.filter(company => {
      return !company.TIN.includes(tin);
    });
    //console.log('Filtered company settings:', filteredCompanySettings);

    if (!filteredCompanySettings.length) {
      return res.status(404).json({ success: false, message: 'Filtered company settings not found' });
    }

    res.json({ success: true, companySettings: filteredCompanySettings });
  } catch (error) {
    console.error('Error fetching company settings:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});


router.get('/api/stackbar', async (req, res) => {
  try {
    const submissions = await WP_SUBMISSION_STATUS.findAll();
    res.json(submissions);
  } catch (error) {
    console.error('Error fetching data:', error);
    res.status(500).send('Error fetching data');
  }
});

// Add this new endpoint for syncing with API
router.post('/api/documents/sync', async (req, res) => {
  try {
    // Fetch from API and save to database
    const apiData = await fetchRecentDocuments(req);
    await saveInboundStatus(apiData);
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error syncing with API:', error);
    res.status(500).json({
      success: false,
      message: `Failed to sync with API: ${error.message}`
    });
  }
});

// Add this new endpoint to get the database record
router.get('/api/documents/:uuid/record', async (req, res) => {
  const { uuid } = req.params;
  try {
    const record = await WP_INBOUND_STATUS.findOne({
      where: { uuid }
    });

    if (!record) {
      return res.status(404).json({ 
        success: false, 
        message: 'Record not found' 
      });
    }

    res.json({
      success: true,
      longId: record.longId,
      ...record.toJSON()
    });
  } catch (error) {
    console.error('Error fetching record:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch record' 
    });
  }
}); // Changed }; to });

// // Route for Excel outbound page
// router.get('/outbound-excel', (req, res) => {
//   res.sendFile(path.join(__dirname, '../src', 'pages', 'dashboard', 'outbound-excel.html'));
// });

// router.get('/api/outbound-files', async (req, res) => {
//   try {
//     if (!fs.existsSync(excelOutboundPath)) {
//       fs.mkdirSync(excelOutboundPath, { recursive: true });
//       return res.json({ success: true, data: [] });
//     }

//     // Define column mappings based on Excel structure
//     const columnMap = {
//       'A': 'Row_Identifier',
//       'B': 'Invoice',              // Invoice ID
//       'C': 'Invoice_DocumentReference_UUID',
//       'D': 'Invoice_DocumentReference_InternalID',
//       'E': 'IssueDate',
//       'F': 'IssueTime',
//       'G': 'InvoiceTypeCode',
//       'H': 'DocumentCurrencyCode',
//       'I': 'TaxCurrencyCode',
//       'J': 'Exchange_Rate',
//       'K': 'StartDate',
//       'L': 'EndDate',
//       'M': 'Description',
//       'N': 'ID',
//       'O': 'DocumentType',
//       'P': 'DocumentDescription',
//       'Q': 'AdditionalAccountID',
//       'R': 'schemeAgencyName',
//       'S': 'IndustryClassificationCode',
//       'T': 'PartyIdentification_ID',
//       'U': 'PartyIdentification_schemeID',
//       'V': 'CityName',
//       'W': 'PostalZone',
//       'X': 'CountrySubentityCode',
//       'Y': 'AddressLine',
//       'Z': 'Country_IdentificationCode',
//       'AA': 'Country_listID',
//       'AB': 'Country_listAgencyID',
//       'AC': 'RegistrationName',
//       'AD': 'Contact_Telephone',
//       'AE': 'Contact_ElectronicMail',
//       'AF': 'Buyer_PartyIdentification_ID',
//       'AG': 'Buyer_PartyIdentification_schemeID',
//       'AH': 'Buyer_CityName',
//       'AI': 'Buyer_PostalZone',
//       'AJ': 'Buyer_CountrySubentityCode',
//       'AK': 'Buyer_AddressLine',
//       'AL': 'Buyer_Country_IdentificationCode',
//       'AM': 'Buyer_RegistrationName',
//       'AN': 'Buyer_Contact_Telephone',
//       'AO': 'Buyer_Contact_ElectronicMail',
//       'AP': 'Delivery_PartyIdentification_ID',
//       'AQ': 'Delivery_PartyIdentification_schemeID',
//       'AR': 'Delivery_CityName',
//       'AS': 'Delivery_PostalZone',
//       'AT': 'Delivery_CountrySubentityCode',
//       'AU': 'Delivery_AddressLine',
//       'AV': 'Delivery_Country_IdentificationCode',
//       'AW': 'PaymentMeansCode_ID',
//       'AX': 'PayeeFinancialAccount',
//       'AY': 'PaymentTerms_Notes',
//       'AZ': 'PrepaidPayment_ID',
//       'BA': 'PrepaidPayment_PaidAmount',
//       'BB': 'PrepaidPayment_PaidDate',
//       'BC': 'PrepaidPayment_PaidTime',
//       'BD': 'InvoiceAllowanceCharge_ChargeIndicator',
//       'BE': 'InvoiceAllowanceCharge_AllowanceChargeReason',
//       'BF': 'InvoiceAllowanceCharge_Amount',
//       'BG': 'TaxAmount',
//       'BH': 'TaxSubtotal_TaxableAmount',
//       'BI': 'TaxSubtotal_TaxAmount',
//       'BJ': 'TaxCategory_ID',
//       'BK': 'TaxScheme_ID',
//       'BL': 'TaxScheme_schemeID',
//       'BM': 'TaxScheme_schemeAgencyID',
//       'BN': 'LegalMonetaryTotal_LineExtensionAmount',
//       'BO': 'LegalMonetaryTotal_TaxExclusiveAmount',
//       'BP': 'LegalMonetaryTotal_TaxInclusiveAmount',
//       'BQ': 'LegalMonetaryTotal_AllowanceTotalAmount',
//       'BR': 'LegalMonetaryTotal_ChargeTotalAmount',
//       'BS': 'LegalMonetaryTotal_PayableRoundingAmount',
//       'BT': 'LegalMonetaryTotal_PayableAmount',
//       'BU': 'InvoiceLine_ID',
//       'BV': 'InvoiceLine_InvoicedQuantity',
//       'BW': 'InvoiceLine_unitCode',
//       'BX': 'InvoiceLine_LineExtensionAmount',
//       'BY': 'InvoiceLine_ChargeIndicator',
//       'BZ': 'InvoiceLine_AllowanceChargeReason',
//       'CA': 'InvoiceLine_MultiplierFactorNumeric',
//       'CB': 'InvoiceLine_Amount',
//       'CC': 'InvoiceLine_TaxTotal_TaxAmount',
//       'CD': 'InvoiceLine_TaxTotal_TaxableAmount',
//       'CE': 'InvoiceLine_TaxTotal_Percent',
//       'CF': 'InvoiceLine_TaxTotal_TaxCategory_ID',
//       'CG': 'InvoiceLine_TaxTotal_TaxCategory_TaxExemptionReason',
//       'CH': 'InvoiceLine_TaxTotal_TaxCategory_TaxScheme_ID',
//       'CI': 'InvoiceLine_TaxTotal_TaxCategory_TaxScheme_schemeID',
//       'CJ': 'InvoiceLine_TaxTotal_TaxCategory_TaxScheme_schemeAgencyID',
//       'CK': 'ItemClassificationCode',
//       'CL': 'listID',
//       'CM': 'Description',
//       'CN': 'OriginCountry',
//       'CO': 'PriceAmount',
//       'CP': 'ItemPriceExtension_Amount'
//     };

//     const files = await fsp.readdir(excelOutboundPath);
//     console.log('Found Excel files:', files);

//     const fileDetails = await Promise.all(files.map(async (file) => {
//       try {
//         if (!file.endsWith('.xlsx') && !file.endsWith('.xls')) return null;

//         const filePath = path.join(excelOutboundPath, file);
//         await writeDebugLog('Processing file:', filePath);

//         const workbook = XLSX.readFile(filePath);
//         const sheetName = workbook.SheetNames[0];
//         const worksheet = workbook.Sheets[sheetName];
//         const rawData = XLSX.utils.sheet_to_json(worksheet, {
//           raw: true,
//           defval: '',
//           blankrows: false
//         });

//         await writeDebugLog('Raw Excel data:', rawData);

//         // Skip the first two header rows and process the actual data
//         const transactions = processExcelData(rawData);

//         const lhdnFormattedData = transactions
//           .map(mapToLHDNFormat)
//           .filter(data => data !== null);
//         await writeDebugLog('LHDN formatted data:', lhdnFormattedData);

//         if (lhdnFormattedData.length === 0) {
//           const errorMsg = `No valid transactions found in file: ${file}`;
//           await writeDebugLog('Error:', errorMsg);
//           return {
//             fileName: file,
//             error: errorMsg,
//             status: 'Error'
//           };
//         }

//         const jsonOutputPath = path.join(__dirname, '../logs/excel-parsing');
//         if (!fs.existsSync(jsonOutputPath)) {
//           fs.mkdirSync(jsonOutputPath, { recursive: true });
//         }

//         const outputPath = path.join(jsonOutputPath, `${file.replace(/\.[^/.]+$/, '')}_lhdn.json`);
//         console.log('Writing LHDN JSON to:', outputPath);
        
//         await fsp.writeFile(outputPath, JSON.stringify(lhdnFormattedData, null, 2));
//         console.log('Successfully wrote LHDN JSON file');

//         return {
//           fileName: file,
//           data: lhdnFormattedData,
//           dateUploaded: (await fsp.stat(filePath)).birthtime,
//           dateSubmitted: null,
//           status: 'Success',
//           transactionCount: lhdnFormattedData.length,
//           jsonPath: outputPath
//         };

//       } catch (error) {
//         await writeDebugLog('Error processing file:', {
//           file,
//           error: error.message,
//           stack: error.stack
//         });
//         return {
//           fileName: file,
//           error: error.message,
//           status: 'Error'
//         };
//       }
//     }));

//     const validFileDetails = fileDetails.filter(detail => detail !== null);
//     console.log('Processed files:', validFileDetails.map(d => ({
//       file: d.fileName,
//       status: d.status,
//       transactions: d.transactionCount
//     })));
    
//     res.json({ 
//       success: true, 
//       data: validFileDetails,
//       totalFiles: validFileDetails.length
//     });

//   } catch (error) {
//     await writeDebugLog('Error in /api/outbound-files:', {
//       error: error.message,
//       stack: error.stack
//     });
//     res.status(500).json({ 
//       success: false, 
//       error: error.message 
//     });
//   }
// });

// // Add static file serving
// router.use('/excel/outbound', express.static(excelOutboundPath));

// const writeDebugLog = async (message, data) => {
//   const debugLogPath = path.join(__dirname, '../logs/debug');
//   if (!fs.existsSync(debugLogPath)) {
//     fs.mkdirSync(debugLogPath, { recursive: true });
//   }
  
//   const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
//   const logFile = path.join(debugLogPath, `debug_${timestamp}.txt`);
  
//   const logContent = `${message}\n${JSON.stringify(data, null, 2)}\n\n`;
//   await fsp.appendFile(logFile, logContent);
//   return logFile;
// };



// // 4. Main processing function
// router.get('/api/outbound-files', async (req, res) => {
//   try {
//     const files = await fsp.readdir(excelOutboundPath);
    
//     const results = await Promise.all(files.map(async file => {
//       if (!file.endsWith('.xlsx')) return null;
      
//       const filePath = path.join(excelOutboundPath, file);
      
//       // Step 1: Extract Excel data
//       const rawData = extractExcelData(filePath);
      
//       // Step 2: Group into transactions
//       const transactions = groupTransactions(rawData);
      
//       // Step 3: Map to LHDN format
//       const lhdnData = transactions.map(mapToLHDNFormat).filter(Boolean);
      
//       // Save the result
//       const outputPath = path.join(__dirname, '../logs/excel-parsing', `${file}_lhdn.json`);
//       await fsp.writeFile(outputPath, JSON.stringify(lhdnData, null, 2));
      
//       return {
//         fileName: file,
//         data: lhdnData,
//         status: 'Success',
//         transactionCount: lhdnData.length
//       };
//     }));

//     res.json({ 
//       success: true, 
//       data: results.filter(Boolean)
//     });

//   } catch (error) {
//     console.error('Error processing files:', error);
//     res.status(500).json({ success: false, error: error.message });
//   }
// });



// EXCEL FILE CODES

// Function to ensure all directories exist
function createDirectories() {
  Object.entries(paths).forEach(([key, dirPath]) => {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
      } else {
        console.log(`Directory already exists: ${dirPath}`);
      }
    } catch (error) {
      console.error(`Error creating directory ${dirPath}:`, error);
    }
  });
}

createDirectories();

// Route for Excel outbound page
router.get('/outbound-bqe', (req, res) => {
  res.render('dashboard/outbound-bqe.html', { title: 'Outbound' });
});

router.get('/inbound', (req, res) => {
  res.render('dashboard/inbound.html', { title: 'Inbound' });
});

// // Route to get list of processed files
// router.get('/outbound-files', async (req, res) => {
//   try {
//     if (!fs.existsSync(excelOutboundPath)) {
//       return res.json({ success: true, data: [] });
//     }

//     const files = await fsp.readdir(excelOutboundPath);
//     const fileDetails = await Promise.all(files
//       .filter(file => file.endsWith('.xlsx') || file.endsWith('.xls'))
//       .map(async (file) => {
//         const filePath = path.join(excelOutboundPath, file);
//         const stats = await fsp.stat(filePath);
//         const jsonFileName = `${path.parse(file).name}.json`;
//         const jsonFilePath = path.join(jsonOutputPath, jsonFileName);
//         const isProcessed = fs.existsSync(jsonFilePath);

//         return {
//           fileName: file,
//           dateCreated: stats.birthtime,
//           lastModified: stats.mtime,
//           size: stats.size,
//           isProcessed: isProcessed,
//           jsonPath: isProcessed ? jsonFilePath : null
//         };
//     }));

//     res.json({
//       success: true,
//       data: fileDetails,
//       totalFiles: fileDetails.length
//     });

//   } catch (error) {
//     console.error('Error in outbound-files:', error);
//     res.status(500).json({
//       success: false,
//       error: error.message
//     });
//   }
// });

// Route to get list of processed files
// router.get('/api/outbound-files', async (req, res) => {
//   try {
//     const files = await fsp.readdir(paths.outbound);
//     const fileDetails = await Promise.all(files
//       .filter(file => file.endsWith('.xlsx') || file.endsWith('.xls'))
//       .map(async (file) => {
//         try {
//           const filePath = path.join(paths.outbound, file);
//           const workbook = XLSX.readFile(filePath);
//           const worksheet = workbook.Sheets[workbook.SheetNames[0]];
//           const rawData = XLSX.utils.sheet_to_json(worksheet, {
//             raw: true,
//             defval: null,
//             blankrows: false
//           });

//           // Process the data
//           const processedData = processExcelData(rawData);

//           // Add scheme ID summary to validation results
//           const schemeIdSummary = processedData.reduce((acc, doc) => {
//             // Count supplier scheme IDs
//             doc.supplier.identifications.forEach(id => {
//               acc.supplier[id.schemeId] = (acc.supplier[id.schemeId] || 0) + 1;
//             });
//             // Count buyer scheme IDs
//             doc.buyer.identifications.forEach(id => {
//               acc.buyer[id.schemeId] = (acc.buyer[id.schemeId] || 0) + 1;
//             });
//             return acc;
//           }, { supplier: {}, buyer: {} });

//           // Validate the data
//           const validationResult = await processAndValidateExcel(filePath);
          
//           // Add scheme ID summary to validation results
//           validationResult.validation.schemeIdSummary = schemeIdSummary;

//           // Save to JSON and logs
//           const jsonFileName = `${path.parse(file).name}_${Date.now()}.json`;
//           const jsonPath = path.join(paths.jsonOutput, jsonFileName);
//           const logFileName = `${path.parse(file).name}_validation_${Date.now()}.json`;
//           const logPath = path.join(paths.processLogs, logFileName);
          
//           await Promise.all([
//             fsp.writeFile(jsonPath, JSON.stringify(processedData, null, 2)),
//             fsp.writeFile(logPath, JSON.stringify(validationResult.validation, null, 2))
//           ]);

//           return {
//             fileName: file,
//             processedDate: new Date(),
//             documentCount: processedData.length,
//             status: 'Success',
//             data: processedData,
//             jsonPath,
//             validation: {
//               status: validationResult.validation.isValid ? 'Valid' : 'Invalid',
//               details: validationResult.validation,
//               schemeIdSummary,
//               logPath
//             }
//           };
//         } catch (error) {
//           console.error(`Error processing file ${file}:`, error);
//           return {
//             fileName: file,
//             error: error.message,
//             status: 'Error'
//           };
//         }
//     }));

//     res.json({
//       success: true,
//       data: fileDetails,
//       totalFiles: fileDetails.length
//     });

//   } catch (error) {
//     console.error('Error processing files:', error);
//     res.status(500).json({
//       success: false,
//       error: error.message
//     });
//   }
// });

// router.get('/api/validate-excel', async (req, res) => {
//   try {
//     const files = await fsp.readdir(paths.outbound);
//     const validationResults = await Promise.all(files
//       .filter(file => file.endsWith('.xlsx') || file.endsWith('.xls'))
//       .map(async (file) => {
//         try {
//           const filePath = path.join(paths.outbound, file);
//           const result = await processAndValidateExcel(filePath);

//           // Save validation results to log file
//           const logFileName = `${path.parse(file).name}_validation_${Date.now()}.json`;
//           const logPath = path.join(paths.processLogs, logFileName);
//           await fsp.writeFile(logPath, JSON.stringify(result.validation, null, 2));

//           return {
//             fileName: file,
//             validationDate: new Date(),
//             status: 'Success',
//             validation: result.validation,
//             logPath
//           };

//         } catch (error) {
//           console.error(`Error validating file ${file}:`, error);
//           return {
//             fileName: file,
//             error: error.message,
//             status: 'Error'
//           };
//         }
//     }));

//     res.json({
//       success: true,
//       data: validationResults,
//       totalFiles: validationResults.length
//     });

//   } catch (error) {
//     console.error('Error processing files:', error);
//     res.status(500).json({
//       success: false,
//       error: error.message
//     });
//   }
// });


// // Route to get list of processed files
// router.get('/outbound-files', async (req, res) => {
//   try {
//     if (!fs.existsSync(excelOutboundPath)) {
//       return res.json({ success: true, data: [] });
//     }

//     const files = await fsp.readdir(excelOutboundPath);
//     const fileDetails = await Promise.all(files
//       .filter(file => file.endsWith('.xlsx') || file.endsWith('.xls'))
//       .map(async (file) => {
//         const filePath = path.join(excelOutboundPath, file);
//         const stats = await fsp.stat(filePath);
//         const jsonFileName = `${path.parse(file).name}.json`;
//         const jsonFilePath = path.join(jsonOutputPath, jsonFileName);
//         const isProcessed = fs.existsSync(jsonFilePath);

//         return {
//           fileName: file,
//           dateCreated: stats.birthtime,
//           lastModified: stats.mtime,
//           size: stats.size,
//           isProcessed: isProcessed,
//           jsonPath: isProcessed ? jsonFilePath : null
//         };
//     }));

//     res.json({
//       success: true,
//       data: fileDetails,
//       totalFiles: fileDetails.length
//     });

//   } catch (error) {
//     console.error('Error in outbound-files:', error);
//     res.status(500).json({
//       success: false,
//       error: error.message
//     });
//   }
// });


// // Route to get processed JSON data for a specific file
// router.get('/outbound-file/:filename', async (req, res) => {
//   try {
//     const jsonFileName = `${path.parse(req.params.filename).name}.json`;
//     const jsonFilePath = path.join(jsonOutputPath, jsonFileName);

//     if (!fs.existsSync(jsonFilePath)) {
//       return res.status(404).json({
//         success: false,
//         error: 'Processed file not found'
//       });
//     }

//     const jsonData = await fsp.readFile(jsonFilePath, 'utf8');
//     res.json({
//       success: true,
//       data: JSON.parse(jsonData)
//     });

//   } catch (error) {
//     console.error('Error reading processed file:', error);
//     res.status(500).json({
//       success: false,
//       error: error.message
//     });
//   }
// });

// Add static file serving for Excel files
router.use('/excel/outbound', express.static(excelOutboundPath));

// Update the outbound files endpoint
router.get('/api/outbound-files', async (req, res) => {
  try {
    if (!fs.existsSync(paths.outbound)) {
      fs.mkdirSync(paths.outbound, { recursive: true });
    }

    // Get submission statuses from database
    const submissionStatuses = await WP_SUBMISSION_STATUS.findAll({
      attributes: ['DocNum', 'UUID', 'SubmissionStatus', 'DateTimeSent', 'DateTimeUpdated', 'RejectionDetails', 'FileName'],
      raw: true
    });

    const statusMap = new Map();
    submissionStatuses.forEach(status => {
      statusMap.set(status.DocNum, status);
      statusMap.set(status.FileName, status);
    });

    const files = await fsp.readdir(paths.outbound);
    const fileDetails = await Promise.all(files
      .filter(file => file.endsWith('.xlsx') || file.endsWith('.xls'))
      .map(async (file) => {
        try {
          const filePath = path.join(paths.outbound, file);
          const workbook = XLSX.readFile(filePath);
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          
          // Process Excel data to get buyer information
          const rawData = XLSX.utils.sheet_to_json(worksheet, {
            raw: true,
            defval: null,
            blankrows: false
          });

          // Process the Excel data using processExcelData
          const processedData = processExcelData(rawData);
          
          // Extract buyer name from the first document if available
          const buyerName = processedData?.[0]?.buyer?.name || null;
          
          // Extract DocNum from filename
          const docNumMatch = file.match(/\d{2}_(IN\d+|CN\d+)/);
          const docNum = docNumMatch ? docNumMatch[1] : null;

          // Get file stats
          const stats = await fsp.stat(filePath);
          
          // Check for existing submission by both DocNum and FileName
          const submissionByDoc = statusMap.get(docNum);
          const submissionByFile = statusMap.get(file);
          const submissionStatus = submissionByDoc || submissionByFile;

          // Log for debugging
          console.log('File Status:', {
            file,
            docNum,
            buyerName, // Add buyer name to the log
            hasSubmissionByDoc: !!submissionByDoc,
            hasSubmissionByFile: !!submissionByFile,
            status: submissionStatus?.SubmissionStatus
          });

          return {
            fileName: file,
            invoiceId: docNum,
            buyerName: buyerName, // Add buyer name to the response
            dateUploaded: stats.mtime.toISOString(),
            dateSubmitted: submissionStatus?.DateTimeSent || null,
            dateUpdated: submissionStatus?.DateTimeUpdated || null,
            status: submissionStatus?.SubmissionStatus || 'Pending',
            uuid: submissionStatus?.UUID || null,
            rejectionDetails: submissionStatus?.RejectionDetails || null
          };

        } catch (error) {
          console.error(`Error processing file ${file}:`, error);
          return {
            fileName: file,
            error: error.message,
            status: 'Error'
          };
        }
    }));

    // Filter out duplicates based on DocNum
    const uniqueFiles = fileDetails.reduce((acc, curr) => {
      if (!acc.some(item => item.invoiceId === curr.invoiceId)) {
        acc.push(curr);
      }
      return acc;
    }, []);

    res.json({
      success: true,
      data: uniqueFiles.filter(detail => detail !== null)
    });

  } catch (error) {
    console.error('Error processing files:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// // Add a route to get LHDN format for a specific file
// router.get('/api/lhdn-format/:filename', async (req, res) => {
//   try {
//     const filePath = path.join(paths.outbound, req.params.filename);
//     const validationResult = await processAndValidateExcel(filePath);
//     const processedData = processExcelData(validationResult.data);
    
//     // Map to LHDN format
//     const lhdnData = mapToLHDNFormat(processedData);

//     // Save LHDN format
//     const lhdnFileName = `${path.parse(req.params.filename).name}_lhdn_${Date.now()}.json`;
//     await fsp.writeFile(
//       path.join(paths.jsonOutput, lhdnFileName), 
//       JSON.stringify(lhdnData, null, 2)
//     );

//     res.json({
//       success: true,
//       fileName: req.params.filename,
//       lhdnFormat: lhdnData,
//       savedAs: lhdnFileName
//     });

//   } catch (error) {
//     console.error('Error generating LHDN format:', error);
//     res.status(500).json({
//       success: false,
//       error: error.message
//     });
//   }
// });

// // Add this route handler for fetching file details
// router.get('/api/outbound-files/details/:filename', async (req, res) => {
//   try {
//     const { filename } = req.params;
//     const filePath = path.join(paths.outbound, filename);

//     // Check if file exists
//     if (!fs.existsSync(filePath)) {
//       return res.status(404).json({
//         success: false,
//         message: 'File not found'
//       });
//     }

//     // Read and process the Excel file
//     const workbook = XLSX.readFile(filePath);
//     const worksheet = workbook.Sheets[workbook.SheetNames[0]];
//     const rawData = XLSX.utils.sheet_to_json(worksheet, {
//       raw: true,
//       defval: null,
//       blankrows: false
//     });

//     // Process the Excel data
//     const processedData = processExcelData(rawData);

//     // Return the first document's details
//     if (processedData && processedData[0]) {
//       res.json({
//         success: true,
//         data: processedData[0]
//       });
//     } else {
//       res.status(404).json({
//         success: false,
//         message: 'No valid data found in file'
//       });
//     }

//   } catch (error) {
//     console.error('Error fetching file details:', error);
//     res.status(500).json({
//       success: false,
//       message: 'Failed to fetch file details',
//       error: error.message
//     });
//   }
// });

// // Add this new route for LHDN mapping
// router.get('/api/outbound-files/:filename/lhdn-format', async (req, res) => {
//   try {
//     const { filename } = req.params;
//     const filePath = path.join(paths.outbound, filename);

//     // Check if file exists
//     if (!fs.existsSync(filePath)) {
//       return res.status(404).json({
//         success: false,
//         message: 'File not found'
//       });
//     }

//     // Read and process the Excel file
//     const workbook = XLSX.readFile(filePath);
//     const worksheet = workbook.Sheets[workbook.SheetNames[0]];
//     const rawData = XLSX.utils.sheet_to_json(worksheet, {
//       raw: true,
//       defval: null,
//       blankrows: false
//     });

//     // Process the Excel data
//     const processedData = processExcelData(rawData);

//     // Map to LHDN format using the existing mapper
//     const lhdnFormat = mapToLHDNFormat(processedData);

//     // Return the LHDN formatted data
//     res.json({
//       success: true,
//       data: lhdnFormat
//     });

//   } catch (error) {
//     console.error('Error generating LHDN format:', error);
//     res.status(500).json({
//       success: false,
//       message: 'Failed to generate LHDN format',
//       error: error.message
//     });
//   }
// });

// Add this new endpoint for submission statuses
router.get('/api/submission-statuses', async (req, res) => {
  try {
    const statuses = await WP_SUBMISSION_STATUS.findAll({
      attributes: ['DocNum', 'UUID', 'SubmissionStatus', 'DateTimeSent', 'DateTimeUpdated']
    });
    
    res.json({
      success: true,
      data: statuses
    });
  } catch (error) {
    console.error('Error fetching submission statuses:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// // Add endpoint for submitting to LHDN
// router.post('/api/outbound-files/:filename/submit-to-lhdn', async (req, res) => {
//   try {
//     const { filename } = req.params;
//     const filePath = path.join(paths.outbound, filename);

//     // Initialize LHDN submitter
//     const lhdnSubmitter = new LHDNSubmitter(req);

//     // Read and process the Excel file
//     const workbook = XLSX.readFile(filePath);
//     const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    
//     // Process Excel data using the existing function
//     const rawData = XLSX.utils.sheet_to_json(worksheet, {
//       raw: true,
//       defval: null,
//       blankrows: false
//     });

//     // Process the Excel data to get the structured document
//     const processedDocs = processExcelData(rawData);
    
//     if (!processedDocs || processedDocs.length === 0) {
//       throw new Error('No valid documents found in Excel file');
//     }

//     // Map the processed data to LHDN format
//     const lhdnJson = mapToLHDNFormat(processedDocs);

//     if (!lhdnJson) {
//       throw new Error('Failed to generate LHDN JSON format');
//     }

//     // Save the LHDN JSON for reference
//     const jsonFileName = `${path.parse(filename).name}_lhdn.json`;
//     const jsonFilePath = path.join(paths.jsonOutput, jsonFileName);
//     await fsp.writeFile(jsonFilePath, JSON.stringify(lhdnJson, null, 2));

//     // Submit the document using LHDN submitter
//     const result = await lhdnSubmitter.submitDocument(lhdnJson);

//     if (result.success) {
//       res.json({
//         success: true,
//         message: 'Document submitted successfully',
//         docNum: result.docNum,
//         uuid: result.acceptedDocuments[0]?.uuid
//       });
//     } else {
//       // If there are validation errors or other issues
//       res.status(400).json({
//         success: false,
//         error: result.error,
//         docNum: result.docNum
//       });
//     }

//   } catch (error) {
//     console.error('Error submitting document:', error);
//     res.status(500).json({
//       success: false,
//       error: {
//         message: error.message,
//         code: 'SubmissionError',
//         details: error.response?.data?.error || {}
//       }
//     });
//   }
// });

// Add this route to handle document cancellation
router.post('/api/outbound-files/:filename/cancel', async (req, res) => {
  try {
    const { filename } = req.params;
    const { reason } = req.body;

    if (!reason) {
      return res.status(400).json({
        success: false,
        error: 'Cancellation reason is required'
      });
    }

    // Get the file details first
    const filePath = path.join(paths.outbound, filename);
    const workbook = XLSX.readFile(filePath);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    
    // Read all rows as an array of objects with headers
    const rows = XLSX.utils.sheet_to_json(worksheet, { 
      header: 'A',
      raw: false
    });

    // Find the Invoice_ID row (Row H)
    const invoiceIdRow = rows.find(row => row.A === 'H');
    const invoiceId = invoiceIdRow?.B;

    if (!invoiceId) {
      throw new Error('Invoice ID not found in file');
    }

    // Get submission status from database
    const submissionStatus = await WP_SUBMISSION_STATUS.findOne({
      where: { DocNum: invoiceId }
    });

    if (!submissionStatus || !submissionStatus.UUID) {
      throw new Error('Document not found or not yet submitted');
    }

    // Get auth token
    const authResponse = await getTokenAsTaxPayer(req);
    if (!authResponse?.access_token) {
      throw new Error('Failed to get authentication token');
    }

    // Call LHDN service to cancel the document
    const cancelResponse = await cancelValidDocumentBySupplier(
      submissionStatus.UUID,
      reason,
      authResponse.access_token
    );

    if (cancelResponse.status === 'success') {
      // Update status in database
      await WP_SUBMISSION_STATUS.update(
        {
          SubmissionStatus: 'Cancelled',
          DateTimeUpdated: new Date()
        },
        {
          where: { DocNum: invoiceId }
        }
      );

      // Log the cancellation
      await WP_LOGS.create({
        Description: `Document ${invoiceId} cancelled. Reason: ${reason}`,
        CreateTS: new Date(),
        LoggedUser: req.session.user?.username || 'System'
      });

      res.json({
        success: true,
        message: 'Document cancelled successfully'
      });
    } else {
      throw new Error('Failed to cancel document at LHDN');
    }

  } catch (error) {
    console.error('Error cancelling document:', error);
    
    // Handle specific API errors
    if (error.response?.data) {
      const apiError = error.response.data;
      return res.status(500).json({
        success: false,
        error: {
          code: apiError.code || 'API_ERROR',
          message: apiError.message || apiError.error || error.message,
          details: apiError.details || []
        }
      });
    }

    // Handle other errors
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});



module.exports = router;

