{% extends 'layout.html' %}

{% block head %}
<!-- Set page title -->
<title>Dashboard</title>


<link href="/assets/css/welcome-card.css" rel="stylesheet">
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<link href="/assets/css/pages/inbound/card.css" rel="stylesheet">
<link href="/assets/css/pages/inbound/notice.css" rel="stylesheet">


<link href="/assets/css/pages/inbound/validation-modal.css" rel="stylesheet">
<link href="/assets/css/pages/inbound/inbound-modal.css" rel="stylesheet">
<!-- Template Main CSS Files -->
<link href="/assets/css/pages/outbound/outbound-table.css" rel="stylesheet">

<script src="/assets/js/config/validation-translations.js"></script>
<style>

  /* Skeleton Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite linear;
  border-radius: 4px;
  height: 14px;
  margin-bottom: 8px;
}

/* Card Skeleton Styles */
.card-skeleton .card-title-skeleton {
  height: 20px;
  width: 60%;
  margin-bottom: 20px;
}

.card-skeleton .skeleton-number {
  height: 30px;
  width: 80px;
  margin-bottom: 10px;
}

.card-skeleton .skeleton-text {
  height: 14px;
  width: 60%;
}

/* Status Items Skeleton */
.status-item-skeleton {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.status-icon-skeleton {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
}

.status-content-skeleton {
  flex-grow: 1;
}

.status-content-skeleton .skeleton-title {
  height: 18px;
  width: 120px;
  margin-bottom: 8px;
}

.status-content-skeleton .skeleton-text {
  height: 14px;
  width: 180px;
}

/* Chart Skeleton */
.chart-skeleton {
  height: 300px;
  border-radius: 8px;
}

/* Customer List Skeleton */
.customer-item-skeleton {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
}

.customer-avatar-skeleton {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

.customer-info-skeleton {
  flex-grow: 1;
}

.customer-info-skeleton .skeleton-name {
  height: 18px;
  width: 140px;
  margin-bottom: 8px;
}

.customer-info-skeleton .skeleton-details {
  height: 14px;
  width: 100px;
}

.customer-info-skeleton .skeleton-badge {
  height: 22px;
  width: 60px;
  border-radius: 20px;
  position: absolute;
  right: 20px;
  top: 12px;
}

/* Activity Log Skeleton */
.activity-item-skeleton {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
}

.activity-icon-skeleton {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 16px;
  flex-shrink: 0;
}

.activity-content-skeleton {
  flex-grow: 1;
}

.activity-content-skeleton .skeleton-title {
  height: 18px;
  width: 80%;
  margin-bottom: 8px;
}

.activity-content-skeleton .skeleton-meta {
  height: 14px;
  width: 60%;
}

/* Handle showing/hiding skeletons */
.skeleton-wrapper {
  display: none;
}

.is-loading .skeleton-wrapper {
  display: block;
}

.is-loading .content-wrapper {
  display: none;
}

.no-data .skeleton-wrapper {
  display: none;
}

/* Status Indicators */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 12px;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  box-shadow: 0 0 0 rgba(0,0,0,0.1);
}

.status-submitted { 
  background-color: #4361ee; 
}
.status-pending { 
  background-color: #f7b801; 
}
.status-valid { 
  background-color: #2ec4b6; 
}
.status-invalid { 
  background-color: #e63946; 
}
.status-cancelled { 
  background-color: #6c757d; 
}

/* System Status Grid */
.system-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.status-item {
  transition: all 0.3s ease;
  border: none;
  border-radius: 10px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.status-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.08);
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: rgba(var(--bs-primary-rgb), 0.1);
  transition: all 0.3s ease;
}

.status-item:hover .status-icon {
  transform: scale(1.1);
}

/* Progress bars */
.progress-thin {
  height: 6px;
  border-radius: 3px;
  background-color: #e9ecef;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
}

.progress-wrapper {
  display: flex;
  align-items: center;
}

.progress-bar {
  transition: width 0.6s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  from { background-position: 1rem 0; }
  to { background-position: 0 0; }
}

/* Refresh button animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.refresh-button .fa-sync-alt {
  transition: transform 0.3s ease;
}

.refresh-button:active .fa-sync-alt {
  animation: spin 1s linear;
}

/* Sync status indicator */
#syncStatus {
  font-size: 8px;
  transition: all 0.3s ease;
}

#syncStatus.recent { 
  color: #2ec4b6; 
  animation: pulse 2s infinite;
}
#syncStatus.warning { 
  color: #f7b801; 
}
#syncStatus.danger { 
  color: #e63946; 
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* Card updated animation */
@keyframes card-updated-animation {
  0% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); }
  50% { box-shadow: 0 4px 20px rgba(67, 97, 238, 0.3); }
  100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); }
}

.card-updated {
  animation: card-updated-animation 1s ease;
}

/* Hover effect for status items */
.hover-bg {
  transition: all 0.2s ease;
}

.hover-bg:hover {
  background-color: rgba(0, 0, 0, 0.03);
  transform: translateX(3px);
}

/* Badge styling */
.badge {
  padding: 0.4em 0.65em;
  font-weight: 500;
  border-radius: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.bg-success {
  background-color: #2ec4b6 !important;
}

.bg-danger {
  background-color: #e63946 !important;
}

.bg-warning {
  background-color: #f7b801 !important;
}

.bg-info {
  background-color: #4361ee !important;
}

.bg-primary-subtle {
  background-color: rgba(67, 97, 238, 0.1);
}

.bg-success-subtle {
  background-color: rgba(46, 196, 182, 0.1);
}

.bg-warning-subtle {
  background-color: rgba(247, 184, 1, 0.1);
}

.bg-info-subtle {
  background-color: rgba(67, 97, 238, 0.1);
}

.text-success {
  color: #2ec4b6 !important;
}

.text-warning {
  color: #f7b801 !important;
}

.text-danger {
  color: #e63946 !important;
}

.text-info {
  color: #4361ee !important;
}

/* Button styling */
.btn-light {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.btn-light:hover {
  background-color: #e9ecef;
  border-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.rounded-circle {
  width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

  /* Card Base Styles */
  .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
  }
  
  .card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  }

  .info-card {
    height: 100%;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
  }

  .info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all 0.3s ease;
  }

  .info-card:hover::before {
    height: 6px;
  }

  .customers-card::before { background: #00ac69; }
  .revenue-card::before { background: #f4a100; }
  .sales-card::before { background: #0061f2; }

  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 1rem;
    transition: all 0.3s ease;
  }

  .info-card:hover .card-icon {
    transform: scale(1.1);
  }

  .customers-card .card-icon {
    background: rgba(0, 172, 105, 0.1);
    color: #00ac69;
  }

  .revenue-card .card-icon {
    background: rgba(244, 161, 0, 0.1);
    color: #f4a100;
  }

  .sales-card .card-icon {
    background: rgba(0, 97, 242, 0.1);
    color: #0061f2;
  }

  /* Stats Numbers */
  #fileCount,
  #inboundCount,
  #companyCount {
    font-size: 1.75rem;
    font-weight: 700;
    color: #363d47;
    margin-bottom: 0.25rem;
    line-height: 1;
  }

  .text-muted {
    color: #69707a !important;
    font-size: 0.875rem;
    font-weight: 500;
  }

  /* Card Title */
  .card-title-new {
    font-size: 0.875rem;
    font-weight: 600;
    color: #363d47;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin: 0;
    position: relative;
    display: inline-flex;
    align-items: center;
  }
  
  .card-title-new::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: #4361ee;
    transition: width 0.3s ease;
  }
  
  .card:hover .card-title-new::after {
    width: 50px;
  }

/* New Analytics Styles */
.invoice-stats-card {
  background: #fff;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-pending { background: #f4a100; }
.status-approved { background: #00ac69; }
.status-submitted { background: #0061f2; }
.status-rejected { background: #dc3545; }
.status-cancelled { background: #f4a100; }
.status-valid { background: #00ac69; }
.status-invalid { background: #dc3545; }
.status-queued { background: #f4a100; }

.mini-chart {
  height: 40px;
  width: 100px;
  margin-left: auto;
}

.payment-status-card {
  background: linear-gradient(45deg, #2962ff0d 0%, #2962ff03 100%);
  border-left: 4px solid #2962ff;
}

.invoice-amount-card {
  background: linear-gradient(45deg, #00ac690d 0%, #00ac6903 100%);
  border-left: 4px solid #00ac69;
}

.progress-thin {
  height: 4px;
  border-radius: 2px;
}

.customer-logo {
  width: 40px;
  height: 40px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fallback-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.company-initial {
  background: linear-gradient(45deg, #2962ff, #2962ff80);
  color: white;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-initial::after {
  content: attr(data-initial);
  position: absolute;
  color: white;
}

.hover-bg:hover {
  background-color: rgba(0, 0, 0, 0.03);
  transition: background-color 0.2s ease;
}

.customer-name {
  font-size: 0.875rem;
  font-weight: 600;
}

.badge {
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 500;
}

.bg-success-subtle {
  background-color: rgba(0, 172, 105, 0.1);
}

.bg-warning-subtle {
  background-color: rgba(255, 171, 0, 0.1);
}

.customer-list > div:last-child {
  margin-bottom: 0 !important;
}

/* Customer List Styling */
.customer-item {
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.customer-item:hover {
  background-color: var(--bs-gray-100);
  border-color: var(--bs-gray-200);
  transform: translateX(3px);
}

/* Avatar Styling */
.customer-avatar {
  position: relative;
  width: 48px;
  height: 48px;
}

.avatar-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--bs-gray-100);
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.customer-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.2rem;
  color: white;
  background: linear-gradient(45deg, #2196F3, #3F51B5);
}

/* Badge Styling */
.badge {
  padding: 0.5em 0.8em;
  font-weight: 500;
  font-size: 0.75rem;
}

.bg-success-subtle {
  background-color: rgba(25, 135, 84, 0.1);
}

.bg-warning-subtle {
  background-color: rgba(255, 193, 7, 0.1);
}

/* Customer Info Styling */
.customer-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--bs-gray-900);
}

.customer-info small {
  font-size: 0.8rem;
}

.text-primary {
  color: #2196F3 !important;
}

/* Activity Log Container */
.activity-log-container {
  max-height: 280px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--bs-gray-400) transparent;
}

/* Activity Item Styling */
.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  position: relative;
}

.activity-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20px;
  top: 48px;
  bottom: 0;
  width: 2px;
  background-color: var(--bs-gray-200);
}

/* Icon Styling */
.activity-icon {
  flex-shrink: 0;
  margin-right: 1rem;
}

.icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.bg-info-subtle {
  background-color: rgba(13, 202, 240, 0.1);
}

.bg-success-subtle {
  background-color: rgba(25, 135, 84, 0.1);
}

/* Content Styling */
.activity-content {
  flex-grow: 1;
  min-width: 0;
}

.activity-text {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-meta {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
}

/* Date Filter Styling */
.date-filter .btn-group {
  border-radius: 20px;
  overflow: hidden;
}

.date-filter .btn {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-color: var(--bs-gray-300);
}

.date-filter .btn.active {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

/* View All Button */
.view-all-btn {
  transition: all 0.2s ease;
  border: 1px solid var(--bs-gray-300);
}

.view-all-btn:hover {
  background-color: var(--bs-gray-200);
  transform: translateY(-1px);
}

/* Scrollbar Styling */
.activity-log-container::-webkit-scrollbar {
  width: 6px;
}

.activity-log-container::-webkit-scrollbar-track {
  background: transparent;
}

.activity-log-container::-webkit-scrollbar-thumb {
  background-color: var(--bs-gray-400);
  border-radius: 3px;
}

/* No Data State Styles */
.no-data-state {
  text-align: center;
  padding: 2rem;
  color: var(--bs-gray-500);
  display: none;
}

.no-data-state i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.no-data-state p {
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* Loading Indicator Styles */
.loading-spinner {
  display: none;
  text-align: center;
  padding: 2rem;
}

.loading-spinner .spinner-border {
  width: 3rem;
  height: 3rem;
  color: var(--bs-primary);
}

.loading-text {
  margin-top: 1rem;
  color: var(--bs-gray-600);
  font-size: 0.9rem;
}

/* Show these states when needed */
.is-loading .loading-spinner {
  display: block;
}

.no-data .no-data-state {
  display: block;
}

/* Tooltip Styles */
/* .guide-tooltip .tooltip-inner {
    max-width: 250px;
    padding: 8px 12px;
    color: #fff;
    text-align: center;
    background-color: #2c3e50;
    border-radius: 6px;
    font-size: 0.875rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.guide-tooltip .tooltip-arrow::before {
    border-top-color: #2c3e50;
} */

.info-icon {
    font-size: 14px;
    color: #6c757d;
    margin-left: 5px;
    cursor: help;
}

.info-icon:hover {
    color: #495057;
}

.modal-content {
  border-radius: 15px;
}

.modal-header {
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}

.form-control-lg, .form-select-lg {
  height: 50px;
  font-size: 1rem;
}

.input-group-text {
  color: #6c757d;
}

.form-control:focus, .form-select:focus {
  box-shadow: none;
  border-color: var(--bs-primary);
}

.alert {
  border-radius: 10px;
}

.alert-success {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.btn-lg {
  padding: 12px 24px;
  font-weight: 500;
}

.btn-light {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:hover {
  background-color: #e9ecef;
  border-color: #e9ecef;
}

#tinSearchModal .modal-dialog {
  max-width: 500px;
}

.form-label.small {
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

/* Activity Logs Styles */
.activity-logs-list {
  max-height: auto;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.activity-logs-list::-webkit-scrollbar {
  width: 6px;
}

.activity-logs-list::-webkit-scrollbar-track {
  background: transparent;
}

.activity-logs-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.activity-item {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.activity-item:hover {
  background-color: #f1f3f5;
  transform: translateX(3px);
}

.activity-item.info-log {
  border-left-color: #4361ee;
}

.activity-item.success-log {
  border-left-color: #2ec4b6;
}

.activity-item.warning-log {
  border-left-color: #f7b801;
}

.activity-item.error-log {
  border-left-color: #e63946;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.activity-icon.info-icon {
  background-color: rgba(67, 97, 238, 0.1);
  color: #4361ee;
}

.activity-icon.success-icon {
  background-color: rgba(46, 196, 182, 0.1);
  color: #2ec4b6;
}

.activity-icon.warning-icon {
  background-color: rgba(247, 184, 1, 0.1);
  color: #f7b801;
}

.activity-icon.error-icon {
  background-color: rgba(230, 57, 70, 0.1);
  color: #e63946;
}

.activity-time {
  font-size: 0.75rem;
  color: #6c757d;
  margin-bottom: 4px;
}

.activity-message {
  font-size: 0.875rem;
  color: #343a40;
  margin-bottom: 4px;
  font-weight: 500;
}

.activity-user {
  font-size: 0.75rem;
  color: #6c757d;
}

.activity-user span {
  font-weight: 500;
  color: #343a40;
}

.info-card {
  height: 100%;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}


</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-2 px-md-2 px-lg-2">
  <!-- Welcome Card -->
    <div class="profile-welcome-card">
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
          <div class="welcome-icon">
            <i class="bi bi-speedometer2"></i>
          </div>
          <div class="welcome-content">
            <h4 class="mb-1">Dashboard Overview</h4>
            <p class="mb-0">Monitor your e-invoice activities and manage your transactions efficiently.</p>
          </div>
        </div>
        <div class="welcome-datetime">
          <div class="current-time">
            <i class="bi bi-clock"></i>
            <span id="currentTime">00:00:00 AM</span>
          </div>
          <div class="current-date">
            <i class="bi bi-calendar3"></i>
            <span id="currentDate">Loading...</span>
          </div>
        </div>
      </div>
    </div>

<!-- Add this right after the welcome card, before the main content -->
<div class="skeleton-wrapper">
  <!-- Stats Cards Skeletons -->
  <div class="row g-3 mb-4">
    <!-- Outbound Card Skeleton -->
    <div class="col-md-4">
      <div class="card card-skeleton">
        <div class="card-body p-3">
          <div class="d-flex align-items-center">
            <div class="status-icon-skeleton skeleton"></div>
            <div class="flex-grow-1">
              <div class="skeleton-number skeleton"></div>
              <div class="skeleton-text skeleton"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Inbound Card Skeleton -->
    <div class="col-md-4">
      <div class="card card-skeleton">
        <div class="card-body p-3">
          <div class="d-flex align-items-center">
            <div class="status-icon-skeleton skeleton"></div>
            <div class="flex-grow-1">
              <div class="skeleton-number skeleton"></div>
              <div class="skeleton-text skeleton"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Companies Card Skeleton -->
    <div class="col-md-4">
      <div class="card card-skeleton">
        <div class="card-body p-3">
          <div class="d-flex align-items-center">
            <div class="status-icon-skeleton skeleton"></div>
            <div class="flex-grow-1">
              <div class="skeleton-number skeleton"></div>
              <div class="skeleton-text skeleton"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Invoice Status and System Status Skeletons -->
  <div class="row g-3 mb-4">
    <!-- Invoice Status Skeleton -->
    <div class="col-md-5">
      <div class="card card-skeleton">
        <div class="card-body p-4">
          <div class="card-title-skeleton skeleton"></div>
          <!-- Status Items -->
          {% for i in range(4) %}
          <div class="status-item-skeleton">
            <div class="status-icon-skeleton skeleton"></div>
            <div class="status-content-skeleton">
              <div class="skeleton-title skeleton"></div>
              <div class="skeleton-text skeleton"></div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- System Status Skeleton -->
    <div class="col-md-7">
      <div class="card card-skeleton">
        <div class="card-body p-4">
          <div class="card-title-skeleton skeleton"></div>
          <div class="system-status-grid">
            {% for i in range(4) %}
            <div class="status-item-skeleton">
              <div class="status-icon-skeleton skeleton"></div>
              <div class="status-content-skeleton">
                <div class="skeleton-title skeleton"></div>
                <div class="skeleton-text skeleton"></div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Chart Skeleton -->
  <div class="col-md-12">
    <div class="card">
      <div class="card-body">
        <div class="card-title-skeleton skeleton"></div>
        <div class="chart-skeleton skeleton"></div>
      </div>
    </div>
  </div>

  <!-- Right Column Skeletons -->
  <div class="col-lg-4">
    <!-- Top Customers Skeleton -->
    <div class="card mb-4">
      <div class="card-body p-3">
        <div class="card-title-skeleton skeleton"></div>
        {% for i in range(5) %}
        <div class="customer-item-skeleton">
          <div class="customer-avatar-skeleton skeleton"></div>
          <div class="customer-info-skeleton">
            <div class="skeleton-name skeleton"></div>
            <div class="skeleton-details skeleton"></div>
            <div class="skeleton-badge skeleton"></div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>

    <!-- Recent Activity Skeleton -->
    <div class="card">
      <div class="card-body p-3">
        <div class="card-title-skeleton skeleton"></div>
        {% for i in range(5) %}
        <div class="activity-item-skeleton">
          <div class="activity-icon-skeleton skeleton"></div>
          <div class="activity-content-skeleton">
            <div class="skeleton-title skeleton"></div>
            <div class="skeleton-meta skeleton"></div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>
</div>

<div class="row">
    <!-- Left Column - Stats & Reports -->
    <div class="col-lg-8">
      <!-- Statistics Cards Row -->
        <div class="row g-3 mb-4">
          <!-- Outbound Card -->
          <div class="col-md-4">
            <div class="card info-card customers-card h-100" id="outbound-card" >
              <div class="card-body p-3">
                <div class="d-flex align-items-center justify-content-between">
                  <div class="d-flex align-items-center">
                    <div class="card-icon">
                      <i class="bi bi-box-arrow-right"></i>
                    </div>
                    <div>
                      <h6 id="fileCount" class="mb-0">0</h6>
                      <span class="text-muted">Total</span>
                    </div>
                  </div>
                  <a href="/outbound" class="nav-link">
                    <h5 style="text-align: center;" class="card-title-new">OUTBOUND</h5>
                    <span style="text-align: center;" class="text-muted">STATUS</span>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Inbound Card -->
          <div class="col-md-4">
            <div class="card info-card revenue-card h-100" id="inbound-card" >
              <div class="card-body p-3">
                <div class="d-flex align-items-center justify-content-between">
                  <div class="d-flex align-items-center">
                    <div class="card-icon">
                      <i class="bi bi-box-arrow-in-right"></i>
                    </div>
                    <div>
                      <h6 id="inboundCount" class="mb-0">0</h6>
                      <span class="text-muted">Total</span>
                    </div>
                  </div>
                  <a href="/inbound" class="nav-link">
                    <h5 class="card-title-new">INBOUND</h5>
                    <span style="text-align: center;" class="text-muted">STATUS</span>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Company Card -->
          <div class="col-md-4">
            <div class="card info-card sales-card h-100" id="companies-card" >
              <div class="card-body p-3">
                <div class="d-flex align-items-center justify-content-between">
                  <div class="d-flex align-items-center">
                    <div class="card-icon">
                      <i class="bi bi-building"></i>
                    </div>
                    <div>
                      <h6 id="companyCount" class="mb-0">0</h6>
                      <span class="text-muted">Active</span>
                    </div>
                  </div>
                  <a href="#" class="nav-link">
                    <h5 class="card-title-new">COMPANIES</h5>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- New Analytics Row -->
          <div class="row g-3 mb-4">
            <div class="col-md-5">
              <!-- Invoice Status Distribution -->
                <div class="card h-100" id="invoice-status-card">
                  <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                      <h5 class="card-title-new">
                        PINNACLE INVOICE STATUS
                        <i class="fas fa-info-circle info-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Real-time status distribution of your e-invoices"></i>
                      </h5>
                      <div class="refresh-button">
                        <button class="btn btn-sm btn-light rounded-circle shadow-sm" onclick="updateInvoiceStatus()">
                          <i class="fas fa-sync-alt"></i>
                        </button>
                      </div>
                    </div>
                    
                    <!-- Status Items -->
                    <div class="status-items">
                      <!-- Submitted Status -->
                      <div class="d-flex align-items-center mb-4 p-2 rounded hover-bg">
                        <span class="status-indicator status-submitted"></span>
                        <div class="status-info flex-grow-1">
                          <span class="fw-semibold d-block">Submitted</span>
                          <small class="text-muted" style="font-size: 13px;">Initial structure validation passed</small>
                        </div>
                        <div class="progress-wrapper">
                          <div class="progress progress-thin" style="width: 100px;">
                            <div class="progress-bar bg-primary" style="width: 0%" data-status="submitted"></div>
                          </div>
                          <span class="ms-2 fw-semibold percentage" data-status="submitted">9 documents</span>
                        </div>
                      </div>
    
                      <!-- Valid Status -->
                      <div class="d-flex align-items-center mb-4 p-2 rounded hover-bg">
                        <span class="status-indicator status-valid"></span>
                        <div class="status-info flex-grow-1">
                          <span class="fw-semibold d-block">Valid</span>
                          <small class="text-muted" style="font-size: 13px;">Successful validation</small>
                        </div>
                        <div class="progress-wrapper">
                          <div class="progress progress-thin" style="width: 100px;">
                            <div class="progress-bar bg-success" style="width: 0%" data-status="valid"></div>
                          </div>
                          <span class="ms-2 fw-semibold percentage" data-status="valid">6 documents</span>
                        </div>
                      </div>
    
                      <!-- Invalid Status -->
                      <div class="d-flex align-items-center mb-4 p-2 rounded hover-bg">
                        <span class="status-indicator status-invalid"></span>
                        <div class="status-info flex-grow-1">
                          <span class="fw-semibold d-block">Invalid</span>
                          <small class="text-muted" style="font-size: 13px;">Validation issues found</small>
                        </div>
                        <div class="progress-wrapper">
                          <div class="progress progress-thin" style="width: 100px;">
                            <div class="progress-bar bg-danger" style="width: 0%" data-status="invalid"></div>
                          </div>
                          <span class="ms-2 fw-semibold percentage" data-status="invalid">20 documents</span>
                        </div>
                      </div>
    
                      <!-- Cancelled Status -->
                      <div class="d-flex align-items-center mb-4 p-2 rounded hover-bg">
                        <span class="status-indicator status-cancelled"></span>
                        <div class="status-info flex-grow-1">
                          <span class="fw-semibold d-block">Cancelled</span>
                          <small class="text-muted" style="font-size: 13px;">Cancelled by issuer</small>
                        </div>
                        <div class="progress-wrapper">
                          <div class="progress progress-thin" style="width: 100px;">
                            <div class="progress-bar bg-secondary" style="width: 0%" data-status="cancelled"></div>
                          </div>
                          <span class="ms-2 fw-semibold percentage" data-status="cancelled">1 documents</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
        <!-- LHDN SYSTEM STATUS -->
              <div class="col-md-7">
                <div class="card h-100" id="system-status-card">
                  <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                      <h5 class="card-title-new">
                        LHDN SYSTEM STATUS
                        <i class="fas fa-info-circle info-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Monitor LHDN system connectivity and queue status"></i>
                      </h5>
                    </div>
                    <div class="system-status-grid">
                      <!-- API Endpoint Status -->
                      <div class="status-item p-3 rounded mb-2">
                        <div class="d-flex align-items-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Shows current connection status with LHDN API">
                          <div class="status-icon me-3 bg-primary-subtle">
                            <i class="fas fa-cloud text-primary"></i>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-2 fw-semibold">API Endpoint</h6>
                            <div class="d-flex align-items-center mb-2">
                              <span name="apiStatus" id="apiStatus" class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Connected
                              </span>
                              <small class="ms-2 text-muted" id="apiLastCheck">Last checked: 10:18:28 PM</small>
                            </div>
                            <small class="text-muted" id="apiEndpointUrl">Environment: sandbox</small>
                          </div>
                        </div>
                      </div>

                            
                      <!-- Online Users -->
                      <div class="status-item p-3 rounded mb-2">
                        <div class="d-flex align-items-center" >
                          <div class="status-icon me-3 bg-success-subtle">
                            <i class="fas fa-users text-success"></i>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-2 fw-semibold">Total Users</h6>
                            <div class="d-flex align-items-center mb-2">
                              <span id="onlineUsers" class="fw-semibold">1</span>
                              <i id="onlineUsersStatus" class="fas fa-circle ms-2 text-success"></i>
                            </div>
                            <small class="text-muted text-small" id="onlineUsersDetails">Number of users currently registered</small>
                          </div>
                        </div>
                      </div>

                      
                      <!-- Last Sync -->
                      <div class="status-item p-3 rounded mb-2">
                        <div class="d-flex align-items-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Indicates when data was last synchronized with LHDN">
                          <div class="status-icon me-3 bg-warning-subtle">
                            <i class="fas fa-clock text-warning"></i>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-2 fw-semibold">Inbound Last Sync</h6>
                            <div class="d-flex align-items-center mb-2">
                              <span id="lastSync" class="fw-semibold">246 mins ago</span>
                              <i id="syncStatus" class="fas fa-circle ms-2 warning"></i>
                            </div>
                            <small class="text-muted text-small" id="syncDetails">Sync is slightly delayed</small>
                          </div>
                        </div>
                      </div>

                      <!-- Queue Status -->
                      <div class="status-item p-3 rounded mb-2">
                        <div class="d-flex align-items-center" data-bs-toggle="tooltip" data-bs-placement="top" title="Displays number of documents waiting to be processed">
                          <div class="status-icon me-3 bg-info-subtle">
                            <i class="fas fa-tasks text-info"></i>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-2 fw-semibold">LHDN Queue Status</h6>
                            <div class="queue-status-wrapper mb-2">
                              <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                  <span id="queueCount" >0 Queue</span>
                                  <button class="btn btn-sm btn-light rounded-circle shadow-sm ms-2 p-1" onclick="refreshQueue()" title="Refresh Queue">
                                    <i class="fas fa-sync-alt"></i>
                                  </button>
                                </div>
                                <span class="queue-time text-muted small">Last updated: <span id="queueLastUpdate">Just now</span></span>
                              </div>
                              <div class="queue-progress mt-2">
                                <div class="progress" style="height: 8px; border-radius: 4px; background-color: rgba(0,0,0,0.05);">
                                  <div id="queueProgressBar" class="progress-bar bg-info" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                              </div>
                            </div>
                            <div class="d-flex align-items-center">
                              <small class="text-muted" id="queueDetails">Queue is empty</small>
                              <span id="queueStatusIndicator" class="ms-2 badge bg-success-subtle text-success">Ready</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Add LHDN Link Section -->
                    <div class="mt-3 text-center">
                      <hr class="my-3" style="opacity: 0.1; font-size: 100px;">
                      <a href="https://mytax.hasil.gov.my/" target="_blank" class="btn btn-lhdn d-flex align-items-center justify-content-center" style="font-size: 0.85rem;">
                        <i class="fas fa-external-link-alt me-2"></i>
                        <span>Access LHDN MyTax Portal</span>
                      </a>
                      <small class="text-muted mt-2" style="font-size: 0.75rem;">Visit the official LHDN portal for tax-related services and information</small>
                    </div>
                  </div>
                </div>
              </div>
                        
          </div>
       
          <div class="col-md-12">
            <div class="card h-90" style="height: 550px;">
              <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="card-title-new mb-0">REPORTS BY WEEK</h5>
                
                </div>
                
                <!-- Loading Spinner -->
                <div class="loading-spinner">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <p class="loading-text">Loading chart data...</p>
                </div>
      
                <!-- No Data State -->
                <div class="no-data-state">
                  <i class="bi bi-bar-chart"></i>
                  <p>No report data available</p>
                </div>
      
                <div id="stackedBarChart"></div>
              </div>
            </div>
            
          </div>
    </div>

    <!-- Right Column - Activity -->
      <div class="col-lg-4">
          <!-- Top Customers Card -->
          <div class="card mb-4">
            <div class="card-body p-3">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="card-title-new mb-0">
                  TOP CUSTOMERS
                  <i class="fas fa-info-circle info-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="View your most active customers based on transaction volume"></i>
                </h5>
                <!-- <button class="btn btn-sm btn-outline-primary" onclick="showTinSearchModal()">
                  <i class="fas fa-search me-1"></i>Search TIN
                </button> -->
              </div>
              <div class="customer-list">
                <!-- Loading Spinner -->
                <div class="loading-spinner">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <p class="loading-text">Loading customer data...</p>
                </div>

                <!-- No Data State -->
                <div class="no-data-state">
                  <i class="bi bi-people"></i>
                  <p>No customer data available</p>
                </div>

                <!-- Customer Item Template -->
                <div class="d-flex align-items-center mb-3 p-2 rounded customer-item">
                  <div class="customer-avatar me-3">
                    <div class="avatar-wrapper">
                      <img src="/assets/img/default-avatar.png"
                          alt="Acme Corp"
                          class="customer-logo"
                          onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                      <div class="avatar-fallback">
                        <span>EL</span>
                      </div>
                    </div>
                  </div>
                  <div class="customer-info flex-grow-1">
                    <div class="d-flex justify-content-between align-items-center">
                      <h6 class="mb-0 customer-name"></h6>
                      <span class="badge bg-success-subtle text-success"></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                      <small class="text-muted"><i class="fas fa-file-invoice me-1"></i></small>
                      <span class="fw-semibold text-secondary"></span>
                    </div>
                  </div>
                  
                </div>
              </div>
            </div>
          </div>



      <!-- SDK Updates RSS Feed Card -->
      <div class="card mb-4">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title-new mb-0">
              LHDN UPDATES
              <i class="fas fa-info-circle info-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Latest updates from MyInvois SDK"></i>
            </h5>
            <a href="/api/rss" class="btn btn-sm btn-lhdn">
              <i class="bi bi-rss me-1"></i> RSS
            </a>
          </div>
          
          <!-- SDK Updates Container -->
          <div class="sdk-updates-container">
            <!-- Loading State -->
            <div class="loading-spinner text-center py-4" id="sdk-loading">
              <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="loading-text mt-2">Loading SDK updates...</p>
            </div>
            
            <!-- No Data State -->
            <div class="no-data-state text-center py-4 d-none" id="sdk-no-data">
              <i class="bi bi-journal-code fs-4 mb-2 text-muted"></i>
              <p class="mb-0">No SDK updates available</p>
            </div>
            
            <!-- SDK Updates List -->
            <div class="sdk-updates-list" id="sdk-updates-list">
              <!-- Sample update item (will be populated from API) -->
              <div class="sdk-update-item p-3 mb-3 rounded hover-bg">
                <div class="d-flex align-items-center mb-2">
                  <div class="sdk-update-icon me-2 bg-info-subtle text-info rounded-circle p-1">
                    <i class="bi bi-code-square"></i>
                  </div>
                  <div class="sdk-update-date fw-semibold">11 April 2025</div>
                  <span class="badge bg-success-subtle text-success ms-2">New</span>
                </div>
                <div class="sdk-update-title fw-medium">Updates to API Documentation</div>
                <div class="sdk-update-content text-muted small">
                  Added new API definition for Taxpayer's QR Code in e-Invoice API section.
                </div>
              </div>
              
              <div class="sdk-update-item p-3 mb-3 rounded hover-bg">
                <div class="d-flex align-items-center mb-2">
                  <div class="sdk-update-icon me-2 bg-success-subtle text-success rounded-circle p-1">
                    <i class="bi bi-file-earmark-code"></i>
                  </div>
                  <div class="sdk-update-date fw-semibold">7 February 2025</div>
                </div>
                <div class="sdk-update-title fw-medium">Updates to the XML and JSON samples</div>
                <div class="sdk-update-content text-muted small">
                  Added General TIN 'EI00000000010' to Consolidated Sample XML and JSON. Updated Multi Line Item Samples with different Tax Types.
                </div>
              </div>
            </div>
          </div>
          
          <!-- View All SDK Updates Link -->
          <div class="text-center mt-3">
            <a href="/dashboard/sdk-updates" class="btn btn-sm btn-light">
              <i class="bi bi-journal-text me-1"></i> View All SDK Updates
            </a>
          </div>
        </div>
      </div>
              
      <!-- Recent Activity Logs Card -->
      <div class="card mb-4">
        <div class="card-body p-3">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title-new mb-0">
              RECENT ACTIVITY LOGS
              <i class="fas fa-info-circle info-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="View recent system activities and user actions" data-bs-html="true"></i>
            </h5>
            <div class="dropdown">
              <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="logFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-html="true">
                <i class="fas fa-filter me-1"></i>All
              </button>
              <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="logFilterDropdown">
                <li><a class="dropdown-item active" href="#" data-filter="all">All Activities</a></li>
                <li><a class="dropdown-item" href="#" data-filter="info">Information</a></li>
                <li><a class="dropdown-item" href="#" data-filter="success">Success</a></li>
                <li><a class="dropdown-item" href="#" data-filter="warning">Warning</a></li>
                <li><a class="dropdown-item" href="#" data-filter="error">Error</a></li>
              </ul>
            </div>
          </div>
          
          <!-- Activity Log Container -->
          <div class="activity-log-container" id="activity-log">
            <!-- Loading State -->
            <div class="loading-spinner text-center py-4" id="logs-loading">
              <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="loading-text mt-2">Loading activity logs...</p>
            </div>
            
            <!-- No Data State -->
            <div class="no-data-state text-center py-4 d-none" id="logs-no-data">
              <i class="fas fa-clipboard-list fs-4 mb-2 text-muted"></i>
              <p class="mb-0">No activity logs available</p>
            </div>
            
            <!-- Activity Logs will be populated here -->
            <div class="activity-logs-list" id="activity-logs-list">
              <!-- Log items will be inserted here by JavaScript -->
            </div>
          </div>
          
          <!-- View All Link -->
          <div class="text-center mt-3">
            <a href="/logs" class="btn btn-sm btn-light">
              <i class="fas fa-list me-1"></i> View All Logs
            </a>
          </div>
        </div>
      </div>
</div>

<!-- TIN Search Modal -->
<div class="modal fade" id="tinSearchModal" tabindex="-1">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content border-0 shadow">
      <div class="modal-header border-0 bg-primary text-white px-4 py-3">
        <div class="d-flex align-items-center">
          <i class="fas fa-search me-2"></i>
          <h5 class="modal-title mb-0">Search Taxpayer TIN</h5>
        </div>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body p-4">
        <div class="alert alert-info border-0 mb-4">
          <i class="fas fa-info-circle me-2"></i>
          <small>You can search by Company Name OR Company ID. If both are provided, the search will use AND operator.</small>
        </div>
        
        <form id="tinSearchForm">
          <!-- Company Name -->
          <div class="mb-4">
            <label class="form-label text-muted small text-uppercase fw-bold">Company Name</label>
            <div class="input-group">
              <span class="input-group-text border-0 bg-light">
                <i class="fas fa-building"></i>
              </span>
              <input type="text" class="form-control form-control-lg border-0 bg-light" 
                     id="taxpayerName" 
                     placeholder="Enter company name"
                     autocomplete="off">
            </div>
            <small class="text-muted">Example: ABC XYZ</small>
          </div>
          
          <div class="border-top my-4"></div>
          
          <!-- Company ID -->
          <div class="mb-4">
            <label class="form-label text-muted small text-uppercase fw-bold">ID Type</label>
            <select class="form-select form-select-lg border-0 bg-light" id="idType">
              <option value="">Select ID Type</option>
              <option value="BRN">Business Registration Number (BRN)</option>
              <option value="NRIC">NRIC</option>
              <option value="PASSPORT">Passport Number</option>
              <option value="ARMY">Army ID</option>
            </select>
          </div>
          
          <div class="mb-4">
            <label class="form-label text-muted small text-uppercase fw-bold">ID Value</label>
            <div class="input-group">
              <span class="input-group-text border-0 bg-light">
                <i class="fas fa-id-card"></i>
              </span>
              <input type="text" class="form-control form-control-lg border-0 bg-light" 
                     id="idValue" 
                     placeholder="Enter ID value"
                     autocomplete="off">
            </div>
            <small class="text-muted" id="idValueExample">Example: 201901234567 (BRN)</small>
          </div>
        </form>
        
        <div id="searchResult" class="mt-4" style="display: none;">
          <div class="alert alert-success border-0 d-flex align-items-center">
            <i class="fas fa-check-circle me-3 fs-4"></i>
            <div>
              <div class="text-muted small text-uppercase fw-bold">Tax Identification Number</div>
              <div class="fs-5 fw-bold" id="tinResult"></div>
            </div>
          </div>
        </div>
        
        <div id="searchError" class="mt-4" style="display: none;">
          <div class="alert alert-danger border-0 d-flex align-items-center">
            <i class="fas fa-exclamation-circle me-3 fs-4"></i>
            <span id="errorMessage" class="fw-medium"></span>
          </div>
        </div>
      </div>
      <div class="modal-footer border-0 px-4 pb-4">
        <button type="button" class="btn btn-light btn-lg px-4" data-bs-dismiss="modal">
          <i class="fas fa-times me-2"></i>Close
        </button>
        <button type="button" class="btn btn-primary btn-lg px-4" onclick="searchTIN()">
          <i class="fas fa-search me-2"></i>Search
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/assets/js/stackbar.js"></script>
<script src="/assets/js/logs.js"></script>
<script src="/assets/js/dashboard.js"></script>
<script src="/assets/js/dashboard-logs.js"></script>


<script>
function updateDateTime() {
    const timeElement = document.getElementById('currentTime');
    const dateElement = document.getElementById('currentDate');
    
    function update() {
        const now = new Date();
        
        // Update time
        timeElement.textContent = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });
        
        // Update date
        dateElement.textContent = now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    
    // Update immediately and then every second
    update();
    setInterval(update, 1000);
}

// Call when the document is ready
document.addEventListener('DOMContentLoaded', updateDateTime);

// Function to load SDK updates
function loadSDKUpdates() {
    // Show loading state
    document.getElementById('sdk-loading').style.display = 'block';
    document.getElementById('sdk-updates-list').style.display = 'none';
    document.getElementById('sdk-no-data').classList.add('d-none');
    
    fetch('/api/rss/updates')
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to fetch SDK updates');
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.updates && data.updates.length > 0) {
                // Hide loading, show content
                document.getElementById('sdk-loading').style.display = 'none';
                document.getElementById('sdk-updates-list').style.display = 'block';
                
                // Get the container element
                const container = document.getElementById('sdk-updates-list');
                container.innerHTML = '';
                
                // Display only the first 3 updates
                const updates = data.updates.slice(0, 2);
                
                // Process each update
                updates.forEach(update => {
                    const date = new Date(update.date);
                    const isRecent = isWithinDays(date, 30);
                    const isNew = isWithinDays(date, 7);
                    
                    // Create update item
                    const updateItem = document.createElement('div');
                    updateItem.className = 'sdk-update-item p-3 mb-3 rounded hover-bg';
                    
                    // Create date section with badge if recent
                    const dateSection = document.createElement('div');
                    dateSection.className = 'd-flex align-items-center mb-2';
                    
                    // Determine icon based on content
                    let iconClass = 'bi-info-circle';
                    let bgClass = 'bg-info-subtle text-info';
                    
                    if (update.sections[0].title.toLowerCase().includes('api')) {
                        iconClass = 'bi-code-square';
                        bgClass = 'bg-primary-subtle text-primary';
                    } else if (update.sections[0].title.toLowerCase().includes('xml') || 
                               update.sections[0].title.toLowerCase().includes('json')) {
                        iconClass = 'bi-file-earmark-code';
                        bgClass = 'bg-success-subtle text-success';
                    } else if (update.sections[0].title.toLowerCase().includes('validation')) {
                        iconClass = 'bi-check-circle';
                        bgClass = 'bg-warning-subtle text-warning';
                    }
                    
                    dateSection.innerHTML = `
                        <div class="sdk-update-icon me-2 ${bgClass} rounded-circle p-1">
                            <i class="bi ${iconClass}"></i>
                        </div>
                        <div class="sdk-update-date fw-semibold">${update.date}</div>
                        ${isNew ? '<span class="badge bg-success-subtle text-success ms-2">New</span>' : ''}
                    `;
                    
                    // Create title element
                    const titleElement = document.createElement('div');
                    titleElement.className = 'sdk-update-title fw-medium';
                    titleElement.textContent = update.sections[0].title;
                    
                    // Create content element (first item from first section)
                    const contentElement = document.createElement('div');
                    contentElement.className = 'sdk-update-content text-muted small';
                    contentElement.textContent = update.sections[0].items[0];
                    
                    // Assemble the update item
                    updateItem.appendChild(dateSection);
                    updateItem.appendChild(titleElement);
                    updateItem.appendChild(contentElement);
                    
                    // Add to container
                    container.appendChild(updateItem);
                });
            } else {
                // Show no data state
                document.getElementById('sdk-loading').style.display = 'none';
                document.getElementById('sdk-no-data').classList.remove('d-none');
            }
        })
        .catch(error => {
            console.error('Error loading SDK updates:', error);
            // Show no data state with error
            document.getElementById('sdk-loading').style.display = 'none';
            document.getElementById('sdk-no-data').classList.remove('d-none');
            document.getElementById('sdk-no-data').querySelector('p').textContent = 'Failed to load SDK updates';
        });
}

// Helper function to check if a date is within specified days from now
function isWithinDays(date, days) {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= days;
}

// Load SDK updates when document is ready
document.addEventListener('DOMContentLoaded', function() {
    updateDateTime();
    loadSDKUpdates();
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const avatarFallbacks = document.querySelectorAll('.avatar-fallback');
  
  avatarFallbacks.forEach(fallback => {
    const initial = fallback.textContent.trim();
    fallback.style.background = generateGradientFromString(initial);
  });
});

function generateGradientFromString(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue1 = hash % 360;
  const hue2 = (hue1 + 40) % 360;
  
  return `linear-gradient(45deg, 
    hsl(${hue1}, 70%, 50%), 
    hsl(${hue2}, 70%, 45%)
  )`;
}
</script>
{% endblock %}