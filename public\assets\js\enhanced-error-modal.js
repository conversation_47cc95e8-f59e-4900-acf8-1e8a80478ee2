/**
 * Enhanced Error Modal
 * A better UI for displaying validation errors and other error messages
 */
class EnhancedErrorModal {
  constructor() {
    this.modalId = 'enhancedErrorModal';
    this.initialized = false;
    this.init();
  }

  init() {
    // Check if modal already exists
    if (document.getElementById(this.modalId)) {
      this.initialized = true;
      return;
    }

    // Create modal element
    const modalElement = document.createElement('div');
    modalElement.id = this.modalId;
    modalElement.className = 'enhanced-error-modal';
    
    // Set initial HTML
    modalElement.innerHTML = `
      <div class="enhanced-error-dialog">
        <div class="enhanced-error-header">
          <button type="button" class="enhanced-error-close" aria-label="Close">×</button>
          <div class="enhanced-error-icon">
            <i class="bi bi-x-circle"></i>
          </div>
          <h3 class="enhanced-error-title">Error</h3>
          <p class="enhanced-error-subtitle">An error has occurred</p>
        </div>
        <div class="enhanced-error-body">
          <div class="enhanced-error-message">
            Please check the error details below.
          </div>
          <div class="enhanced-error-code"></div>
          <div class="enhanced-error-details"></div>
        </div>
        <div class="enhanced-error-footer">
          <button type="button" class="enhanced-error-btn enhanced-error-btn-primary">OK</button>
        </div>
      </div>
    `;
    
    // Append to body
    document.body.appendChild(modalElement);
    
    // Add event listeners
    this.addEventListeners();
    
    this.initialized = true;
  }

  addEventListeners() {
    const modal = document.getElementById(this.modalId);
    const closeBtn = modal.querySelector('.enhanced-error-close');
    const okBtn = modal.querySelector('.enhanced-error-btn-primary');
    
    // Close button
    closeBtn.addEventListener('click', () => this.hide());
    
    // OK button
    okBtn.addEventListener('click', () => this.hide());
    
    // Click outside to close
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.hide();
      }
    });
    
    // Escape key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal.classList.contains('show')) {
        this.hide();
      }
    });
  }

  /**
   * Show the error modal with the provided options
   * @param {Object} options - Configuration options
   * @param {string} options.title - Error title
   * @param {string} options.subtitle - Error subtitle
   * @param {string} options.message - Error message
   * @param {string} options.code - Error code
   * @param {Array|Object} options.details - Error details
   * @param {Function} options.onClose - Callback when modal is closed
   */
  show(options = {}) {
    if (!this.initialized) {
      this.init();
    }
    
    const modal = document.getElementById(this.modalId);
    const title = modal.querySelector('.enhanced-error-title');
    const subtitle = modal.querySelector('.enhanced-error-subtitle');
    const message = modal.querySelector('.enhanced-error-message');
    const code = modal.querySelector('.enhanced-error-code');
    const details = modal.querySelector('.enhanced-error-details');
    
    // Set title and subtitle
    title.textContent = options.title || 'Error';
    subtitle.textContent = options.subtitle || 'An error has occurred';
    
    // Set message
    message.innerHTML = options.message || 'Please check the error details below.';
    
    // Set code if provided
    if (options.code) {
      code.textContent = options.code;
      code.style.display = 'block';
    } else {
      code.style.display = 'none';
    }
    
    // Set details if provided
    if (options.details) {
      details.innerHTML = this.formatDetails(options.details);
      details.style.display = 'block';
    } else {
      details.style.display = 'none';
    }
    
    // Store callback
    this.onCloseCallback = options.onClose;
    
    // Show modal
    modal.classList.add('show');
    
    // Prevent body scrolling
    document.body.style.overflow = 'hidden';
  }

  /**
   * Format error details into HTML
   * @param {Array|Object} details - Error details
   * @returns {string} - Formatted HTML
   */
  formatDetails(details) {
    if (!details) return '';
    
    let html = '<div class="enhanced-error-details-title"><i class="bi bi-info-circle"></i> Error Details</div>';
    
    if (Array.isArray(details)) {
      // Handle array of errors
      details.forEach((detail, index) => {
        html += this.formatDetailItem(detail, index);
      });
    } else if (typeof details === 'object') {
      // Handle single error object
      html += this.formatDetailItem(details);
    } else if (typeof details === 'string') {
      // Handle string
      try {
        // Try to parse as JSON
        const parsedDetails = JSON.parse(details);
        if (Array.isArray(parsedDetails)) {
          parsedDetails.forEach((detail, index) => {
            html += this.formatDetailItem(detail, index);
          });
        } else {
          html += this.formatDetailItem(parsedDetails);
        }
      } catch (e) {
        // Not JSON, just display as text
        html += `<div class="enhanced-error-property">
          <div class="enhanced-error-property-value">${details}</div>
        </div>`;
      }
    }
    
    return html;
  }

  /**
   * Format a single detail item
   * @param {Object} detail - Detail object
   * @param {number} index - Index for multiple items
   * @returns {string} - Formatted HTML
   */
  formatDetailItem(detail, index) {
    if (!detail) return '';
    
    let html = '';
    
    // If it's a validation error with specific format
    if (detail.code && detail.message) {
      html += `<div class="enhanced-error-property">
        <div class="enhanced-error-property-name">Error Code</div>
        <div class="enhanced-error-property-value">${detail.code}</div>
      </div>
      <div class="enhanced-error-property">
        <div class="enhanced-error-property-name">Message</div>
        <div class="enhanced-error-property-value">${detail.message}</div>
      </div>`;
      
      // Add target if available
      if (detail.target) {
        html += `<div class="enhanced-error-property">
          <div class="enhanced-error-property-name">Field</div>
          <div class="enhanced-error-property-value">${detail.target}</div>
        </div>`;
      }
      
      // Add propertyPath if available
      if (detail.propertyPath) {
        html += `<div class="enhanced-error-property">
          <div class="enhanced-error-property-name">Property Path</div>
          <div class="enhanced-error-property-value">${detail.propertyPath}</div>
        </div>`;
      }
      
      // Add details if available
      if (detail.details) {
        html += `<div class="enhanced-error-property">
          <div class="enhanced-error-property-name">Details</div>
          <div class="enhanced-error-property-value">${detail.details}</div>
        </div>`;
      }
    } else {
      // Generic object, display all properties
      Object.entries(detail).forEach(([key, value]) => {
        html += `<div class="enhanced-error-property">
          <div class="enhanced-error-property-name">${this.formatPropertyName(key)}</div>
          <div class="enhanced-error-property-value">${this.formatPropertyValue(value)}</div>
        </div>`;
      });
    }
    
    return html;
  }

  /**
   * Format property name for display
   * @param {string} name - Property name
   * @returns {string} - Formatted name
   */
  formatPropertyName(name) {
    return name
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .replace(/_/g, ' ');
  }

  /**
   * Format property value for display
   * @param {any} value - Property value
   * @returns {string} - Formatted value
   */
  formatPropertyValue(value) {
    if (value === null || value === undefined) {
      return '<em>None</em>';
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    
    return String(value);
  }

  /**
   * Hide the error modal
   */
  hide() {
    const modal = document.getElementById(this.modalId);
    modal.classList.remove('show');
    
    // Restore body scrolling
    document.body.style.overflow = '';
    
    // Call onClose callback if provided
    if (typeof this.onCloseCallback === 'function') {
      this.onCloseCallback();
    }
  }
}

// Create global instance
window.enhancedErrorModal = new EnhancedErrorModal();
