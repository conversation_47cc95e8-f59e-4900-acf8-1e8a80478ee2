const BQETestUtils = require('./bqeTestUtils');

async function runTests() {
    console.log('🧪 Starting BQE to LHDN Mapping Tests...\n');

    try {
        // Run quick test with sample data
        console.log('Running Quick Test...');
        console.log('====================');
        await BQETestUtils.quickTest();

        // You can add more test cases here
        // For example:
        const testUtils = new BQETestUtils();

        // Test case with tax exemption
        console.log('\nRunning Tax Exemption Test...');
        console.log('============================');
        const taxExemptData = {
            _rawInvoice: {
                invoiceNumber: 'EXEMPT-001',
                date: new Date().toISOString(),
                currency: 'MYR',
                serviceAmount: 2000,
                serviceTaxAmount: 0,
                invoiceAmount: 2000
            },
            supplier: {
                name: 'Exempt Supplier',
                tin: 'SUP789',
                registrationNumber: 'BRN789',
                sstId: 'SST789',
                address: {
                    line1: '789 Exempt Street',
                    city: 'Test City',
                    state: '14',
                    postcode: '67890',
                    country: 'MYS'
                }
            },
            buyer: {
                name: 'Exempt Buyer',
                tin: 'BUY789',
                registrationNumber: 'BRN987',
                sstId: 'SST987',
                address: {
                    line1: '987 Exempt Avenue',
                    city: 'Test City',
                    state: '14',
                    postcode: '98765',
                    country: 'MYS'
                }
            },
            _projectDetailsArray: [{
                details: {
                    customFields: [
                        {
                            label: 'TAX TYPE (CODE)',
                            value: 'E',
                            description: 'Tax Exempt'
                        },
                        {
                            label: 'Details of Tax Exemption',
                            value: 'Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018'
                        }
                    ]
                }
            }]
        };

        await testUtils.testMapping(taxExemptData);

        // Test case with line items
        console.log('\nRunning Line Items Test...');
        console.log('=========================');
        const lineItemsData = {
            _rawInvoice: {
                invoiceNumber: 'LINE-001',
                date: new Date().toISOString(),
                currency: 'MYR',
                serviceAmount: 3000,
                serviceTaxAmount: 180,
                invoiceAmount: 3180
            },
            supplier: {
                name: 'Line Items Supplier',
                tin: 'SUP456',
                registrationNumber: 'BRN456',
                sstId: 'SST456',
                address: {
                    line1: '456 Line Street',
                    city: 'Test City',
                    state: '14',
                    postcode: '45678',
                    country: 'MYS'
                }
            },
            buyer: {
                name: 'Line Items Buyer',
                tin: 'BUY456',
                registrationNumber: 'BRN654',
                sstId: 'SST654',
                address: {
                    line1: '654 Line Avenue',
                    city: 'Test City',
                    state: '14',
                    postcode: '65432',
                    country: 'MYS'
                }
            },
            _projectDetailsArray: [{
                details: {
                    customFields: [
                        {
                            label: 'TAX TYPE (CODE)',
                            value: '02',
                            description: 'Service Tax'
                        }
                    ]
                }
            }],
            line_items: [
                {
                    description: 'Service Item 1',
                    amount: 2000,
                    tax: {
                        type: '02',
                        rate: 6,
                        amount: 120
                    }
                },
                {
                    description: 'Service Item 2',
                    amount: 1000,
                    tax: {
                        type: '02',
                        rate: 6,
                        amount: 60
                    }
                }
            ]
        };

        await testUtils.testMapping(lineItemsData);

        console.log('\n✅ All tests completed successfully!');

    } catch (error) {
        console.error('\n❌ Test suite failed:', error);
        process.exit(1);
    }
}

// Run the tests
runTests().catch(console.error); 