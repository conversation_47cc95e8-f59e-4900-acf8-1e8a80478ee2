'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('PortalSettings', {
      ID: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      UserID: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      Settings: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      CreateTS: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('GETDATE')
      },
      UpdateTS: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('GETDATE')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('PortalSettings');
  }
}; 