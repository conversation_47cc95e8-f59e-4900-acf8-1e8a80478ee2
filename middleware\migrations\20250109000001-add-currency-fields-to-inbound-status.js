'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add documentCurrency column
      await queryInterface.addColumn('WP_INBOUND_STATUS', 'documentCurrency', {
        type: Sequelize.STRING(10),
        allowNull: true,
        defaultValue: 'MYR'
      });

      // Add currency column (if it doesn't exist)
      await queryInterface.addColumn('WP_INBOUND_STATUS', 'currency', {
        type: Sequelize.STRING(10),
        allowNull: true,
        defaultValue: 'MYR'
      });

      console.log('Successfully added currency fields to WP_INBOUND_STATUS table');
    } catch (error) {
      // If columns already exist, that's okay
      if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
        console.log('Currency columns already exist, skipping...');
      } else {
        throw error;
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('WP_INBOUND_STATUS', 'documentCurrency');
      await queryInterface.removeColumn('WP_INBOUND_STATUS', 'currency');
      console.log('Successfully removed currency fields from WP_INBOUND_STATUS table');
    } catch (error) {
      console.log('Error removing currency columns:', error.message);
    }
  }
};
