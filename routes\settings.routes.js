const express = require('express');
const router = express.Router();
const isAuthenticated = require('../middleware/auth.middleware');
const PortalSettings = require('../models/PortalSettings');
const EInvoiceSettings = require('../models/EInvoiceSettings');
const NotificationSettings = require('../models/NotificationSettings');

// Page Routes
router.get('/settings', isAuthenticated, (req, res) => {
    res.render('dashboard/settings/settings.html');
});

router.get('/einvoice', isAuthenticated, (req, res) => {
    res.render('dashboard/settings/einvoice.html');
});

router.get('/notifications', isAuthenticated, (req, res) => {
    res.render('dashboard/settings/notifications.html');
});

router.get('/integrations', isAuthenticated, (req, res) => {
    res.render('dashboard/settings/integrations.html');
});

// API Routes - Portal Settings
router.get('/api/getPortalSettings', isAuthenticated, async (req, res) => {
    try {
        const settings = await PortalSettings.findOne({
            where: { userId: req.user.id }
        });
        res.json(settings || {});
    } catch (error) {
        console.error('Error fetching portal settings:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch portal settings' });
    }
});

router.post('/api/savePortalSettings', isAuthenticated, async (req, res) => {
    try {
        const [settings] = await PortalSettings.upsert({
            userId: req.user.id,
            ...req.body
        });
        res.json({ success: true, message: 'Portal settings saved successfully' });
    } catch (error) {
        console.error('Error saving portal settings:', error);
        res.status(500).json({ success: false, message: 'Failed to save portal settings' });
    }
});

// API Routes - eInvoice Settings
router.get('/api/getEInvoiceSettings', isAuthenticated, async (req, res) => {
    try {
        const settings = await EInvoiceSettings.findOne({
            where: { userId: req.user.id }
        });
        res.json(settings || {});
    } catch (error) {
        console.error('Error fetching einvoice settings:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch einvoice settings' });
    }
});

router.post('/api/saveEInvoiceSettings', isAuthenticated, async (req, res) => {
    try {
        const [settings] = await EInvoiceSettings.upsert({
            userId: req.user.id,
            ...req.body
        });
        res.json({ success: true, message: 'eInvoice settings saved successfully' });
    } catch (error) {
        console.error('Error saving einvoice settings:', error);
        res.status(500).json({ success: false, message: 'Failed to save einvoice settings' });
    }
});

// API Routes - Notification Settings
router.get('/api/getNotificationSettings', isAuthenticated, async (req, res) => {
    try {
        const settings = await NotificationSettings.findOne({
            where: { userId: req.user.id }
        });
        res.json(settings || {});
    } catch (error) {
        console.error('Error fetching notification settings:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch notification settings' });
    }
});

router.post('/api/saveNotificationSettings', isAuthenticated, async (req, res) => {
    try {
        const [settings] = await NotificationSettings.upsert({
            userId: req.user.id,
            ...req.body
        });
        res.json({ success: true, message: 'Notification settings saved successfully' });
    } catch (error) {
        console.error('Error saving notification settings:', error);
        res.status(500).json({ success: false, message: 'Failed to save notification settings' });
    }
});

// Mobile Verification Routes
router.post('/api/verifyMobile', isAuthenticated, async (req, res) => {
    try {
        const { mobileNumber } = req.body;
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 10);

        await NotificationSettings.update({
            mobileVerificationCode: verificationCode,
            mobileVerificationExpires: expiresAt
        }, {
            where: { userId: req.user.id }
        });

        // TODO: Implement SMS sending service integration
        console.log(`Verification code ${verificationCode} sent to ${mobileNumber}`);

        res.json({ success: true, message: 'Verification code sent successfully' });
    } catch (error) {
        console.error('Error sending verification code:', error);
        res.status(500).json({ success: false, message: 'Failed to send verification code' });
    }
});

router.post('/api/confirmMobileVerification', isAuthenticated, async (req, res) => {
    try {
        const { mobileNumber, code } = req.body;
        const settings = await NotificationSettings.findOne({
            where: { userId: req.user.id }
        });

        if (!settings || 
            settings.mobileVerificationCode !== code || 
            new Date() > settings.mobileVerificationExpires) {
            return res.status(400).json({ success: false, message: 'Invalid or expired verification code' });
        }

        await settings.update({
            mobileNumber,
            mobileVerified: true,
            mobileVerificationCode: null,
            mobileVerificationExpires: null
        });

        res.json({ success: true, message: 'Mobile number verified successfully' });
    } catch (error) {
        console.error('Error verifying mobile number:', error);
        res.status(500).json({ success: false, message: 'Failed to verify mobile number' });
    }
});

module.exports = router; 