class NotificationManager {
  constructor() {
    this.pageSize = 10;
    this.pageNo = 1;
    this.container = document.getElementById('notifications-container');
    this.countElement = document.getElementById('notification-count');
    this.loadingState = false;
  }

  async fetchNotifications() {
    try {
      if (this.loadingState) return null;
      this.loadingState = true;

      console.log('Fetching notifications...');
      const response = await fetch(`/api/notifications?pageSize=${this.pageSize}&pageNo=${this.pageNo}`, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'same-origin' // Include cookies
      });
      
      console.log('Response status:', response.status);
      
      const data = await response.json();
      console.log('Response data:', data);

      if (!response.ok) {
        if (response.status === 401) {
          console.log('Authentication error, redirecting to login...');
          window.location.href = '/login';
          return null;
        }
        
        throw new Error(data.message || 'Failed to fetch notifications');
      }

      if (!data.success) {
        throw new Error(data.error?.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('Error fetching notifications:', {
        message: error.message,
        stack: error.stack
      });
      return { 
        success: false, 
        result: [], 
        metadata: { hasNext: false },
        error: error.message
      };
    } finally {
      this.loadingState = false;
    }
  }

  async fetchUnreadCount() {
    try {
      const response = await fetch('/api/notifications/unread-count');
      if (!response.ok) throw new Error('Failed to fetch unread count');
      const data = await response.json();
      return data.unreadCount;
    } catch (error) {
      console.error('Error fetching unread count:', error);
      return 0;
    }
  }

  formatDateTime(dateTime) {
    if (!dateTime) return '';
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) return '';
    
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return date.toLocaleTimeString();
    } else if (days === 1) {
      return 'Yesterday';
    } else if (days < 7) {
      return `${days} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  getNotificationIcon(typeId) {
    const icons = {
      '3': 'verified',
      '6': 'description',
      '7': 'check_circle',
      '8': 'cancel',
      '10': 'person',
      '11': 'business',
      '15': 'error',
      '26': 'data_check',
      '33': 'summarize',
      '34': 'template_frame',
      '35': 'delete'
    };
    return icons[typeId] || 'notifications';
  }

  getNotificationStatus(status) {
    const statusClasses = {
      '1': 'bg-blue-100 text-blue-800',
      '2': 'bg-yellow-100 text-yellow-800',
      '3': 'bg-purple-100 text-purple-800',
      '4': 'bg-green-100 text-green-800',
      '5': 'bg-red-100 text-red-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  }

  renderNotification(notification) {
    const statusClass = this.getNotificationStatus(notification.status);
    return `
      <div class="p-4 hover:bg-slate-50 border-b last:border-b-0 transition-colors duration-200">
        <div class="flex items-start gap-3">
          <span class="material-symbols-outlined text-blue-600 mt-1">
            ${this.getNotificationIcon(notification.typeId)}
          </span>
          <div class="flex-1">
            <div class="flex items-center justify-between gap-4">
              <p class="font-medium text-gray-900">${notification.notificationSubject}</p>
              <span class="px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                ${notification.status === '1' ? 'New' : 'Read'}
              </span>
            </div>
            <p class="text-sm text-slate-600 mt-1">${notification.finalMessage}</p>
            <div class="flex items-center gap-4 mt-2">
              <p class="text-xs text-slate-400">
                ${this.formatDateTime(notification.creationDateTime)}
              </p>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  async updateNotifications() {
    try {
      // Show loading state
      this.container.innerHTML = `
        <div class="py-4 text-center text-slate-500">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-2">Loading notifications...</p>
        </div>
      `;

      const data = await this.fetchNotifications();
      
      // Handle authentication errors
      if (!data) {
        return; // Already handled in fetchNotifications
      }

      // Handle API errors
      if (!data.success) {
        throw new Error(data.error?.message || 'Failed to fetch notifications');
      }

      const { result: notifications } = data;
      
      if (!notifications || notifications.length === 0) {
        this.container.innerHTML = `
          <div class="py-8 text-center text-slate-500">
            <span class="material-symbols-outlined text-4xl mb-2">notifications_off</span>
            <p>No new notifications</p>
          </div>
        `;
        this.countElement.textContent = '0';
        return;
      }

      this.container.innerHTML = notifications
        .map(notification => this.renderNotification(notification))
        .join('');
      
      const unreadCount = await this.fetchUnreadCount();
      this.countElement.textContent = unreadCount;

    } catch (error) {
      console.error('Error updating notifications:', {
        message: error.message,
        stack: error.stack
      });
      
      this.container.innerHTML = `
        <div class="py-8 text-center text-red-500">
          <span class="material-symbols-outlined text-4xl mb-2">error</span>
          <p>Failed to load notifications</p>
          <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Try Again
          </button>
        </div>
      `;
    }
  }

  init() {
    this.updateNotifications();
    // Update notifications every 5 minutes
    setInterval(() => this.updateNotifications(), 5 * 60 * 1000);
  }
}

// Initialize notifications when document is ready
document.addEventListener('DOMContentLoaded', () => {
  const notificationManager = new NotificationManager();
  notificationManager.init();
}); 