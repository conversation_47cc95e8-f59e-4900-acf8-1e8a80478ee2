# LHDN Tax Calculation Documentation

## Overview

This document explains the tax calculation logic implemented in the LHDN (Royal Malaysian Customs Department) integration for eInvoicing. The primary components involved in tax calculation are:

1. `services/bqe/dataProcessor.js` - Processes raw invoice data and extracts tax information
2. `services/bqe/mapper.js` - Maps processed data to the LHDN format

## Tax Calculation Process

### 1. Data Processing Stage

In `dataProcessor.js`, the system:

1. Extracts tax type code from project custom fields (e.g., '01', '02', 'E')
2. Normalizes tax type codes (converts single character codes to standard LHDN format)
3. Determines if an invoice is tax exempt (when tax type is 'E' or '06')
4. Calculates tax rate using a centralized method (`getTaxRate`)
5. Sets tax amount to zero for exempt invoices, or calculates it as `amount * rate / 100` for taxable invoices

### 2. Mapping Stage

In `mapper.js`, the system:

1. Uses already-processed tax information from `dataProcessor.js`
2. Ensures consistent tax calculation between invoice total and line items
3. For tax-inclusive amounts, back-calculates the tax-exclusive base amount
4. Sets `TaxExclusiveAmount` and `LineExtensionAmount` to equal values (tax exclusive amount)
5. Sets `TaxInclusiveAmount` and `PayableAmount` to invoice total amount

## Tax Rate Determination Logic

Tax rates are determined using the following hierarchy:

1. If tax type is 'E' or '06' (exempt), tax rate is always 0
2. Otherwise, look for tax rate in:
   - Project custom fields (labeled 'Tax Rate' or 'SERVICE TAX RATE')
   - Project details' mainServiceTax
   - Invoice custom fields (labeled 'Tax rate')
   - Raw invoice tax rate
   - Default to 8.00% if no rate is found

## Tax Type Normalization

Tax types are normalized to standard LHDN codes:
- '01' - Sales Tax
- '02' - Service Tax
- '03' - Tourism Tax
- '04' - High-Value Goods Tax
- '05' - Sales Tax on Low Value Goods
- '06' - Not Applicable
- 'E' - Tax Exemption

Single-character codes are mapped as follows:
- 'S' → '02' (Service Tax)
- 'G' → '01' (Sales Tax)
- 'T' → '03' (Tourism Tax)
- 'H' → '04' (High-Value Goods Tax)
- 'L' → '05' (Low Value Goods Tax)
- 'N/NA' → '06' (Not Applicable)
- 'OTH' → '06' (Not Applicable)

## Example Calculation

For a non-exempt invoice with:
- Total amount: 10,000.00 MYR
- Tax rate: 8%

The calculation is:
- Tax amount = 10,000.00 * 8/100 = 800.00 MYR
- Tax exclusive amount = 10,000.00 - 800.00 = 9,200.00 MYR

For an exempt invoice:
- Tax amount = 0
- Tax exclusive amount = Total amount

## Known Issues Fixed

1. Inconsistent tax calculations between invoice totals and line items
2. Duplicate calculation logic between files
3. Incorrect handling of tax exemption cases
4. Missing or incorrect tax type normalization 