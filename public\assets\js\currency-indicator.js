/**
 * Currency Indicator
 * A utility for displaying foreign currency values with MYR conversion
 */
class CurrencyIndicator {
  constructor() {
    this.defaultCurrency = 'MYR';
    this.defaultExchangeRate = 1.0;
    this.init();
  }

  init() {
    // Add event listeners for dynamic content
    document.addEventListener('DOMContentLoaded', () => {
      this.initCurrencyElements();
    });
    
    // Initialize any existing currency elements
    this.initCurrencyElements();
  }

  initCurrencyElements() {
    // Find all elements with the currency-value class
    const currencyElements = document.querySelectorAll('.currency-value');
    currencyElements.forEach(element => {
      this.processCurrencyElement(element);
    });
    
    // Find all elements with the currency-cell class
    const currencyCells = document.querySelectorAll('.currency-cell');
    currencyCells.forEach(cell => {
      this.processCurrencyCell(cell);
    });
  }

  processCurrencyElement(element) {
    // Skip if already processed
    if (element.dataset.processed === 'true') return;
    
    // Get currency and value from data attributes
    const currency = element.dataset.currency || this.defaultCurrency;
    const value = parseFloat(element.dataset.value || 0);
    const exchangeRate = parseFloat(element.dataset.exchangeRate || this.defaultExchangeRate);
    
    // Skip processing for MYR currency
    if (currency === 'MYR') {
      element.textContent = this.formatCurrency(value, currency);
      element.dataset.processed = 'true';
      return;
    }
    
    // Calculate MYR value
    const myrValue = value * exchangeRate;
    
    // Create the foreign currency display
    const foreignCurrencySpan = document.createElement('span');
    foreignCurrencySpan.className = 'currency-value-foreign';
    foreignCurrencySpan.textContent = this.formatCurrency(value, currency);
    
    // Create the MYR value display
    const myrValueSpan = document.createElement('span');
    myrValueSpan.className = 'currency-value-myr';
    myrValueSpan.textContent = this.formatCurrency(myrValue, 'MYR').replace('MYR ', '');
    
    // Create currency badge if needed
    if (element.dataset.showBadge === 'true') {
      const currencyBadge = document.createElement('span');
      currencyBadge.className = 'currency-badge';
      currencyBadge.textContent = currency;
      foreignCurrencySpan.appendChild(currencyBadge);
    }
    
    // Clear the element and append the new content
    element.innerHTML = '';
    element.appendChild(foreignCurrencySpan);
    element.appendChild(myrValueSpan);
    
    // Add tooltip if needed
    if (element.dataset.showTooltip === 'true') {
      this.addCurrencyTooltip(element, currency, exchangeRate);
    }
    
    // Mark as processed
    element.dataset.processed = 'true';
  }

  processCurrencyCell(cell) {
    // Skip if already processed
    if (cell.dataset.processed === 'true') return;
    
    // Get currency and value from data attributes
    const currency = cell.dataset.currency || this.defaultCurrency;
    const value = parseFloat(cell.dataset.value || 0);
    const exchangeRate = parseFloat(cell.dataset.exchangeRate || this.defaultExchangeRate);
    
    // Skip processing for MYR currency
    if (currency === 'MYR') {
      cell.textContent = this.formatCurrency(value, currency);
      cell.dataset.processed = 'true';
      return;
    }
    
    // Calculate MYR value
    const myrValue = value * exchangeRate;
    
    // Create the foreign currency display
    const foreignCurrencySpan = document.createElement('span');
    foreignCurrencySpan.className = 'currency-cell-foreign';
    foreignCurrencySpan.textContent = this.formatCurrency(value, currency);
    
    // Create the MYR value display
    const myrValueSpan = document.createElement('span');
    myrValueSpan.className = 'currency-cell-myr';
    myrValueSpan.textContent = this.formatCurrency(myrValue, 'MYR').replace('MYR ', '');
    
    // Clear the cell and append the new content
    cell.innerHTML = '';
    cell.appendChild(foreignCurrencySpan);
    cell.appendChild(myrValueSpan);
    
    // Mark as processed
    cell.dataset.processed = 'true';
  }

  addCurrencyTooltip(element, currency, exchangeRate) {
    // Create tooltip container
    const tooltipContainer = document.createElement('div');
    tooltipContainer.className = 'currency-tooltip';
    
    // Create tooltip text
    const tooltipText = document.createElement('span');
    tooltipText.className = 'currency-tooltip-text';
    tooltipText.innerHTML = `
      <strong>Currency:</strong> ${currency}<br>
      <strong>Exchange Rate:</strong> 1 ${currency} = ${exchangeRate} MYR<br>
      <strong>Last Updated:</strong> ${new Date().toLocaleDateString()}
    `;
    
    // Wrap the element in the tooltip container
    element.parentNode.insertBefore(tooltipContainer, element);
    tooltipContainer.appendChild(element);
    tooltipContainer.appendChild(tooltipText);
  }

  /**
   * Format a currency value
   * @param {number} value - The value to format
   * @param {string} currency - The currency code
   * @returns {string} - Formatted currency string
   */
  formatCurrency(value, currency = 'MYR') {
    if (value === null || value === undefined || isNaN(value)) {
      return `${currency} 0.00`;
    }
    
    return `${currency} ${parseFloat(value).toLocaleString('en-MY', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  }

  /**
   * Create a currency header with exchange rate information
   * @param {string} containerId - The ID of the container element
   * @param {string} currency - The currency code
   * @param {number} exchangeRate - The exchange rate
   */
  createCurrencyHeader(containerId, currency, exchangeRate) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const header = document.createElement('div');
    header.className = 'currency-header';
    header.innerHTML = `
      <h5 class="currency-header-title">Currency: ${currency}</h5>
      <div class="currency-header-info">
        <span class="exchange-rate-indicator">
          <i class="bi bi-currency-exchange"></i>
          1 ${currency} = ${exchangeRate} MYR
        </span>
        <span class="currency-badge">${currency}</span>
      </div>
    `;
    
    container.insertBefore(header, container.firstChild);
  }

  /**
   * Update all currency elements with new exchange rate
   * @param {number} newExchangeRate - The new exchange rate
   */
  updateExchangeRate(newExchangeRate) {
    this.defaultExchangeRate = newExchangeRate;
    
    // Reset all currency elements
    const currencyElements = document.querySelectorAll('.currency-value, .currency-cell');
    currencyElements.forEach(element => {
      element.dataset.processed = 'false';
      element.dataset.exchangeRate = newExchangeRate;
    });
    
    // Re-process all elements
    this.initCurrencyElements();
  }
}

// Create global instance
window.currencyIndicator = new CurrencyIndicator();
