<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Postcode Cleaning Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-case {
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #f8f9fa;
        }
        .test-result {
            font-family: monospace;
            background: #fff;
            padding: 0.5rem;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-5">Postcode Cleaning Test</h1>
        
        <div class="alert alert-info">
            <strong>LHDN Requirement:</strong> Postcodes must be maximum 5 characters and contain only alphanumeric characters.
        </div>

        <div id="test-results"></div>

        <div class="mt-4">
            <h3>Manual Test</h3>
            <div class="input-group mb-3">
                <input type="text" id="manual-postcode" class="form-control" placeholder="Enter postcode to test">
                <button class="btn btn-primary" onclick="testManualPostcode()">Test</button>
            </div>
            <div id="manual-result"></div>
        </div>
    </div>

    <script src="/assets/js/shared/utils.js"></script>
    <script>
        // Test cases for postcode cleaning
        const testCases = [
            { input: '50450', expected: '50450', description: 'Valid 5-digit postcode' },
            { input: '50450-1234', expected: '50450', description: 'Postcode with dash and extra digits' },
            { input: '504 50', expected: '50450', description: 'Postcode with space' },
            { input: '50-450', expected: '50450', description: 'Postcode with dash in middle' },
            { input: '50450ABC', expected: '50450', description: 'Postcode with letters (should keep only first 5)' },
            { input: 'ABC50450', expected: '50450', description: 'Postcode with letters at start' },
            { input: '50450-', expected: '50450', description: 'Postcode with trailing dash' },
            { input: ' 50450 ', expected: '50450', description: 'Postcode with leading/trailing spaces' },
            { input: '5-0-4-5-0', expected: '50450', description: 'Postcode with multiple dashes' },
            { input: '504501234', expected: '50450', description: 'Long postcode (should truncate to 5)' },
            { input: '', expected: '', description: 'Empty postcode' },
            { input: '   ', expected: '', description: 'Whitespace only' },
            { input: '---', expected: '', description: 'Dashes only' },
            { input: '12345', expected: '12345', description: 'Another valid 5-digit postcode' },
            { input: '1234', expected: '1234', description: '4-digit postcode (valid but short)' },
            { input: '123', expected: '123', description: '3-digit postcode (valid but short)' }
        ];

        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            let passedTests = 0;
            let totalTests = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = Utils.cleanPostcode(testCase.input);
                const passed = result === testCase.expected;
                
                if (passed) passedTests++;

                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <span><strong>Test ${index + 1}:</strong> ${testCase.description}</span>
                        <span class="${passed ? 'success' : 'error'}">${passed ? '✓ PASS' : '✗ FAIL'}</span>
                    </div>
                    <div class="test-result">
                        <strong>Input:</strong> "${testCase.input}"<br>
                        <strong>Expected:</strong> "${testCase.expected}"<br>
                        <strong>Actual:</strong> "${result}"
                        ${!passed ? `<br><span class="error">❌ Test failed!</span>` : ''}
                    </div>
                `;
                resultsContainer.appendChild(testDiv);
            });

            // Summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `alert ${passedTests === totalTests ? 'alert-success' : 'alert-warning'}`;
            summaryDiv.innerHTML = `
                <h4>Test Summary</h4>
                <p><strong>${passedTests}/${totalTests}</strong> tests passed</p>
                ${passedTests === totalTests ? 
                    '<p class="success">🎉 All tests passed! Postcode cleaning is working correctly.</p>' : 
                    '<p class="warning">⚠️ Some tests failed. Please check the implementation.</p>'
                }
            `;
            resultsContainer.appendChild(summaryDiv);
        }

        function testManualPostcode() {
            const input = document.getElementById('manual-postcode').value;
            const result = Utils.cleanPostcode(input);
            const isValid = Utils.isValidPostcode(input);
            
            const resultDiv = document.getElementById('manual-result');
            resultDiv.innerHTML = `
                <div class="test-result">
                    <strong>Input:</strong> "${input}"<br>
                    <strong>Cleaned:</strong> "${result}"<br>
                    <strong>Valid Malaysian Postcode:</strong> <span class="${isValid ? 'success' : 'error'}">${isValid ? 'Yes' : 'No'}</span><br>
                    <strong>Length:</strong> ${result.length} characters<br>
                    <strong>LHDN Compliant:</strong> <span class="${result.length <= 5 ? 'success' : 'error'}">${result.length <= 5 ? 'Yes' : 'No'}</span>
                </div>
            `;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
