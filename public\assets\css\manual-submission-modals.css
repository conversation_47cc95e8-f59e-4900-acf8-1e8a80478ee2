/* Modern Manual Submission Modals - Glass Morphism Design */
:root {
  /* Primary Colors - Modern Blue Gradient */
  --manual-primary: #3b82f6;
  --manual-primary-light: #eff6ff;
  --manual-primary-dark: #1e40af;
  --manual-primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);

  /* Status Colors */
  --manual-success: #10b981;
  --manual-success-light: #ecfdf5;
  --manual-success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --manual-error: #ef4444;
  --manual-error-light: #fef2f2;
  --manual-error-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --manual-warning: #f59e0b;
  --manual-warning-light: #fffbeb;
  --manual-warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

  /* Text Colors */
  --manual-text-primary: #1f2937;
  --manual-text-secondary: #6b7280;
  --manual-text-muted: #9ca3af;
  --manual-text-light: #f9fafb;

  /* Background & Borders */
  --manual-bg-primary: #ffffff;
  --manual-bg-secondary: #f8fafc;
  --manual-bg-glass: rgba(255, 255, 255, 0.95);
  --manual-border: #e5e7eb;
  --manual-border-light: #f3f4f6;

  /* Shadows & Effects */
  --manual-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --manual-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --manual-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --manual-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --manual-glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Border Radius */
  --manual-radius-sm: 8px;
  --manual-radius: 12px;
  --manual-radius-lg: 16px;
  --manual-radius-xl: 20px;

  /* Spacing */
  --manual-space-xs: 0.5rem;
  --manual-space-sm: 0.75rem;
  --manual-space-md: 1rem;
  --manual-space-lg: 1.5rem;
  --manual-space-xl: 2rem;
  --manual-space-2xl: 3rem;
}

/* Manual Submission Modal Base Styles */
.manual-submission-modal {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.manual-submission-modal .swal2-popup {
  border-radius: var(--manual-radius-lg) !important;
  box-shadow: var(--manual-shadow-lg) !important;
  border: none !important;
  padding: 0 !important;
  overflow: hidden !important;
  animation: manualModalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
}

@keyframes manualModalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Manual Modal Header */
.manual-modal-header {
  background: linear-gradient(135deg, var(--manual-primary) 0%, var(--manual-primary-dark) 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.manual-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.manual-modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  position: relative;
  z-index: 1;
}

.manual-modal-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  position: relative;
  z-index: 1;
}

.manual-modal-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  font-size: 1.5rem;
  position: relative;
  z-index: 1;
  animation: manualIconPulse 2s infinite;
}

@keyframes manualIconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Manual Modal Body */
.manual-modal-body {
  padding: 2rem;
  background: white;
}

/* Version Selection Cards */
.manual-version-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.manual-version-card {
  border: 2px solid var(--manual-border);
  border-radius: var(--manual-radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  position: relative;
  overflow: hidden;
}

.manual-version-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
  transition: left 0.5s;
}

.manual-version-card:hover::before {
  left: 100%;
}

.manual-version-card:hover {
  border-color: var(--manual-primary);
  box-shadow: var(--manual-shadow);
  transform: translateY(-2px);
}

.manual-version-card.selected {
  border-color: var(--manual-primary);
  background: var(--manual-primary-light);
  box-shadow: var(--manual-shadow);
  transform: translateY(-2px);
}

.manual-version-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f9fafb;
}

.manual-version-card.disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: var(--manual-border);
}

.manual-version-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.manual-version-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--manual-text-primary);
}

.manual-version-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.manual-badge-available {
  background: var(--manual-success-light);
  color: var(--manual-success);
}

.manual-badge-coming-soon {
  background: var(--manual-warning-light);
  color: var(--manual-warning);
}

.manual-version-description {
  color: var(--manual-text-secondary);
  line-height: 1.5;
}

.manual-version-card.selected::after {
  content: '✓';
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 24px;
  height: 24px;
  background: var(--manual-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: bold;
  animation: manualCheckmarkBounce 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes manualCheckmarkBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Confirmation Details */
.manual-details-container {
  background: #f8fafc;
  border-radius: var(--manual-radius);
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.manual-details-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--manual-border);
}

.manual-details-icon {
  width: 40px;
  height: 40px;
  background: var(--manual-primary-light);
  color: var(--manual-primary);
  border-radius: var(--manual-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
}

.manual-details-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--manual-text-primary);
}

.manual-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.manual-detail-row:last-child {
  border-bottom: none;
}

.manual-detail-label {
  font-weight: 500;
  color: var(--manual-text-secondary);
}

.manual-detail-value {
  color: var(--manual-text-primary);
  font-weight: 600;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

/* Progress Steps */
.manual-steps-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.manual-step-card {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  border-radius: var(--manual-radius);
  border: 2px solid var(--manual-border-light);
  background: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.6;
  transform: translateX(-10px);
}

.manual-step-card.processing {
  opacity: 1;
  transform: translateX(0);
  border-color: var(--manual-primary);
  background: var(--manual-primary-light);
  box-shadow: var(--manual-shadow);
}

.manual-step-card.completed {
  opacity: 1;
  transform: translateX(0);
  border-color: var(--manual-success);
  background: var(--manual-success-light);
}

.manual-step-card.error {
  opacity: 1;
  transform: translateX(0);
  border-color: var(--manual-error);
  background: var(--manual-error-light);
}

.manual-step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.25rem;
  position: relative;
  flex-shrink: 0;
}

.manual-step-card.processing .manual-step-icon {
  background: white;
  color: var(--manual-primary);
}

.manual-step-card.completed .manual-step-icon {
  background: var(--manual-success);
  color: white;
}

.manual-step-card.error .manual-step-icon {
  background: var(--manual-error);
  color: white;
}

.manual-step-icon.spinning::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  border: 3px solid var(--manual-primary);
  border-right-color: transparent;
  border-radius: 50%;
  animation: manualSpinner 1s linear infinite;
  z-index: 10;
}

@keyframes manualSpinner {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Ensure proper transitions for step status changes */
.manual-step-card * {
  transition: all 0.3s ease !important;
}

/* Force visibility of status changes */
.manual-step-card.processing {
  border-color: var(--manual-primary) !important;
  background: var(--manual-primary-light) !important;
  opacity: 1 !important;
  transform: translateX(0) !important;
}

.manual-step-card.completed {
  border-color: var(--manual-success) !important;
  background: var(--manual-success-light) !important;
  opacity: 1 !important;
  transform: translateX(0) !important;
}

.manual-step-card.error {
  border-color: var(--manual-error) !important;
  background: var(--manual-error-light) !important;
  opacity: 1 !important;
  transform: translateX(0) !important;
}

.manual-step-content {
  flex: 1;
}

.manual-step-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--manual-text-primary);
  margin-bottom: 0.25rem;
}

.manual-step-status {
  color: var(--manual-text-secondary);
  font-size: 0.875rem;
  transition: opacity 0.3s;
}

/* Success Modal */
.manual-success-container {
  text-align: center;
  padding: 1rem 0;
}

.manual-success-icon {
  width: 80px;
  height: 80px;
  background: var(--manual-success-light);
  color: var(--manual-success);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
  font-size: 2rem;
  animation: manualSuccessBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes manualSuccessBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.manual-success-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--manual-text-primary);
  margin-bottom: 0.5rem;
}

.manual-success-message {
  color: var(--manual-text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Error Modal */
.manual-error-container {
  text-align: center;
  padding: 1rem 0;
}

.manual-error-icon {
  width: 80px;
  height: 80px;
  background: var(--manual-error-light);
  color: var(--manual-error);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
  font-size: 2rem;
  animation: manualErrorShake 0.6s;
}

@keyframes manualErrorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* SweetAlert2 Button Container Spacing */
.manual-submission-modal .swal2-actions {
  gap: 1rem !important;
  margin-top: 1.5rem !important;
  padding: 1.5rem !important;
  background: #f8f9fa !important;
  border-top: 1px solid var(--manual-border) !important;
  border-radius: 0 0 var(--manual-radius-lg) var(--manual-radius-lg) !important;
  justify-content: flex-end !important;
}

/* Buttons */
.manual-btn {
  padding: 0.75rem 2rem;
  border-radius: var(--manual-radius);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  margin: 0 !important; /* Override SweetAlert2 default margins */
}

.manual-btn-primary {
  background: var(--manual-primary);
  color: white;
}

.manual-btn-primary:hover {
  background: var(--manual-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--manual-shadow);
}

.manual-btn-secondary {
  background: #f3f4f6;
  color: var(--manual-text-primary);
  border: 1px solid var(--manual-border);
}

.manual-btn-secondary:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

/* Additional SweetAlert2 button overrides */
.manual-submission-modal .swal2-styled {
  margin: 0 !important;
  padding: 0.75rem 2rem !important;
  min-width: auto !important;
}

.manual-submission-modal .swal2-styled:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15) !important;
}

/* Compact Success Modal */
.manual-success-modal .swal2-popup {
  max-height: 80vh !important;
  overflow-y: auto !important;
}

.manual-success-modal .manual-modal-body {
  padding: 1rem !important;
}

.manual-success-modal .manual-details-container {
  margin-top: 0.75rem !important;
  padding: 0.75rem !important;
}

.manual-success-modal .manual-detail-row {
  padding: 0.375rem 0 !important;
  font-size: 0.875rem !important;
}

.manual-success-modal .manual-details-header {
  margin-bottom: 0.75rem !important;
  padding-bottom: 0.5rem !important;
}

.manual-success-modal .manual-success-container {
  padding: 0.5rem 0 !important;
}

.manual-success-modal .manual-success-icon {
  width: 50px !important;
  height: 50px !important;
  margin: 0 auto 0.75rem auto !important;
  font-size: 1.5rem !important;
}

/* Compact Error Modal */
.manual-error-modal .swal2-popup {
  max-height: 80vh !important;
  overflow-y: auto !important;
}

.manual-error-modal .manual-modal-body {
  padding: 1rem !important;
}

.manual-error-modal .manual-details-container {
  margin-top: 0.75rem !important;
  padding: 0.75rem !important;
}

.manual-error-modal .manual-detail-row {
  padding: 0.375rem 0 !important;
  font-size: 0.875rem !important;
}

.manual-error-modal .manual-details-header {
  margin-bottom: 0.75rem !important;
  padding-bottom: 0.5rem !important;
}

.manual-error-modal .manual-error-container {
  padding: 0.5rem 0 !important;
}

.manual-error-modal .manual-error-icon {
  width: 50px !important;
  height: 50px !important;
  margin: 0 auto 0.75rem auto !important;
  font-size: 1.5rem !important;
}

/* Responsive Design */
@media (max-width: 640px) {
  .manual-modal-header {
    padding: 1.5rem;
  }

  .manual-modal-body {
    padding: 1.5rem;
  }

  .manual-modal-title {
    font-size: 1.25rem;
  }

  .manual-version-card {
    padding: 1rem;
  }

  .manual-step-card {
    padding: 1rem;
  }

  .manual-step-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --manual-text-primary: #f9fafb;
    --manual-text-secondary: #d1d5db;
    --manual-text-muted: #9ca3af;
    --manual-border: #374151;
    --manual-border-light: #4b5563;
  }

  .manual-modal-body {
    background: #1f2937;
  }

  .manual-version-card {
    background: #1f2937;
    border-color: var(--manual-border);
  }

  .manual-details-container {
    background: #111827;
  }

  .manual-step-card {
    background: #1f2937;
  }
}
