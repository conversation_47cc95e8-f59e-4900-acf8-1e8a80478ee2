const express = require('express');
const router = express.Router();
const settingsService = require('../../services/settings.service');
const { isAuthenticated } = require('../../middleware/auth');

// Get settings
router.get('/getSettings', isAuthenticated, async (req, res) => {
    try {
        const companyId = req.user.company_id;
        const result = await settingsService.getSettings(companyId);
        res.json(result);
    } catch (error) {
        console.error('Error getting settings:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Failed to get settings'
        });
    }
});

// Save settings
router.post('/saveSettings', isAuthenticated, async (req, res) => {
    try {
        const companyId = req.user.company_id;
        const result = await settingsService.saveSettings(companyId, req.body);
        res.json(result);
    } catch (error) {
        console.error('Error saving settings:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Failed to save settings'
        });
    }
});

// Test connection
router.post('/testConnection', isAuthenticated, async (req, res) => {
    try {
        const companyId = req.user.company_id;
        const result = await settingsService.testConnection(companyId);
        res.json(result);
    } catch (error) {
        console.error('Error testing connection:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Connection test failed'
        });
    }
});

module.exports = router; 