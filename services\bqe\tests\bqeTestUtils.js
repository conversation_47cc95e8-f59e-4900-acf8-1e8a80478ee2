const fs = require('fs');
const path = require('path');
const moment = require('moment');
const { mapBQEToLHDNFormat } = require('../mapper');
const BQEDataProcessor = require('../dataProcessor');
const BQELogger = require('../bqeLogger');

class BQETestUtils {
    constructor() {
        this.logDir = path.join(__dirname, '../../../logs/bqe/tests');
        this.processor = new BQEDataProcessor();
        this.logger = new BQELogger();
        this.ensureLogDirectories();
    }

    ensureLogDirectories() {
        const dirs = [
            this.logDir,
            path.join(this.logDir, 'raw'),
            path.join(this.logDir, 'processed'),
            path.join(this.logDir, 'lhdn'),
            path.join(this.logDir, 'final')
        ];

        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }

    async writeLog(type, data, invoiceNumber) {
        try {
            const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
            const safeInvoiceNumber = invoiceNumber.replace(/[^a-zA-Z0-9-]/g, '_');
            const fileName = `${type}_${safeInvoiceNumber}_${timestamp}.json`;
            const filePath = path.join(this.logDir, type, fileName);

            await fs.promises.writeFile(
                filePath,
                JSON.stringify(data, null, 2),
                'utf8'
            );

            console.log(`✅ ${type.toUpperCase()} log written to: ${filePath}`);
            return filePath;
        } catch (error) {
            console.error(`❌ Error writing ${type} log:`, error);
            throw error;
        }
    }

    validateStructure(data, stage) {
        const validationResults = {
            stage,
            timestamp: moment().format(),
            isValid: true,
            errors: [],
            warnings: []
        };

        // Basic structure validation
        if (!data) {
            validationResults.isValid = false;
            validationResults.errors.push('Data is null or undefined');
            return validationResults;
        }

        // Stage-specific validation
        switch (stage) {
            case 'raw':
                if (!data._rawInvoice) {
                    validationResults.errors.push('Missing _rawInvoice data');
                }
                if (!data.supplier) {
                    validationResults.errors.push('Missing supplier data');
                }
                if (!data.buyer) {
                    validationResults.errors.push('Missing buyer data');
                }
                break;

            case 'processed':
                if (!data.tax_info) {
                    validationResults.errors.push('Missing tax information');
                }
                if (!data.line_items) {
                    validationResults.warnings.push('Missing line items');
                }
                break;

            case 'lhdn':
                if (!data.Invoice?.[0]?.ID) {
                    validationResults.errors.push('Missing Invoice ID');
                }
                if (!data.Invoice?.[0]?.AccountingSupplierParty) {
                    validationResults.errors.push('Missing Supplier information');
                }
                if (!data.Invoice?.[0]?.AccountingCustomerParty) {
                    validationResults.errors.push('Missing Customer information');
                }
                break;
        }

        validationResults.isValid = validationResults.errors.length === 0;
        return validationResults;
    }

    async testMapping(rawData, version = '1.0') {
        try {
            console.log('🚀 Starting BQE to LHDN mapping test...\n');
            const invoiceNumber = rawData._rawInvoice?.invoiceNumber || 'test_invoice';
            const results = {
                timestamp: moment().format(),
                invoiceNumber,
                version,
                stages: []
            };

            // 1. Log Raw Data
            console.log('📝 Step 1: Logging Raw Data...');
            const rawValidation = this.validateStructure(rawData, 'raw');
            await this.writeLog('raw', {
                data: rawData,
                validation: rawValidation
            }, invoiceNumber);
            results.stages.push({ stage: 'raw', validation: rawValidation });

            // 2. Process Raw Data
            console.log('\n📝 Step 2: Processing Raw Data...');
            const processedData = await this.processor.processRawBQEData(rawData);
            const processedValidation = this.validateStructure(processedData, 'processed');
            await this.writeLog('processed', {
                data: processedData,
                validation: processedValidation
            }, invoiceNumber);
            results.stages.push({ stage: 'processed', validation: processedValidation });

            // 3. Map to LHDN Structure
            console.log('\n📝 Step 3: Mapping to LHDN Structure...');
            const lhdnData = mapBQEToLHDNFormat(processedData, version);
            const lhdnValidation = this.validateStructure(lhdnData, 'lhdn');
            await this.writeLog('lhdn', {
                data: lhdnData,
                validation: lhdnValidation
            }, invoiceNumber);
            results.stages.push({ stage: 'lhdn', validation: lhdnValidation });

            // 4. Final Structure
            console.log('\n📝 Step 4: Generating Final Structure...');
            const finalData = {
                metadata: {
                    version,
                    generatedAt: moment().format(),
                    invoiceNumber,
                    status: 'success'
                },
                validations: {
                    raw: rawValidation,
                    processed: processedValidation,
                    lhdn: lhdnValidation
                },
                data: {
                    raw: rawData,
                    processed: processedData,
                    lhdn: lhdnData
                }
            };

            await this.writeLog('final', finalData, invoiceNumber);

            // Print Summary
            console.log('\n📊 Test Summary:');
            console.log('================');
            results.stages.forEach(stage => {
                const icon = stage.validation.isValid ? '✅' : '❌';
                console.log(`${icon} ${stage.stage.toUpperCase()}: ${stage.validation.isValid ? 'Valid' : 'Invalid'}`);
                if (stage.validation.errors.length > 0) {
                    console.log('   Errors:');
                    stage.validation.errors.forEach(err => console.log(`   - ${err}`));
                }
                if (stage.validation.warnings.length > 0) {
                    console.log('   Warnings:');
                    stage.validation.warnings.forEach(warn => console.log(`   - ${warn}`));
                }
            });

            return finalData;

        } catch (error) {
            console.error('\n❌ Test Error:', error);
            throw error;
        }
    }

    // Helper method to run a quick test with sample data
    static async quickTest() {
        const testUtils = new BQETestUtils();
        
        // Sample test data
        const sampleData = {
            _rawInvoice: {
                invoiceNumber: 'TEST-001',
                date: moment().format('YYYY-MM-DD'),
                currency: 'MYR',
                serviceAmount: 1000,
                serviceTaxAmount: 60,
                invoiceAmount: 1060
            },
            supplier: {
                name: 'Test Supplier',
                tin: 'SUP123',
                registrationNumber: 'BRN123',
                sstId: 'SST123',
                address: {
                    line1: '123 Test Street',
                    city: 'Test City',
                    state: '14',
                    postcode: '12345',
                    country: 'MYS'
                }
            },
            buyer: {
                name: 'Test Buyer',
                tin: 'BUY123',
                registrationNumber: 'BRN456',
                sstId: 'SST456',
                address: {
                    line1: '456 Test Avenue',
                    city: 'Test City',
                    state: '14',
                    postcode: '54321',
                    country: 'MYS'
                }
            },
            _projectDetailsArray: [{
                details: {
                    customFields: [
                        {
                            label: 'TAX TYPE (CODE)',
                            value: '02',
                            description: 'Service Tax'
                        }
                    ]
                }
            }]
        };

        try {
            const results = await testUtils.testMapping(sampleData);
            return results;
        } catch (error) {
            console.error('Quick test failed:', error);
            throw error;
        }
    }
}

module.exports = BQETestUtils; 