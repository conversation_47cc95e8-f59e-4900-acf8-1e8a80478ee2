{% extends '../layout.html' %}

{% block head %}
<!-- Template Main CSS Files -->

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<link href="/assets/css/components/table.css" rel="stylesheet">
<link href="/assets/css/components/buttons.css" rel="stylesheet">
<link href="/assets/css/components/tooltip.css" rel="stylesheet">
<link href="/assets/css/components/session-details.css" rel="stylesheet">
<link href="/assets/css/components/filters.css" rel="stylesheet">
<link href="/assets/css/components/outbound-loading-modal.css" rel="stylesheet">
<link href="/assets/css/components/outbound-invoice-modal.css" rel="stylesheet">

<link href="/assets/css/pages/oubound/card.css" rel="stylesheet">
<link href="/assets/css/pages/oubound/dialog.css" rel="stylesheet">
<link href="/assets/css/pages/profile.css" rel="stylesheet">
<link href="/assets/css/pages/oubound/badges.css" rel="stylesheet">


<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>


<script src="/assets/js/shared/utils.js"></script>
<script src="/assets/js/bqe-auth.js"></script>
<script src="/assets/js/modules/bqe/outbound.js"></script>
<script src="/assets/js/modules/bqe/invoice-helper.js"></script>


{% endblock %}

{% block content %}
<div class="profile-container">
<!-- Welcome Card - Full width -->
<div class="profile-welcome-section">
  <div class="profile-welcome-card">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <div class="welcome-icon">
          <i class="bi bi-box-arrow-right"></i>
        </div>
        <div class="welcome-content">
          <h3 class="mb-1">Outbound</h3>
                    <p class="mb-0 cursor-pointer" data-bs-toggle="tooltip" data-bs-html="true" 
                      title="<div class='tooltip-content'>
                                <div class='tooltip-row'>
                                  <i class='bi bi-info-circle'></i>
                                  <span><b>BQE Authorization Guide:</b></span>
                                </div>
                                <div class='tooltip-row'>
                                  <i class='bi bi-1-circle'></i>
                                  <span>Click the 'Authorize BQE' button</span>
                                </div>
                                <div class='tooltip-row'>
                                  <i class='bi bi-2-circle'></i>
                                  <span>Sign in to your BQE account</span>
                                </div>
                                <div class='tooltip-row'> 
                                  <i class='bi bi-3-circle'></i>
                                  <span>Grant necessary permissions</span>
                                </div>
                                <div class='tooltip-row'>
                                  <i class='bi bi-4-circle'></i>
                                  <span>Your invoices will sync automatically</span>
                                </div>
                              </div>">
            View and manage your outbound invoices. To fetch invoices from BQE, please authorize the connection.
          </p>
        </div>
      </div>
      <div class="d-flex align-items-start">
        <!-- Add BQE Auth Button -->
                <div class="me-4 mt-4 mb-2 position-relative">
                    <button id="bqeAuthBtn" class="btn auth-btn" type="button" data-bs-toggle="tooltip">
            <i class="bi bi-shield-lock me-2"></i>
            <span id="bqeAuthBtnText">Authorize BQE</span>
          </button>
        </div>
        <!-- Existing datetime section -->
        <div class="welcome-datetime text-end">
          <div class="current-time">
              <i class="bi bi-clock"></i>
              <span id="currentTime">00:00:00 AM</span>
          </div>
          <div class="current-date">
              <i class="bi bi-calendar3"></i>
              <span id="currentDate">Loading...</span>
        </div>
      </div>
    </div>
  </div>
  </div>
  
</div>

    <!-- Content Section - With margins -->
    <div class="content-section">
      <!-- Cards Section -->
      <div class="cards-container">
          <!-- Invoices Card -->
          <div class="info-card invoices-card">
              <div class="card-info">
                  <div class="card-icon">
                      <i class="bi bi-file-earmark-text"></i>
                  </div>
                  <div class="count-info">
                      <h6>0</h6>
                      <span class="text-muted">Total</span>
                  </div>
              </div>
              <span class="card-title-new">INVOICES</span>
          </div>

          <!-- Submitted Card -->
          <div class="info-card submitted-card">
              <div class="card-info">
                  <div class="card-icon">
                      <i class="bi bi-check2-circle"></i>
                  </div>
                  <div class="count-info">
                      <h6>0</h6>
                      <span class="text-muted">Total</span>
                  </div>
              </div>
              <span class="card-title-new">SUBMITTED</span>
          </div>

          <!-- Pending Card -->
          <div class="info-card pending-card">
              <div class="card-info">
                  <div class="card-icon">
                      <i class="bi bi-hourglass-split"></i>
                  </div>
                  <div class="count-info">
                      <h6>0</h6>
                      <span class="text-muted">Total</span>
                  </div>
              </div>
              <span class="card-title-new">PENDING</span>
          </div>

          <!-- Cancelled Card -->
          <div class="info-card cancelled-card">
              <div class="card-info">
                  <div class="card-icon">
                      <i class="bi bi-x-circle"></i>
                  </div>
                  <div class="count-info">
                      <h6>0</h6>
                      <span class="text-muted">Total</span>
                  </div>
              </div>
              <span class="card-title-new">CANCELLED</span>
          </div>
      </div>

      <!-- Filters Section -->
      <div class="filters-section d-none">
        <div class="filters-header">
            <div class="filter-title">
                <i class="bi bi-funnel me-2"></i>
                Filters
                <span class="api-limit-badge ms-2" data-bs-toggle="tooltip" 
                      title="BQE API has a limitation of 25 pages per request. Each page contains up to 100 records.">
                    <i class="bi bi-info-circle"></i>
                    Max 25 pages
                </span>
            </div>
        </div>

        <div class="filters-body">
            <!-- Main Filters -->
            <div class="main-filters">
                <div class="filter-group">
                    <label>
                        Period 
                        <i class="bi bi-question-circle" data-bs-toggle="tooltip" 
                           title="Select a predefined time period or choose Custom for manual date range"></i>
                    </label>
                    <select class="form-select">
                      <optgroup label="Custom">
                        <option value="custom">Custom Range</option>
                    </optgroup>
                        <optgroup label="Daily">
                            <option value="today">Today</option>
                            <option value="yesterday">Yesterday</option>
                        </optgroup>
                        <optgroup label="Weekly">
                            <option value="this week">This Week</option>
                            <option value="last week">Last Week</option>
                        </optgroup>
                        <optgroup label="Monthly">
                            <option value="this month">This Month</option>
                            <option value="last month">Last Month</option>
                        </optgroup>
                        <optgroup label="Yearly">
                            <option value="this year">This Year</option>
                            <option value="last year">Last Year</option>
                        </optgroup>
                    </select>
                </div>

                <div class="date-range">
                    <div class="filter-group">
                        <label>
                            From 
                            <i class="bi bi-question-circle" data-bs-toggle="tooltip" 
                               title="Select start date for custom date range"></i>
                        </label>
                        <input type="date" class="form-control" id="fromDate">
                    </div>
                    <div class="filter-group">
                        <label>
                            To 
                            <i class="bi bi-question-circle" data-bs-toggle="tooltip" 
                               title="Select end date for custom date range"></i>
                        </label>
                        <input type="date" class="form-control" id="toDate">
                    </div>
                </div>
            </div>

            <!-- Additional Filters -->
            <div class="additional-filters">
     
            </div>
        </div>

        <div class="filters-footer">
            <div class="records-info">
                <span class="records-count me-2">
                    Total Invoices Fetched: 0 
                    <i class="bi bi-question-circle ms-1" data-bs-toggle="tooltip" 
                       title="Total number of invoices found for selected date range"></i>
                </span>
                <span class="api-notice">
                    <i class="bi bi-exclamation-triangle"></i>
                    Limited to 2,500 records (25 pages × 100 records)
                </span>
            </div>
            <button class="btn btn-primary" type="button" id="searchBqeBtn">
                <i class="bi bi-search me-1"></i>
                Search BQE Invoice
            </button>
        </div>
    </div>

         <!-- Table Section -->
         <div class="table-card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table id="reportsTable" class="table">
              <thead>
                <tr>
                                <th class="checkbox-column">
                    <div class="form-check">
                      <input type="checkbox" class="form-check-input" id="selectAll">
                    </div>
                  </th>
                                <th class="invoice-number">Invoice Number</th>
                                <th class="type-column">Type</th>
                                <th class="customer-name">Customer Name</th>
                                <th class="date-column">BQE Date</th>
                                <th class="date-column">Date Sync</th>
                                <th class="date-column">Date Submitted</th>
                                <th class="date-column">Cancellation Time Left</th>
                                <th class="status-column">Status</th>
                                <th class="action-column">Action</th>
                                

                </tr>
              </thead>
            </table>
              
         
          </div>
      </div>
    </div>
</div>


<div class="modal fade outbound-loading-modal" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <div class="loading-content">
                    <div class="loading-animation">
                        <div class="loading-spinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div class="loading-dots">
                            <span class="dot"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                    </div>
                    <div class="loading-message"></div>
                    <div class="loading-time-left"></div>
                    <div class="progress mb-4">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="steps-container">
                        <div class="step" data-step="checking_staging" data-status="waiting">
                            <div class="step-icon">
                                <i class="bi bi-circle-fill"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-label">Checking Database</div>
                                <div class="step-status">Waiting...</div>
                            </div>
                        </div>
                        <div class="step" data-step="retrieving" data-status="waiting">
                            <div class="step-icon">
                                <i class="bi bi-circle-fill"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-label">Retrieving Data</div>
                                <div class="step-status">Waiting...</div>
                            </div>
                        </div>
                        
                        <div class="step" data-step="saving" data-status="waiting">
                            <div class="step-icon">
                                <i class="bi bi-circle-fill"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-label">Saving</div>
                                <div class="step-status">Waiting...</div>
                            </div>
                        </div>
                        <div class="step" data-step="completed" data-status="completed">
                            <div class="step-icon">
                                <i class="bi bi-circle-fill"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-label">Completed</div>
                                <div class="step-status">Completed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- View Details Modal -->
<div class="modal fade outbound-invoice-modal" id="viewDetailsModal" tabindex="-1" role="dialog" aria-modal="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <!-- Loading Overlay -->
            <div id="modalLoadingOverlay" class="modal-loading-overlay d-none">
                <div class="loading-spinner"></div>
            </div>

            <!-- Header -->
            <div class="modal-header">
                <div class="d-flex align-items-center">
                    <h5 class="modal-title">Document Details</h5>
                    <div class="document-id ms-2" id="invoice-number"></div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Content -->
            <div class="modal-body p-0">
                <div class="d-flex">
                    <!-- Left Panel -->
                    <div class="left-panel">
                        <!-- Invoice Information -->
                        <div class="info-section">
                            <div class="section-header">
                                <i class="bi bi-file-text"></i>
                                Invoice Information
                            </div>
                            <div class="info-content">
                                <div class="info-row">
                                    <div class="info-label">INVOICE NUMBER</div>
                                    <div class="info-value" id="modalInvoiceNumber"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">INVOICE STATUS</div>
                                    <div class="info-value" id="modalInvoiceStatus"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">INVOICE DATE</div>
                                    <div class="info-value" id="modalInvoiceDate"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">INVOICE VERSION</div>
                                    <div class="info-value" id="modalInvoiceVersion"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">INVOICE TYPE</div>
                                    <div class="info-value" id="modalInvoiceType"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Supplier Information -->
                        <div class="info-section">
                            <div class="section-header">
                                <i class="bi bi-building"></i>
                                Supplier Information
                            </div>
                            <div class="info-content">
                                <div class="info-row">
                                    <div class="info-label">NAME</div>
                                    <div class="info-value" id="modalSupplierName"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">TIN</div>
                                    <div class="info-value" id="modalSupplierTin"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">BRN</div>
                                    <div class="info-value" id="modalSupplierBrn"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">SST ID</div>
                                    <div class="info-value" id="modalSupplierSst"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Buyer Information -->
                        <div class="info-section">
                            <div class="section-header">
                                <i class="bi bi-person"></i>
                                Buyer Information
                            </div>
                            <div class="info-content">
                                <div class="info-row">
                                    <div class="info-label">NAME</div>
                                    <div class="info-value" id="modalBuyerName"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">TIN</div>
                                    <div class="info-value" id="modalBuyerTin"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">BRN</div>
                                    <div class="info-value" id="modalBuyerBrn"></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">ADDRESS</div>
                                    <div class="info-value" id="modalBuyerAddress"></div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Right Panel -->
                    <div class="right-panel">
                        <!-- Line Items -->
                        <div class="info-section">
                            <div class="section-header">
                                <i class="bi bi-list-check"></i>
                                Line Items
                            </div>
                            <div class="info-content">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead>
                                            <tr>
                                                <th>Classification</th>
                                                <th>Description</th>
                                                <th>Quantity</th>
                                                <th>Unit Price</th>
                                                <th>Amount</th>
                                                <th>Tax Rate</th>
                                                <th>Tax Amount</th>
                                                <th>Total</th>
                                            </tr>
                                        </thead>
                                        <tbody id="lineItemsBody">
                                            <!-- Line items will be inserted here -->
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-lhdn" id="submitToLhdnBtn">
                    <i class="bi bi-send"></i>
                    Submit to LHDN
                </button>
                <button type="button" class="btn btn-danger btn-lhdn btn-cancel-invoice d-none">
                    <i class="bi bi-x-circle"></i>
                    Cancel Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<div class="mt-2">
<div id="bqeSessionInfo" class="d-none">
    <div class="alert alert-info border-info shadow-sm p-2" style="font-size: 0.85rem;">
        <div class="d-flex align-items-center">
            <i class="bi bi-info-circle me-2"></i>
            <div>
                <div class="fw-semibold mb-1">BQE Session Info</div>
                <div class="small">
                    <div class="d-flex align-items-center mb-1">
                        <i class="bi bi-clock-history me-1"></i>
                        Expires in: <span id="sessionExpiryTime" class="ms-1 fw-semibold"></span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-shield-check me-1"></i>
                        Status: <span id="sessionStatus" class="ms-1 fw-semibold text-success">Active</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>



{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', async function() {
        try {
            console.log('DOM loaded, initializing managers...');
            // Initialize managers
            window.invoiceTable = new InvoiceTableManager();
           
            
            // Remove or comment out these lines
            // const filtersSection = document.querySelector('.filters-section');
            // if (filtersSection) {
            //     filtersSection.classList.remove('d-none');
            // }
        } catch (error) {
            console.error('Error initializing managers:', error);
            Swal.fire({
                icon: 'error',
                title: 'Initialization Error',
                text: 'Failed to initialize the application. Please refresh the page.',
                confirmButtonText: 'Refresh',
                showCancelButton: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.reload();
                }
            });
        }
    });
  
  // Cleanup on page unload
  window.addEventListener('unload', () => {
      if (window.invoiceTable) {
          window.invoiceTable.cleanup();
      }
  });

function updateDateTime() {
    const timeElement = document.getElementById('currentTime');
    const dateElement = document.getElementById('currentDate');
    
    function update() {
        const now = new Date();
        
        // Update time
        timeElement.textContent = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });
        
        // Update date
        dateElement.textContent = now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    
    // Update immediately and then every second
    update();
    setInterval(update, 1000);
}

// Call when the document is ready
document.addEventListener('DOMContentLoaded', updateDateTime);
</script>
{% endblock %} 