const moment = require('moment');
const BQELogger = require('./bqeLogger');

class BQEDataProcessor {
  constructor() {
    this.logger = new BQELogger();
    this.processLogs = {
      steps: [],
      documents: []
    };
  }

  getPaymentInfo(customFields) {
    if (!customFields || !Array.isArray(customFields)) {
      return null;
    }

    // Look for payment information in custom fields
    const remitPaymentField = customFields.find(f => f.label === 'Remit Payment To');
    return remitPaymentField?.value || null;
  }

  extractSupplierInfo(companyData) {
    if (!companyData) {
      return {
        name: 'NA',
        tin: 'NA',
        registrationNumber: 'NA',
        sstId: 'NA',
        ttxId: 'NA',
        msicCode: 'NA',
        businessActivity: 'NA',
        countryCode: 'MYS',
        stateCode: '14',
        phone: 'NA',
        email: 'NA',
        certExId: 'NA',
        address: {
          line1: 'NA',
          line2: '',
          city: 'NA',
          state: 'NA',
          postcode: 'NA',
          country: 'MYS',
          formattedAddress: 'NA'
        }
      };
    }

    const customFields = companyData.customFields || [];
    const phone = companyData.phone?.replace(/\s+/g, '') || 'NA';
    const email = companyData.email || 'NA';

    // Format address components
    const addressLine1 = companyData.addressLine1 || '';
    const addressLine2 = companyData.addressLine2 || '';
    const city = companyData.city?.trim() || '';
    //const state = companyData.stateCode || '';
    const state = '14';
    // Clean postcode: remove dashes, spaces, and limit to 5 characters
    const postcode = this.cleanPostcode(companyData.zip || '');
    const country = companyData.country || 'MYS';

    // Create formatted address - preserve the full address structure
    // Don't truncate or modify the original address components
    const formattedAddress = [
      addressLine1,
      addressLine2,
      city,
      state,
      postcode,
      country
    ].filter(part => part).join(', ');

    return {
      name: companyData.name || 'NA',
      tin: customFields.find(f => f.label === "Supplier's TIN")?.value || 'NA',
      registrationNumber: customFields.find(f => f.label === "Supplier's Registration No")?.value || 'NA',
      sstId: customFields.find(f => f.label === "Supplier's SST No")?.value || 'NA',
      ttxId: customFields.find(f => f.label === "Supplier's Tourism Tax No")?.value || 'NA',
      msicCode: customFields.find(f => f.label === "Supplier's MSIC Code")?.value || 'NA',
      businessActivity: customFields.find(f => f.label === "Supplier's Business Activity")?.value || 'NA',
      countryCode: customFields.find(f => f.label === "COUNTRY CODE")?.description || 'MYS',
      stateCode: customFields.find(f => f.label === "State Code")?.description || '17',
      phone: phone,
      email: email,
      certExId: customFields.find(f => f.label === "Company Other 1")?.value || 'NA',
      address: {
        line1: addressLine1,
        line2: addressLine2,
        city: city,
        state: state,
        postcode: postcode,
        country: country,
        formattedAddress: formattedAddress
      }
    };
  }

  extractBuyerInfo(clientData) {
    if (!clientData) {
      return {
        name: 'NA',
        tin: 'NA',
        registrationNumber: 'NA',
        sstId: 'NA',
        ttxId: 'NA',
        msicCode: 'NA',
        businessActivity: 'NA',
        countryCode: 'MYS',
        stateCode: '14',
        phone: 'NA',
        email: 'NA',
        certExId: 'NA',
        address: {
          line1: 'NA',
          line2: '',
          city: 'NA',
          state: '14',
          postcode: 'NA',
          country: 'MYS',
          formattedAddress: 'NA'
        }
      };
    }

    const communications = clientData.communications || [];
    const phone = communications.find(c => c.typeName === 'Phone')?.value?.replace(/\s+/g, '') || 'NA';
    const email = communications.find(c => c.typeName === 'Email')?.value || 'NA';
    const customFields = clientData.customFields || [];

    // Format address components
    const addressLine1 = clientData.address?.street1 || '';
    const addressLine2 = clientData.address?.street2 || '';
    const city = clientData.address?.city || '';
    //const state = clientData.address?.state || '';
    const state = '14';
    // Clean postcode: remove dashes, spaces, and limit to 5 characters
    const postcode = this.cleanPostcode(clientData.address?.zip || '');
    const country = clientData.address?.country || 'MYS';

    // Create formatted address - preserve the full address structure
    // Don't truncate or modify the original address components
    const formattedAddress = [
      addressLine1,
      addressLine2,
      city,
      state,
      postcode,
      country
    ].filter(part => part).join(', ');

    return {
      name: clientData.company || 'NA',
      tin: clientData.taxId || 'NA',
      sstId: customFields.find(f => f.label === "Buyer's SST No")?.value || 'NA',
      ttxId: customFields.find(f => f.label === "Buyer's Tourism Tax No")?.value || 'NA',
      registrationNumber: customFields.find(f => f.label === "Buyer's Registration No")?.value || clientData.registrationNumber || 'NA',
      msicCode: customFields.find(f => f.label === "Buyer's MSIC Code")?.value || clientData.msicCode || 'NA',
      businessActivity: customFields.find(f => f.label === "Buyer's Business Activity")?.value || 'NA',
      countryCode: customFields.find(f => f.label === "BUYER'S COUNTRY CODE")?.description || 'MYS',
      stateCode: customFields.find(f => f.label === "BUYER'S ADDRESS STATE CODE")?.description || '14',
      phone: phone,
      email: email,
      certExId: customFields.find(f => f.label === "Client Other 1")?.value || 'NA',
      address: {
        line1: addressLine1,
        line2: addressLine2,
        city: city,
        state: state,
        postcode: postcode,
        country: country,
        formattedAddress: formattedAddress
      }
    };
  }

 getTaxRate(invoice, projectDetails, projectCustomFields) {
   try {
     // Check if tax type is '06' (Not Applicable) or 'E' (Exempt) - if so, return 0
     const taxTypeField = projectCustomFields?.find(f => f.label === 'TAX TYPE (CODE)');
     const taxType = taxTypeField?.description?.split('|')?.[0]?.trim();
     if (taxType === '06' || taxType === 'NA' || taxType === 'E') {
       console.log('DataProcessor - Tax type is exempt or not applicable, returning 0');
       return 0;
     }

     // PRIORITY 1: Check project level tax rate first
     if (typeof projectDetails?.mainServiceTax === 'number' &&
         !isNaN(projectDetails.mainServiceTax) &&
         projectDetails.mainServiceTax !== 0) {
       return projectDetails.mainServiceTax;
     }

     // 1.2: Check project custom fields
     const projectTaxRateField = projectCustomFields?.find(f =>
       f.label === 'Tax Rate' ||
       f.label === 'SERVICE TAX RATE'
     );
     if (projectTaxRateField?.value && parseFloat(projectTaxRateField.value) !== 0) {
       return parseFloat(projectTaxRateField.value);
     }

     // PRIORITY 2: Check invoice custom fields
     const invoiceCustomFields = invoice?.customFields || [];
     const taxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');
     if (taxRateField?.value) {
       return parseFloat(taxRateField.value);
     }

     // PRIORITY 3: Check raw invoice tax rate
     if (typeof invoice?.mainServiceTax === 'number' &&
         !isNaN(invoice.mainServiceTax)) {
       return invoice.mainServiceTax;
     }

     // Default to 0 if no tax rate is found
     console.log('DataProcessor - No tax rate found, defaulting to 0');
     return 0;
   } catch (error) {
     console.error('DataProcessor - Error getting tax rate:', error);
     return 0; // Default to 0 on error
   }
 }

  extractTaxInfo(rawInvoice, projectCustomFields, projectDetails, currency = 'MYR', exchangeRate = 1.0) {
    try {
        // Get tax type and exemption information
        const taxTypeField = projectCustomFields?.find(f => f.label === 'TAX TYPE (CODE)');
        const taxExemptionField = projectCustomFields?.find(f => f.label === 'Details of Tax Exemption');

        // Extract and normalize tax type code
        let taxType = taxTypeField?.description?.split('|')?.[0]?.trim() || '02';

        // Check if the tax type description contains "Tax Exempt" or similar phrases
        const isExemptDescription = taxTypeField?.description?.toLowerCase().includes('tax exempt') ||
                                   taxTypeField?.description?.toLowerCase().includes('exemption');

        // Normalize tax type codes
        if (taxType === '02') taxType = '02'; // Service Tax
        else if (taxType === '01') taxType = '01'; // Sales Tax
        else if (taxType === '03') taxType = '03'; // Tourism Tax
        else if (taxType === '04') taxType = '04'; // High-Value Goods Tax
        else if (taxType === '05') taxType = '05'; // Low Value Goods Tax
        else if (taxType === '06' || taxType === 'NA') taxType = '06'; // Not Applicable
        else if (taxType === 'E' || isExemptDescription) taxType = 'E'; // Map E to Tax Exemption

        // Get tax type description based on code
        const taxTypeDescriptions = {
          '01': 'Sales Tax',
          '02': 'Service Tax',
          '03': 'Tourism Tax',
          '04': 'High-Value Goods Tax',
          '05': 'Sales Tax on Low Value Goods',
          '06': 'Not Applicable',
          'E': 'Tax Exemption'
        };

        // Check for tax exemption status
        const isExempted = taxType === 'E';

        // Get tax rate - use getTaxRate method for consistency
        let taxRate = this.getTaxRate(rawInvoice, projectDetails, projectCustomFields);

        // Initialize variables for tax calculation
        // For foreign currency, BQE amounts are already in foreign currency (USD)
        const originalTotalAmount = parseFloat(rawInvoice?.invoiceAmount || 0); // Original total (already in foreign currency if applicable)
        const originalTaxableAmount = parseFloat(rawInvoice?.serviceAmount || 0); // Original taxable (already in foreign currency if applicable)

        // Check if this is a foreign currency invoice - use consistent logic
        const isForeignCurrency = currency !== 'MYR';

        // Calculate amounts based on currency type
        let taxableAmount, taxAmount, totalAmount;
        let taxableAmountMYR, taxAmountMYR;

        if (isForeignCurrency) {
            // For foreign currency: BQE amounts are in MYR, need to convert to foreign currency (USD)
            // Convert MYR amounts to foreign currency by multiplying by exchange rate
            taxableAmount = Math.round(originalTaxableAmount * exchangeRate * 100) / 100;
            totalAmount = Math.round(originalTotalAmount * exchangeRate * 100) / 100;

            // MYR amounts are the original amounts from BQE
            taxableAmountMYR = originalTaxableAmount;

            // Handle tax based on tax type
            if (taxType === '06') {
                // Tax Type '06' (Not Applicable): No tax amount
                taxAmount = 0; // No tax in foreign currency
                taxAmountMYR = 0; // No tax in MYR either
            } else if (taxType === 'E') {
                // Tax Type 'E' (Tax Exempted): Include tax amount even though exempted
                taxAmount = 0; // Tax is always in MYR for foreign currency invoices
                taxAmountMYR = Math.round(taxableAmountMYR * taxRate / 100 * 100) / 100;
            } else {
                // Other tax types: Normal tax calculation
                taxAmount = 0; // Tax is always in MYR for foreign currency invoices
                taxAmountMYR = Math.round(taxableAmountMYR * taxRate / 100 * 100) / 100;
            }
        } else {
            // For local currency: use original amounts as-is
            if (taxType === '06') {
                // Tax Type '06' (Not Applicable): No tax amount
                taxableAmount = originalTotalAmount;
                taxAmount = 0;
            } else if (taxType === 'E') {
                // Tax Type 'E' (Tax Exempted): Include tax amount even though exempted
                if (originalTaxableAmount > 0) {
                    taxableAmount = originalTaxableAmount;
                    taxAmount = Math.round((originalTotalAmount - taxableAmount) * 100) / 100;
                } else {
                    // Calculate tax amount based on rate
                    taxableAmount = Math.round((originalTotalAmount * 100) / (1 + (taxRate/100))) / 100;
                    taxAmount = Math.floor((originalTotalAmount - taxableAmount) * 100) / 100;
                }
            } else {
                // Other tax types: Normal tax calculation
                if (originalTaxableAmount > 0) {
                    taxableAmount = originalTaxableAmount;
                    taxAmount = Math.round((originalTotalAmount - taxableAmount) * 100) / 100;
                } else {
                    // Fallback calculation
                    taxableAmount = Math.round((originalTotalAmount * 100) / (1 + (taxRate/100))) / 100;
                    taxAmount = Math.floor((originalTotalAmount - taxableAmount) * 100) / 100;
                }
            }

            totalAmount = originalTotalAmount;

            // MYR amounts are the same as the amounts
            taxableAmountMYR = taxableAmount;
            taxAmountMYR = taxAmount;
        }

        // For debugging
        console.log('Tax Info Extracted:', {
            taxType,
            isExempted,
            taxRate,
            taxableAmount,
            totalAmount,
            taxAmount,
            currency,
            isForeignCurrency,
            exchangeRate,
            taxableAmountMYR,
            taxAmountMYR,
            totalAmountMYR: isForeignCurrency ? (taxableAmountMYR + taxAmountMYR) : totalAmount,
            taxTypeDescription: taxTypeDescriptions[taxType] || 'NA',
            taxExemption: isExempted ?
                (taxExemptionField?.description || 'Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018') :
                undefined
        });

        // Get the original tax rate for reference (before applying exemptions)
        let originalTaxRate = taxRate;

        // For tax type '06', we need to get the original rate from other sources for reference
        if (taxType === '06') {
            // Try to get the original rate from project details or custom fields
            if (typeof projectDetails?.mainServiceTax === 'number' &&
                !isNaN(projectDetails.mainServiceTax) &&
                projectDetails.mainServiceTax !== 0) {
                originalTaxRate = projectDetails.mainServiceTax;
            } else {
                // Check project custom fields for original rate
                const projectTaxRateField = projectCustomFields?.find(f =>
                    f.label === 'Tax Rate' ||
                    f.label === 'SERVICE TAX RATE'
                );
                if (projectTaxRateField?.value && parseFloat(projectTaxRateField.value) !== 0) {
                    originalTaxRate = parseFloat(projectTaxRateField.value);
                } else {
                    // Check invoice custom fields for original rate
                    const invoiceCustomFields = rawInvoice?.customFields || [];
                    const taxRateField = invoiceCustomFields.find(f => f.label === 'Tax rate');
                    if (taxRateField?.value) {
                        originalTaxRate = parseFloat(taxRateField.value);
                    }
                }
            }
        }

        // Prepare the tax info object - use the actual tax type from the data
        const taxInfo = {
            taxRate: taxRate,
            originalTaxRate: originalTaxRate,
            taxType: taxType, // Use the actual tax type from the data, don't override
            taxTypeDescription: taxTypeDescriptions[taxType] || 'NA',
            taxExemption: (taxType === 'E' || isExempted) ?
                (taxExemptionField?.description || 'Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018') :
                undefined,
            isExempted: isExempted,
            taxableAmount: taxableAmount,  // Amount excluding tax
            taxInclusiveAmount: totalAmount, // Total amount (tax inclusive)
            taxAmount: taxAmount            // Tax amount
        };

        // Add foreign currency information if applicable
        if (isForeignCurrency) {
            // Add basic currency information
            Object.assign(taxInfo, {
                currency: currency,
                exchangeRate: exchangeRate,
                taxCurrency: 'MYR',
                // Add MYR conversion amounts - ensure they're rounded to 2 decimal places
                taxableAmountMYR: Math.round(taxableAmountMYR * 100) / 100,
                taxAmountMYR: Math.round(taxAmountMYR * 100) / 100,
                taxInclusiveAmountMYR: Math.round((taxableAmountMYR + taxAmountMYR) * 100) / 100
            });

            // Log foreign currency tax info for debugging
            console.log('Foreign Currency Tax Info:', {
                currency,
                exchangeRate,
                taxableAmount,
                taxAmount,
                taxableAmountMYR: Math.round(taxableAmountMYR * 100) / 100,
                taxAmountMYR: Math.round(taxAmountMYR * 100) / 100,
                taxInclusiveAmountMYR: Math.round((taxableAmountMYR + taxAmountMYR) * 100) / 100
            });
        }

        return taxInfo;
    } catch (error) {
        console.error('Error extracting tax info:', error);
        return {
            taxRate: 0,
            originalTaxRate: 0,
            taxType: '06',
            taxTypeDescription: 'Not Applicable',
            taxExemption: 'Not Applicable',
            isExempted: true,
            taxableAmount: 0,
            taxInclusiveAmount: 0,
            taxAmount: 0
        };
    }
  }

  processLineItems(invoiceDetails, projectDetailsArray, currency = 'MYR', exchangeRate = 1.0, currencyDetails = null) {
    // Check if this is a foreign currency invoice using the same logic as in processRawBQEData
    let isForeignCurrency = false;

    // First check if currencyDetails is provided and valid
    if (currencyDetails?.data) {
      const cd = currencyDetails.data;
      // Only consider it a foreign currency if it has a currencyCode that's not MYR and has a valid multiplier
      isForeignCurrency = cd.currencyCode && cd.currencyCode !== 'MYR' && cd.multiplier;

      if (isForeignCurrency) {
        // Use the multiplier from currency_details as the exchange rate
        exchangeRate = parseFloat(cd.multiplier) || exchangeRate;
        currency = cd.currencyCode;
        console.log(`Line Items - Using currency_details for foreign currency: ${currency}, Exchange Rate: ${exchangeRate}`);
      }
    } else if (currency !== 'MYR') {
      // Fallback to simple currency check
      isForeignCurrency = true;

      // Don't use hardcoded exchange rates - use a default of 1.0 and log a warning
      if (exchangeRate === 1.0) {
        console.warn(`WARNING: No exchange rate found for ${currency} in line items. Using default rate of 1.0. This may cause incorrect calculations.`);
        // Keep the default exchange rate of 1.0
      }
    }

    if (!invoiceDetails || !Array.isArray(invoiceDetails) || invoiceDetails.length === 0) {
        const defaultItem = {
            id: 'default',
            amount: 0,
            quantity: 1,
            unitCode: "EA",
            description: 'No line items available',
            project: 'NA',
            projectTitle: 'NA',
            mainServiceTax: 0,
            classifications: {
                invoice: "022",
                department: "01-HIGHWAY",
                lead: "NA"
            },
            tax: {
                types: [{
                    type: '02',
                    rate: 0,
                    originalRate: 0,
                    amount: 0,
                    potentialAmount: 0
                }],
                exemption: 'Not Applicable',
                isExempted: false,
                potentialTaxAmount: 0
            },
            accountInfo: {
                income: 'NA',
                code: 'NA'
            }
        };

        // Add foreign currency information if applicable
        if (isForeignCurrency) {
            // Add basic currency information
            defaultItem.currency = currency;
            defaultItem.exchangeRate = exchangeRate;

            // Add MYR conversion properties - using Object.assign to avoid TypeScript errors
            Object.assign(defaultItem, {
                amountMYR: 0,
                totalAmountMYR: 0
            });

            // Add tax amount in MYR
            if (defaultItem.tax) {
                Object.assign(defaultItem.tax, {
                    amountMYR: 0
                });
            }
        }

        return [defaultItem];
    }

    return invoiceDetails.map((detail, index) => {
        // Find matching project details
        const project = projectDetailsArray?.find(p => p.projectId === detail.projectId)?.details || {};
        const customFields = project?.customFields || [];

        // Get tax information from custom fields
        const taxTypeField = customFields.find(f => f.label === "TAX TYPE (CODE)");
        const customProjTitle = customFields.find(f => f.label === "Project Title");
        const taxExemptionField = customFields.find(f => f.label === "Details of Tax Exemption");

        // Normalize tax type code
        let taxType = taxTypeField?.description?.split('|')?.[0]?.trim() || '02';

        // Check if the tax type description contains "Tax Exempt" or similar phrases
        const isExemptDescription = taxTypeField?.description?.toLowerCase().includes('tax exempt') ||
                                   taxTypeField?.description?.toLowerCase().includes('exemption');

        // Normalize tax type codes
        if (taxType === '02') taxType = '02'; // Service Tax
        else if (taxType === '01') taxType = '01'; // Sales Tax
        else if (taxType === '03') taxType = '03'; // Tourism Tax
        else if (taxType === '04') taxType = '04'; // High-Value Goods Tax
        else if (taxType === '05') taxType = '05'; // Low Value Goods Tax
        else if (taxType === '06' || taxType === 'NA') taxType = '06'; // Not Applicable
        else if (taxType === 'E' || isExemptDescription) taxType = 'E'; // Map E to Tax Exemption

        // Check for tax exemption status
        const isExempted = taxType === 'E';

        // Get tax rate using centralized method
        const originalTaxRate = this.getTaxRate({customFields: detail.customFields}, project, customFields);

        // Tax rate is 0 only if Not Applicable, otherwise use original rate
        const taxRate = taxType === '06' ? 0 : originalTaxRate;

        // Clean description
        const description = this.cleanHtmlContent(project?.memo);

        // Get project title
        const projectTitle = this.cleanHtmlContent(customProjTitle?.value) || 'NA';

        // Get classifications
        const invoiceClassification = customFields.find(f => f.label === "Invoice Classification")?.description || "022";
        const projectDepartment = customFields.find(f => f.label === "Project Department")?.description || "01-HIGHWAY";
        const projectLead = customFields.find(f => f.label === "Project LEAD DEPARTMENT")?.description || "NA";

        // IMPORTANT: Use the original values from the invoice details
        // For foreign currency, these are in MYR and need to be converted to foreign currency (USD)
        const originalLineItemAmount = detail.serviceAmount || 0;        // Original service amount in MYR
        const originalTaxAmount = detail.mainServiceTax || 0;            // Original tax amount in MYR
        const originalTotalAmount = detail.amount || 0;                  // Original total amount in MYR

        // Calculate amounts based on currency type
        let lineItemAmount, taxAmount, totalAmount;
        let lineItemAmountMYR, taxAmountMYR, totalAmountMYR;

        if (isForeignCurrency) {
            // For foreign currency: BQE amounts are in MYR, convert to foreign currency (USD)
            // Convert MYR amounts to foreign currency by multiplying by exchange rate
            lineItemAmount = Math.round(originalLineItemAmount * exchangeRate * 100) / 100;

            // MYR amounts are the original amounts from BQE
            lineItemAmountMYR = originalLineItemAmount;

            // Handle tax based on tax type for line items
            if (taxType === '06') {
                // Tax Type '06' (Not Applicable): No tax amount
                taxAmountMYR = 0;
                taxAmount = 0; // Tax amount in foreign currency is also 0
            } else if (taxType === 'E') {
                // Tax Type 'E' (Tax Exempted): Include tax amount even though exempted
                taxAmountMYR = Math.round(lineItemAmountMYR * taxRate / 100 * 100) / 100;
                taxAmount = 0; // Tax is always in MYR for foreign currency invoices
            } else {
                // Other tax types: Normal tax calculation
                taxAmountMYR = Math.round(lineItemAmountMYR * taxRate / 100 * 100) / 100;
                taxAmount = 0; // Tax is always in MYR for foreign currency invoices
            }

            totalAmount = Math.round(originalTotalAmount * exchangeRate * 100) / 100; // Convert total to foreign currency
            totalAmountMYR = lineItemAmountMYR + taxAmountMYR;
        } else {
            // For local currency: use original amounts as-is
            lineItemAmount = originalLineItemAmount;

            // Handle tax based on tax type for line items
            if (taxType === '06') {
                // Tax Type '06' (Not Applicable): No tax amount
                taxAmount = 0;
            } else if (taxType === 'E') {
                // Tax Type 'E' (Tax Exempted): Include tax amount even though exempted
                taxAmount = originalTaxAmount;
            } else {
                // Other tax types: Normal tax calculation
                taxAmount = originalTaxAmount;
            }

            totalAmount = originalTotalAmount;

            // MYR amounts are the same as the amounts
            lineItemAmountMYR = lineItemAmount;
            taxAmountMYR = taxAmount;
            totalAmountMYR = totalAmount;
        }

        // Create the line item object
        const lineItem = {
            id: detail.projectId || `line-${index + 1}`,
            lineId: index + 1,
            amount: lineItemAmount,                            // Taxable amount (ex-tax)
            totalAmount: totalAmount,                          // Total amount (tax inclusive)
            quantity: 1,
            unitCode: "EA",
            description: detail.memo1 || description,
            project: project?.displayName || detail.project || 'NA',
            projectTitle: projectTitle,
            mainServiceTax: project?.mainServiceTax || 0,
            classifications: {
                invoice: invoiceClassification,
                department: projectDepartment,
                lead: projectLead
            },
            tax: {
                types: [{
                    type: taxType,
                    rate: taxRate,
                    originalRate: originalTaxRate,
                    amount: taxAmount,
                    potentialAmount: isExempted ? (lineItemAmount * originalTaxRate / 100) : taxAmount
                }],
                exemption: isExempted ?
                    (taxExemptionField?.description || 'Business To Business (B2B) Exemption under Group G of the Service Tax Act 2018') :
                    undefined,
                isExempted: isExempted,
                potentialTaxAmount: isExempted ? (lineItemAmount * originalTaxRate / 100) : taxAmount
            },
            accountInfo: {
                income: project?.incomeAccount || 'NA',
                code: project?.code || 'NA'
            }
        };

        // Add foreign currency information if applicable
        if (isForeignCurrency) {
            // Add basic currency information
            lineItem.currency = currency;
            lineItem.exchangeRate = exchangeRate;

            // Add MYR conversion properties - using Object.assign to avoid TypeScript errors
            Object.assign(lineItem, {
                amountMYR: lineItemAmountMYR,
                totalAmountMYR: totalAmountMYR
            });

            // Add tax amount in MYR - ensure proper rounding
            if (lineItem.tax) {
                Object.assign(lineItem.tax, {
                    amountMYR: Math.round(taxAmountMYR * 100) / 100
                });

                // Also update the tax types with MYR values
                if (lineItem.tax.types && lineItem.tax.types.length > 0) {
                    lineItem.tax.types = lineItem.tax.types.map(taxType => {
                        if (taxType.amount) {
                            return {
                                ...taxType,
                                amountMYR: Math.round(taxType.amount * exchangeRate * 100) / 100
                            };
                        }
                        return taxType;
                    });
                }
            }
        }

        return lineItem;
    });
}

  cleanHtmlContent(html) {
    if (!html) return '';
    return html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
  }

  // Utility function to clean postcode for LHDN compliance
  cleanPostcode(postcode) {
    if (!postcode) return '';

    const original = postcode;
    // Remove dashes, spaces, and any non-alphanumeric characters
    // Limit to 5 characters as per LHDN requirement
    const cleaned = postcode.replace(/[-\s\W]/g, '').substring(0, 5);

    // Log if postcode was modified for debugging
    if (original !== cleaned && original.length > 0) {
      console.log(`Postcode cleaned: "${original}" -> "${cleaned}"`);
    }

    return cleaned;
  }

  async processRawBQEData(data) {
    console.log('Processing BQE data:', JSON.stringify(data, null, 2));

    if (!data || !data._rawInvoice) {
      throw new Error('Invalid BQE data');
    }

    // Log the raw invoice data for debugging
    console.log('Raw invoice data:', JSON.stringify(data._rawInvoice, null, 2));

    try {
      // Create initial log entry
      const initialLog = {
        stage: 'initial',
        timestamp: moment().format(),
        rawData: data
      };

      const currentDate = new Date();
      const formattedDate = currentDate.toISOString().split('T')[0];
      const formattedTime = currentDate.toISOString().split('T')[1].split('.')[0] + 'Z';

      // Ensure we have the necessary arrays
      if (!data._projectDetailsArray) data._projectDetailsArray = [];
      if (!data._invoiceDetails) data._invoiceDetails = [];

      // Get the first project details if available
      const projectDetails = data._projectDetailsArray[0]?.details || {};
      const projectCustomFields = projectDetails?.customFields || [];

      // Get currency information from the raw invoice data
      const currency = data._rawInvoice?.currency || 'MYR';

      // Log the raw currency information
      console.log('DataProcessor - Raw currency information:', {
        currency,
        rawCurrency: data._rawInvoice?.currency,
        hasCurrencyDetails: !!data.currency_details,
        currencyDetailsData: data.currency_details?.data
      });

      // Check if this is a foreign currency invoice using the same logic as outbound.js
      let isForeignCurrency = false;
      let exchangeRate = 1.0;

      // Check if currency_details exists with valid data
      if (data.currency_details?.data) {
        const cd = data.currency_details.data;
        // Only consider it a foreign currency if it has a currencyCode that's not MYR and has a valid multiplier
        isForeignCurrency = cd.currencyCode && cd.currencyCode !== 'MYR' && cd.multiplier;

        if (isForeignCurrency) {
          // Use the multiplier from currency_details as the exchange rate
          exchangeRate = parseFloat(cd.multiplier) || 1.0;

          // Log detailed currency information
          console.log(`DataProcessor - Using currency_details for foreign currency: ${cd.currencyCode}, Exchange Rate: ${exchangeRate}`);
          console.log('DataProcessor - Currency details data:', {
            currencyCode: cd.currencyCode,
            multiplier: cd.multiplier,
            exchangeRate,
            fullData: cd
          });
        }
      } else if (currency !== 'MYR') {
        // Fallback to checking raw currency if currency_details is not available
        isForeignCurrency = true;

        // Try to get exchange rate from custom fields
        const exchangeRateField = data._rawInvoice?.customFields?.find(f =>
          f.label === 'Exchange Rate' ||
          f.label === 'Currency Exchange Rate');

        if (exchangeRateField?.value) {
          exchangeRate = parseFloat(exchangeRateField.value);
        } else {
          // Don't use hardcoded exchange rates - use a default of 1.0 and log a warning
          console.warn(`WARNING: No exchange rate found for ${currency} in raw invoice. Using default rate of 1.0. This may cause incorrect calculations.`);
          // Keep the default exchange rate of 1.0
        }

        console.log(`Using fallback for foreign currency: ${currency}, Exchange Rate: ${exchangeRate}`);
      }

      // Extract tax information
      const taxInfo = this.extractTaxInfo(
        data._rawInvoice,
        projectCustomFields,
        projectDetails,
        currency,
        exchangeRate
      );

      // Extract supplier and buyer information
      const supplier = this.extractSupplierInfo(data.company);
      const buyer = this.extractBuyerInfo(data._clientDetails);

      // Get project title
      const projTitle = this.cleanHtmlContent(
        projectCustomFields.find(f => f.label === "Project Title")?.value
      ) || 'NA';

      // Get payment information
      const paymentInfo = this.getPaymentInfo(data.company?.customFields);

      // Process line items - pass currency details to ensure consistent handling
      const lineItems = this.processLineItems(
        data._invoiceDetails,
        data._projectDetailsArray,
        currency,
        exchangeRate,
        data.currency_details // Pass the currency_details to ensure consistent handling
      );

      // Process data as before
      const processedData = {
        ...data,
        supplier,
        buyer,
        projTitle,
        tax_info: taxInfo,
        payment_info: paymentInfo,
        projectCustomFields: projectCustomFields, // Add project custom fields for mapper.js
        currency_info: {
          currency: data.currency_details?.data?.currencyCode || currency, // Prioritize currencyCode from currency_details
          isForeignCurrency: isForeignCurrency,
          exchangeRate: exchangeRate,
          taxCurrency: 'MYR', // Tax is always in MYR
          currencyCode: data.currency_details?.data?.currencyCode || currency, // Use currency code from currency_details if available
          currencyDetails: data.currency_details?.data || null, // Include the full currency details for reference
          rawCurrency: currency // Store the original currency from raw invoice for reference
        },
        invoice: {
          EinvoiceTypeCode: '01',
          number: data._rawInvoice?.invoiceNumber || '',
          date: moment(data._rawInvoice?.date || currentDate).format('YYYY-MM-DD'),
          issueDate: [{ _: formattedDate }],
          issueTime: [{ _: formattedTime }],
          currency: currency,
          taxCurrency: 'MYR', // Tax is always in MYR
          version: data._rawInvoice?.customFields?.find(f =>
            f.label === 'Invoice Version')?.value || '1.0',
          amount: isForeignCurrency ? {
            // For foreign currency: BQE amounts are in MYR, convert to foreign currency (USD)
            total: Math.round((data._rawInvoice?.invoiceAmount || 0) * exchangeRate * 100) / 100, // Convert MYR to foreign currency
            taxable: Math.round((data._rawInvoice?.serviceAmount || 0) * exchangeRate * 100) / 100, // Convert MYR to foreign currency
            tax: 0 // Tax is always in MYR for foreign currency invoices
          } : {
            // For local currency: use original amounts as-is
            total: data._rawInvoice?.invoiceAmount || 0,
            taxable: data._rawInvoice?.serviceAmount || 0,
            tax: data._rawInvoice?.mainServiceTax || 0
          },
          // Add MYR amounts for foreign currency (these are the original amounts from BQE)
          amountMYR: isForeignCurrency ? {
            total: data._rawInvoice?.invoiceAmount || 0, // Original MYR amounts from BQE
            taxable: data._rawInvoice?.serviceAmount || 0, // Original MYR amounts from BQE
            tax: data._rawInvoice?.mainServiceTax || 0 // Original MYR amounts from BQE
          } : undefined,
          messageOnInvoice: data._rawInvoice?.messageOnInvoice || '',
          invoiceFrom: data._rawInvoice?.date ? moment(data._rawInvoice.date).format('YYYY-MM-DD') : formattedDate,
          invoiceTo: data._rawInvoice?.dueDate ? moment(data._rawInvoice.dueDate).format('YYYY-MM-DD') : formattedDate
        },
        line_items: lineItems,
        attention: data._clientDetails?.attention ||
        (data._clientDetails?.firstName && data._clientDetails?.lastName ?
          `${data._clientDetails.firstName} ${data._clientDetails.lastName}`.trim() :
          data._clientDetails?.name) || ''
      };

      // Ensure the raw invoice has all necessary fields for the mapper
      if (!processedData._rawInvoice.issuanceDate) {
        processedData._rawInvoice.issuanceDate = formattedDate;
      }

      if (!processedData._rawInvoice.issuanceTime) {
        processedData._rawInvoice.issuanceTime = formattedTime;
      }

      // Create final log entry
      const finalLog = {
        stage: 'processed',
        timestamp: moment().format(),
        processedData,
        validation: {
          hasSupplier: !!processedData.supplier,
          hasBuyer: !!processedData.buyer,
          hasTaxInfo: !!processedData.tax_info,
          hasInvoice: !!processedData.invoice,
          hasLineItems: processedData.line_items && processedData.line_items.length > 0
        }
      };

      // Write processing log
      await this.logger.writeProcessingLog(
        processedData.invoice.number,
        {
          initial: initialLog,
          final: finalLog
        }
      );

      // Add attention to the processed data
      const attention = data._clientDetails?.attention ||
        (data._clientDetails?.firstName && data._clientDetails?.lastName ?
          `${data._clientDetails.firstName} ${data._clientDetails.lastName}`.trim() :
          data._clientDetails?.name) ||
        '';
      processedData.attention = attention;

      // Log the processed data for debugging
      console.log('Processed data before returning:', {
        hasRawInvoice: !!processedData._rawInvoice,
        rawInvoiceNumber: processedData._rawInvoice?.invoiceNumber,
        hasSupplier: !!processedData.supplier,
        hasBuyer: !!processedData.buyer,
        hasTaxInfo: !!processedData.tax_info,
        taxInfo: processedData.tax_info,
        hasLineItems: processedData.line_items && processedData.line_items.length > 0,
        lineItemsCount: processedData.line_items?.length || 0,
        currency: processedData.currency_info?.currency,
        isForeignCurrency: processedData.currency_info?.isForeignCurrency,
        exchangeRate: processedData.currency_info?.exchangeRate,
        // Add currency conversion debugging
        invoiceAmounts: {
          rawMYR: {
            total: data._rawInvoice?.invoiceAmount,
            taxable: data._rawInvoice?.serviceAmount,
            tax: data._rawInvoice?.mainServiceTax
          },
          convertedUSD: processedData.invoice?.amount,
          amountMYR: processedData.invoice?.amountMYR
        }
      });

      return processedData;

    } catch (error) {
      console.error('Error in processRawBQEData:', error);

      // Log error
      await this.logger.writeProcessingLog(
        data._rawInvoice?.invoiceNumber || 'unknown',
        {
          stage: 'error',
          timestamp: moment().format(),
          error: {
            message: error.message,
            stack: error.stack
          },
          rawData: data
        }
      );

      throw error;
    }
  }
}

module.exports = BQEDataProcessor;