const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

async function setupCertificates() {
  try {
    console.log('Setting up certificates...');
    
    const certPath = process.env.PRIVATE_CERT_FILE_PATH;
    const keyPath = process.env.PRIVATE_KEY_FILE_PATH;

    if (!certPath || !keyPath) {
      throw new Error('Certificate paths not configured in environment variables');
    }

    // Check if certificates exist
    try {
      await fs.access(certPath);
      await fs.access(keyPath);
      console.log('✓ Certificates found and accessible');
    } catch (error) {
      console.error('✗ Error accessing certificates:', error.message);
      process.exit(1);
    }

    console.log('Certificate setup complete');
  } catch (error) {
    console.error('Certificate setup failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  setupCertificates();
}

module.exports = setupCertificates; 

// require('dotenv').config();
// const fs = require('fs');
// const path = require('path');

// async function setupCertificates() {
//   const certDir = path.join(process.cwd(), 'certificates');
  
//   // Create certificates directory if it doesn't exist
//   if (!fs.existsSync(certDir)) {
//     fs.mkdirSync(certDir, { recursive: true });
//     console.log('Created certificates directory');
//   }

//   // Define certificate files
//   const certFiles = [
//     {
//       path: process.env.PRIVATE_KEY_FILE_PATH,
//       type: 'Private Key',
//       template: '-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----'
//     },
//     {
//       path: process.env.PRIVATE_CERT_FILE_PATH,
//       type: 'Digital Certificate',
//       template: '(Binary P12 file)'
//     }
//   ];

//   // Check each certificate file
//   certFiles.forEach(cert => {
//     const fullPath = path.resolve(cert.path);
//     console.log(`\nChecking ${cert.type}:`);
//     console.log(`Path: ${fullPath}`);
    
//     if (fs.existsSync(fullPath)) {
//       console.log(`✓ ${cert.type} exists`);
//     } else {
//       console.log(`✗ ${cert.type} is missing`);
//       console.log(`Template format should be:\n${cert.template}`);
//     }
//   });

//   console.log('\nNote: You need to manually copy your certificate files to these locations');
// }

// setupCertificates().catch(console.error); 