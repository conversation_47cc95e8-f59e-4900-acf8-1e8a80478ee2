const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const QRCode = require('qrcode');
const invoiceConfig = require('../../config/invoice.config');

router.post('/generate-report', async (req, res) => {
  const { uuid, longId } = req.body;

  if (!jsreportInstance) {
    return res.status(500).json({
      success: false,
      message: 'PDF service not initialized'
    });
  }

  try {
    const rawResponse = await axios.get(
      `https://preprod-api.myinvois.hasil.gov.my/api/v1.0/documents/${uuid}/raw`,
      {
        headers: { 'Authorization': `Bearer ${req.session.accessToken}` }
      }
    );

    if (!rawResponse.data) {
      throw new Error('Failed to fetch document data');
    }

    const rawData = rawResponse.data;

    const qrUrl = `https://preprod.myinvois.hasil.gov.my/${uuid}/share/${longId}`;
    const qrCodeDataUrl = qrUrl;

    const documentHash = await getPdfHash(rawData);
    const safeInvoiceId = rawData.internalId.replace(/[\/\\:*?"<>|]/g, '_');
    const fileName = `${safeInvoiceId}-${documentHash}-invoice.pdf`;
    const filePath = path.join(__dirname, '../../pdf', fileName);

    const parsedDoc = JSON.parse(rawData.document);
    const invoice = parsedDoc.Invoice[0];


    const templateData = {
      // Header Info
      DigitalSignature: documentHash,

      // Supplier Info
      SupplierName: invoice.AccountingSupplierParty[0].Party[0].PartyLegalEntity[0].RegistrationName[0]._,
      SupplierAddress: invoice.AccountingSupplierParty[0].Party[0].PostalAddress[0].AddressLine
        .map(line => line.Line[0]._)
        .filter(Boolean)
        .join('\n'),
      SupplierContactNo: invoice.AccountingSupplierParty[0].Party[0].Contact?.[0]?.Telephone?.[0]._ || 'NA',
      SupplierEmail: invoice.AccountingSupplierParty[0].Party[0].Contact?.[0]?.ElectronicMail?.[0]._ || 'NA',
      SupplierTourismTaxNo: invoice.AccountingSupplierParty[0].Party[0].PartyIdentification
        .find(id => id.ID[0].schemeID === 'TTX')?.ID[0]._ || 'NA',
      SupplierMSICCode: invoice.AccountingSupplierParty[0].Party[0].IndustryClassificationCode?.[0]._ || 'NA',
      SupplierBusinessActivity: invoice.AccountingSupplierParty[0].Party[0].IndustryClassificationCode?.[0]?.name || 'NA',
      SupplierRegistrationNo: invoice.AccountingSupplierParty[0].Party[0].PartyIdentification
        .find(id => id.ID[0].schemeID === 'BRN')?.ID[0]._ || 'NA',
      SupplierSSTNo: invoice.AccountingSupplierParty[0].Party[0].PartyIdentification
        .find(id => id.ID[0].schemeID === 'SST')?.ID[0]._ || 'NA',
      SupplierTIN: rawData.issuerTin,

      // Buyer Info
      BuyerName: invoice.AccountingCustomerParty[0].Party[0].PartyLegalEntity[0].RegistrationName[0]._,
      BuyerAddress: invoice.AccountingCustomerParty[0].Party[0].PostalAddress[0].AddressLine
        .map(line => line.Line[0]._)
        .filter(Boolean)
        .join('\n'),
      BuyerContactNo: invoice.AccountingCustomerParty[0].Party[0].Contact?.[0]?.Telephone?.[0]._ || 'NA',
      BuyerEmail: invoice.AccountingCustomerParty[0].Party[0].Contact?.[0]?.ElectronicMail?.[0]._ || 'NA',
      BuyerMSICCode: invoice.AccountingCustomerParty[0].Party[0].IndustryClassificationCode?.[0]._ || 'NA',
      BuyerRegistrationNo: invoice.AccountingCustomerParty[0].Party[0].PartyIdentification
        .find(id => id.ID[0].schemeID === 'BRN')?.ID[0]._ || 'NA',
      BuyerSSTNo: invoice.AccountingCustomerParty[0].Party[0].PartyIdentification
        .find(id => id.ID[0].schemeID === 'SST')?.ID[0]._ || 'NA',
      BuyerTIN: rawData.receiverId,
      BuyerAttention: invoiceConfig.defaultAttention || 'NA',

      // Invoice Details
      InvoiceType: rawData.typeName,
      InvoiceVersion: invoice.InvoiceTypeCode[0].listVersionID || '1.0',
      InvoiceNumber: rawData.internalId,
      OriginalInvoiceRefNo: invoice.OriginalInvoiceReference?.[0]?.ID?.[0]._ || 'NA',
      InvoiceDate: new Date(invoice.IssueDate[0]._).toLocaleDateString(),
      InvoiceCurrency: invoice.DocumentCurrencyCode?.[0]._ || 'MYR',
      CurrencyExchangeRate: invoice.PricingExchangeRate?.[0]?.CalculationRate?.[0]._ || '1.0',
      ProjectTitle: invoiceConfig.projectTitle || 'NA',

      // Line Items
      items: invoice.InvoiceLine.map(line => ({
        Classification: line.Item[0].CommodityClassification?.[0]?.ItemClassificationCode?.[0]._ || 'NA',
        Description: line.Item[0].Description[0]._,
        UnitPrice: formatCurrency(line.Price[0].PriceAmount[0]._),
        Amount: formatCurrency(line.LineExtensionAmount[0]._)
      })),

      // Tax Details
      TaxExemptionDetails: invoice.TaxTotal?.[0]?.TaxSubtotal?.[0]?.TaxCategory?.[0]?.TaxExemptionReason?.[0]._ || 'NA',
      AmountTaxExemption: formatCurrency(invoice.TaxTotal?.[0]?.TaxSubtotal?.[0]?.TaxAmount?.[0]._ || 0),
      TaxType: invoice.TaxTotal?.[0]?.TaxSubtotal?.[0]?.TaxCategory?.[0]?.ID?.[0]._ || 'NA',
      TaxRate: invoice.TaxTotal?.[0]?.TaxSubtotal?.[0]?.TaxCategory?.[0]?.Percent?.[0]._ || '0',

      // Summary
      TotalExcludingTax: formatCurrency(rawData.totalExcludingTax),
      TaxAmount: formatCurrency(rawData.totalPayableAmount - rawData.totalExcludingTax),
      TotalIncludingTax: formatCurrency(rawData.totalPayableAmount),
      TotalPayableAmount: formatCurrency(rawData.totalPayableAmount),

      // Payment Info
      PaymentTitle: invoiceConfig.paymentTitle,
      PaymentDetails: invoiceConfig.paymentDetails
    };

    const template = {
      content: fs.readFileSync(path.join(__dirname, '../src/reports/invoice-template.html'), 'utf8'),
      engine: 'jsrender',
      recipe: 'chrome-pdf',
      chrome: {
        format: 'A4',
        printBackground: true,
        marginTop: '1cm',
        marginRight: '1cm',
        marginBottom: '1cm',
        marginLeft: '1cm'
      }
    };

    const result = await jsreportInstance.render({
      template,
      data: templateData,
      qrCodeDataUrl: qrUrl
    });

    if (!result || !result.content) {
      throw new Error('PDF generation failed - no content received');
    }

    const pdfDir = path.join(__dirname, '../../pdf');
    if (!fs.existsSync(pdfDir)) {
      fs.mkdirSync(pdfDir, { recursive: true });
    }

    await fs.promises.writeFile(filePath, result.content);
    console.log('PDF file written successfully:', filePath);

    return res.json({
      success: true,
      url: `/pdf/${fileName}`,
      fileName: fileName,
      contentType: 'application/pdf'
    });

  } catch (error) {
    console.error('PDF generation error:', error);
    return res.status(500).json({
      success: false,
      message: 'PDF generation failed',
      error: error.message
    });
  }
});

router.get('/list', (req, res) => {
  const pdfDir = path.join(__dirname, '../../pdf');
  
  fs.readdir(pdfDir, (err, files) => {
    if (err) {
      console.error(`Error reading directory: ${err.message}`);
      return res.status(500).send('Error reading directory');
    }

    const pdfFiles = files.filter(file => file.endsWith('.pdf'));
    res.json(pdfFiles);
  });
});

router.get('/view/:fileName', (req, res) => {
  const fileName = req.params.fileName;
  const filePath = path.join(__dirname, '../../pdf', fileName);

  if (fs.existsSync(filePath)){
    res.setHeader('Content-Type', 'application/pdf');
    fs.createReadStream(filePath).pipe(res);
  } else {
    res.status(400).send('Invoice not found');
  }
});

module.exports = router; 